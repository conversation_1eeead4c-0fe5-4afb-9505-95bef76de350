import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { X } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import Input from './Input';
import Button from './Button';
import ImagePicker from './ImagePicker';
import EnhancedDropdown from './EnhancedDropdown';

interface InactiveStatusModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: {
    reason: string;
    notes?: string;
    image?: string;
    cascadeToPlants?: boolean;
  }) => Promise<void>;
  entityType: 'plant' | 'animal' | 'equipment' | 'garden' | 'field';
  showCascadeOption?: boolean;
}

const InactiveStatusModal: React.FC<InactiveStatusModalProps> = ({
  visible,
  onClose,
  onSubmit,
  entityType,
  showCascadeOption = false,
}) => {
  const [reason, setReason] = useState('');
  const [notes, setNotes] = useState('');
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [cascadeToPlants, setCascadeToPlants] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Get reason options based on entity type
  const getReasonOptions = () => {
    switch (entityType) {
      case 'plant':
        return [
          { label: 'Harvested', value: 'harvested' },
          { label: 'Died', value: 'died' },
          { label: 'Diseased', value: 'diseased' },
          { label: 'Removed', value: 'removed' },
          { label: 'Transplanted', value: 'transplanted' },
          { label: 'Other', value: 'other' },
        ];
      case 'animal':
        return [
          { label: 'Sold', value: 'sold' },
          { label: 'Died', value: 'died' },
          { label: 'Slaughtered', value: 'slaughtered' },
          { label: 'Transferred', value: 'transferred' },
          { label: 'Lost', value: 'lost' },
          { label: 'Other', value: 'other' },
        ];
      case 'equipment':
        return [
          { label: 'Broken', value: 'broken' },
          { label: 'Sold', value: 'sold' },
          { label: 'Replaced', value: 'replaced' },
          { label: 'Maintenance', value: 'maintenance' },
          { label: 'Lost', value: 'lost' },
          { label: 'Other', value: 'other' },
        ];
      case 'garden':
      case 'field':
        return [
          { label: 'Fallow', value: 'fallow' },
          { label: 'Sold', value: 'sold' },
          { label: 'Repurposed', value: 'repurposed' },
          { label: 'Seasonal Closure', value: 'seasonal_closure' },
          { label: 'Maintenance', value: 'maintenance' },
          { label: 'Other', value: 'other' },
        ];
      default:
        return [
          { label: 'Other', value: 'other' },
        ];
    }
  };
  
  const handleSubmit = async () => {
    if (!reason) {
      Alert.alert('Error', 'Please select a reason');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      await onSubmit({
        reason,
        notes,
        image: imageUri || undefined,
        cascadeToPlants: showCascadeOption ? cascadeToPlants : undefined,
      });
      
      // Reset form
      setReason('');
      setNotes('');
      setImageUri(null);
      setCascadeToPlants(true);
      
      onClose();
    } catch (error) {
      console.error('Error submitting inactive status:', error);
      Alert.alert('Error', 'Failed to mark as inactive. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleClose = () => {
    // Reset form
    setReason('');
    setNotes('');
    setImageUri(null);
    setCascadeToPlants(true);
    
    onClose();
  };
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Mark as Inactive</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={24} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalBody}>
            <Text style={styles.sectionTitle}>
              Why are you marking this {entityType} as inactive?
            </Text>
            
            <EnhancedDropdown
              label="Reason"
              placeholder="Select a reason"
              options={getReasonOptions()}
              value={reason}
              onChange={(value) => setReason(value as string)}
              zIndex={5000}
            />
            
            <Input
              label="Notes (Optional)"
              placeholder="Add any additional details"
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
            />
            
            <Text style={styles.label}>Photo (Optional)</Text>
            <ImagePicker
              image={imageUri || ''}
              onImageSelected={setImageUri}
              placeholder="Add a photo"
              size={200}
            />
            
            {showCascadeOption && (
              <View style={styles.cascadeContainer}>
                <Text style={styles.label}>Additional Options</Text>
                <TouchableOpacity 
                  style={styles.checkboxRow}
                  onPress={() => setCascadeToPlants(!cascadeToPlants)}
                >
                  <View style={[
                    styles.checkbox,
                    cascadeToPlants && styles.checkboxChecked
                  ]}>
                    {cascadeToPlants && (
                      <View style={styles.checkboxInner} />
                    )}
                  </View>
                  <Text style={styles.checkboxLabel}>
                    Also mark all plants in this {entityType} as inactive
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>
          
          <View style={styles.modalFooter}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleClose}
              style={styles.cancelButton}
            />
            <Button
              title={isSubmitting ? "Submitting..." : "Mark as Inactive"}
              onPress={handleSubmit}
              style={styles.submitButton}
              disabled={isSubmitting || !reason}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  closeButton: {
    padding: 4,
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
    marginTop: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  cascadeContainer: {
    marginTop: 16,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.primary,
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
  },
  checkboxInner: {
    width: 10,
    height: 10,
    backgroundColor: colors.white,
    borderRadius: 2,
  },
  checkboxLabel: {
    fontSize: 14,
    color: colors.gray[700],
    flex: 1,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  submitButton: {
    flex: 1,
    marginLeft: 8,
  },
});

export default InactiveStatusModal;
