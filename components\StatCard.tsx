import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/colors';
import { 
  Map, 
  ListTodo, 
  Clock, 
  CheckCircle,
  BarChart3,
  Users,
  Tractor,
  Leaf,
  Rabbit,
  Wheat,
  TreeDeciduous
} from 'lucide-react-native';

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: string;
  color?: string;
  style?: ViewStyle;
  onPress?: () => void;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color = colors.primary,
  style,
  onPress,
}) => {
  const renderIcon = () => {
    switch (icon) {
      case 'map':
        return <Map size={20} color={color} />;
      case 'list-todo':
        return <ListTodo size={20} color={color} />;
      case 'clock':
        return <Clock size={20} color={color} />;
      case 'check-circle':
        return <CheckCircle size={20} color={color} />;
      case 'bar-chart':
        return <BarChart3 size={20} color={color} />;
      case 'users':
        return <Users size={20} color={color} />;
      case 'tractor':
        return <Tractor size={20} color={color} />;
      case 'leaf':
        return <Leaf size={20} color={color} />;
      case 'rabbit':
        return <Rabbit size={20} color={color} />;
      case 'wheat':
        return <Wheat size={20} color={color} />;
      case 'tree':
        return <TreeDeciduous size={20} color={color} />;
      default:
        return null;
    }
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent 
      style={[styles.card, style]} 
      onPress={onPress}
    >
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <Text style={[styles.value, { color }]}>{value}</Text>
      </View>
      {icon && (
        <View style={[styles.iconContainer, { backgroundColor: color + '15' }]}>
          {renderIcon()}
        </View>
      )}
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 12,
    width: '48%',
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  value: {
    fontSize: 24,
    fontWeight: '700',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
});

export default StatCard;