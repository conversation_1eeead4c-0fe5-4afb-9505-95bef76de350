import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import {
  Calendar,
  ChevronDown,
  FileText,
  Check,
  BarChart3,
  PieChart,
  LineChart,
  Users,
  Tractor,
  Leaf,
  Clipboard
} from 'lucide-react-native';

const REPORT_TYPES = [
  { id: 'daily', name: 'Daily Operations', icon: <FileText size={20} color={colors.primary} /> },
  { id: 'weekly', name: 'Weekly Summary', icon: <BarChart3 size={20} color={colors.primary} /> },
  { id: 'inventory', name: 'Inventory Status', icon: <PieChart size={20} color={colors.primary} /> },
  { id: 'staff', name: 'Staff Performance', icon: <Users size={20} color={colors.primary} /> },
  { id: 'equipment', name: 'Equipment Usage', icon: <Tractor size={20} color={colors.primary} /> },
  { id: 'crop', name: 'Crop Health', icon: <Leaf size={20} color={colors.primary} /> },
];

export default function CreateReportScreen() {
  const { currentFarm, generateInventoryStatusReport, getDailyReport } = useFarmStore();
  const { user } = useAuthStore();

  const [title, setTitle] = useState('');
  const [reportType, setReportType] = useState('');
  const [reportTypeName, setReportTypeName] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const [showReportTypeModal, setShowReportTypeModal] = useState(false);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  useEffect(() => {
    // Set default title based on report type
    if (reportType) {
      const selectedType = REPORT_TYPES.find(type => type.id === reportType);
      if (selectedType) {
        setTitle(`${selectedType.name} - ${formatDate(new Date())}`);
      }
    }
  }, [reportType]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleCreateReport = async () => {
    if (!title) {
      Alert.alert('Error', 'Please enter a report title');
      return;
    }

    if (!reportType) {
      Alert.alert('Error', 'Please select a report type');
      return;
    }

    try {
      setIsLoading(true);


      /////////////
      let res = ''
      switch (reportType) {
        case 'daily':
          res = await getDailyReport(currentFarm.id, startDate,title)
          router.push('/report-templates/daily-operations?id='+res);
          break;
        case 'weekly':
          router.push('/report-templates/weekly-summary');
          break;
        case 'inventory':

          res = await generateInventoryStatusReport(currentFarm.id,title)
          router.push('/report-templates/inventory-status?id=' + res);
          break;
        case 'staff':
          router.push('/report-templates/staff-performance');
          break;
        default:
          router.back();
      }
      // In a real app, this would save to Firestore
      // For now, we'll just simulate a delay and navigate
      // setTimeout(() => {
      //   Alert.alert('Success', 'Report created successfully', [
      //     {
      //       text: 'OK',
      //       onPress: async () => {
      //         // Navigate to the appropriate report template
      //         const res = ''
      //         switch (reportType) {
      //           case 'daily':
      //             res = getDailyReport(currentFarm.id, startDate)
      //             // router.push('/report-templates/daily-operations');
      //             break;
      //           case 'weekly':
      //             router.push('/report-templates/weekly-summary');
      //             break;
      //           case 'inventory':

      //             res = await generateInventoryStatusReport(currentFarm.id)
      //             // router.push('/report-templates/inventory-status');
      //             break;
      //           case 'staff':
      //             router.push('/report-templates/staff-performance');
      //             break;
      //           default:
      //             router.back();
      //         }
      //       }
      //     }
      //   ]);
      //   setIsLoading(false);
      // }, 1500);

    } catch (error) {
      console.error('Error creating report:', error);
      Alert.alert('Error', 'Failed to create report. Please try again.');
      setIsLoading(false);
    }
  };

  const handleDateChange = (date: Date, setter: React.Dispatch<React.SetStateAction<Date>>, closeModal: () => void) => {
    setter(date);
    closeModal();
  };

  const renderDatePickerModal = (
    visible: boolean,
    onClose: () => void,
    onSelect: (date: Date) => void,
    currentDate: Date,
    title: string
  ) => {
    if (!visible) return null;

    const today = new Date();
    const dates = [];

    // Generate dates for the past 30 days and next 30 days
    for (let i = -30; i <= 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    return (
      <Modal
        visible={visible}
        transparent={true}
        animationType="slide"
        onRequestClose={onClose}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{title}</Text>
              <TouchableOpacity onPress={onClose}>
                <Text style={styles.modalCloseText}>Close</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={dates}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.dateItem}
                  onPress={() => onSelect(item)}
                >
                  <Text style={styles.dateItemText}>
                    {formatDate(item)}
                  </Text>
                  {item.toDateString() === currentDate.toDateString() && (
                    <Check size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              )}
              keyExtractor={(item) => item.toISOString()}
            />
          </View>
        </View>
      </Modal>
    );
  };

  const renderReportTypeItem = ({ item }: { item: typeof REPORT_TYPES[0] }) => (
    <TouchableOpacity
      style={styles.reportTypeItem}
      onPress={() => {
        setReportType(item.id);
        setReportTypeName(item.name);
        setShowReportTypeModal(false);
      }}
    >
      <View style={styles.reportTypeItemContent}>
        {item.icon}
        <Text style={styles.reportTypeItemText}>{item.name}</Text>
      </View>
      {reportType === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Create New Report',
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.formContainer}>
            <Input
              label="Report Title"
              placeholder="Enter report title"
              value={title}
              onChangeText={setTitle}
              containerStyle={styles.inputContainer}
              leftIcon={<FileText size={20} color={colors.gray[500]} />}
            />

            <Text style={styles.label}>Report Type</Text>
            <TouchableOpacity
              style={styles.selectContainer}
              onPress={() => setShowReportTypeModal(true)}
            >
              <View style={styles.selectIcon}>
                {reportType ? (
                  REPORT_TYPES.find(type => type.id === reportType)?.icon ||
                  <FileText size={20} color={colors.gray[500]} />
                ) : (
                  <FileText size={20} color={colors.gray[500]} />
                )}
              </View>
              <Text style={styles.selectText}>
                {reportTypeName || "Select Report Type"}
              </Text>
              <ChevronDown size={20} color={colors.gray[500]} />
            </TouchableOpacity>

            <Text style={styles.label}>Date Range</Text>
            <View style={styles.dateRangeContainer}>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Calendar size={20} color={colors.gray[500]} style={styles.dateIcon} />
                <Text style={styles.dateText}>
                  {formatDate(startDate)}
                </Text>
              </TouchableOpacity>

              <Text style={styles.dateRangeSeparator}>to</Text>

              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Calendar size={20} color={colors.gray[500]} style={styles.dateIcon} />
                <Text style={styles.dateText}>
                  {formatDate(endDate)}
                </Text>
              </TouchableOpacity>
            </View>

            <Input
              label="Notes (Optional)"
              placeholder="Enter any additional notes or instructions"
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
              leftIcon={<Clipboard size={20} color={colors.gray[500]} />}
            />

            <View style={styles.infoContainer}>
              <Text style={styles.infoText}>
                This report will include data from {currentFarm?.name || 'your farm'} for the selected date range.
              </Text>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? "Creating..." : "Generate Report"}
              onPress={handleCreateReport}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
            />
          </View>
        </ScrollView>

        {/* Date Picker Modals */}
        {renderDatePickerModal(
          showStartDatePicker,
          () => setShowStartDatePicker(false),
          (date) => handleDateChange(date, setStartDate, () => setShowStartDatePicker(false)),
          startDate,
          "Select Start Date"
        )}

        {renderDatePickerModal(
          showEndDatePicker,
          () => setShowEndDatePicker(false),
          (date) => handleDateChange(date, setEndDate, () => setShowEndDatePicker(false)),
          endDate,
          "Select End Date"
        )}

        {/* Report Type Modal */}
        <Modal
          visible={showReportTypeModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowReportTypeModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Report Type</Text>
                <TouchableOpacity onPress={() => setShowReportTypeModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              <FlatList
                data={REPORT_TYPES}
                renderItem={renderReportTypeItem}
                keyExtractor={item => item.id}
                style={styles.modalList}
              />
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  selectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 16,
  },
  selectIcon: {
    marginRight: 8,
  },
  selectText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  datePickerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dateRangeSeparator: {
    marginHorizontal: 8,
    color: colors.gray[500],
  },
  dateIcon: {
    marginRight: 8,
  },
  dateText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  infoContainer: {
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  infoText: {
    fontSize: 14,
    color: colors.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  reportTypeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  reportTypeItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportTypeItemText: {
    fontSize: 16,
    color: colors.gray[800],
    marginLeft: 12,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dateItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
});