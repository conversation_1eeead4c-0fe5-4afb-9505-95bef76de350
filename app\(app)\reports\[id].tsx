import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ActivityIndicator, ScrollView } from 'react-native';
import { Stack, useLocalSearchParams } from 'expo-router';
import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '@/firebase/config'; // Assuming your firestore instance is exported from here
import { colors } from '@/constants/colors';
import { Calendar, User, Info } from 'lucide-react-native';

export default function ReportDetailScreen() {
  const { id } = useLocalSearchParams();
  const [report, setReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReport = async () => {
      if (!id || typeof id !== 'string') {
        setError('Report ID is missing or invalid.');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const reportDocRef = doc(firestore, 'reports', id);
        const reportSnapshot = await getDoc(reportDocRef);

        if (reportSnapshot.exists()) {
          const data = reportSnapshot.data();
          setReport({
            id: reportSnapshot.id,
            ...data,
            // Convert Firestore Timestamp to a readable date string
            date: data.date?.toDate ? new Date(data.date.toDate()).toLocaleDateString() : data.date,
          });
        } else {
          setError('Report not found.');
        }
      } catch (err) {
        console.error('Error fetching report:', err);
        setError('Failed to load report. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchReport();
  }, [id]);

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading report...</Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Info size={48} color={colors.danger} />
        <Text style={styles.errorText}>{error}</Text>
      </SafeAreaView>
    );
  }

  if (!report) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Info size={48} color={colors.gray[500]} />
        <Text style={styles.errorText}>No report data available.</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: report.title || 'Report Details' }} />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.card}>
          <Text style={styles.title}>{report.title}</Text>
          <View style={styles.metaContainer}>
            <Calendar size={16} color={colors.gray[600]} />
            <Text style={styles.metaText}>{report.date}</Text>
            <User size={16} color={colors.gray[600]} style={styles.metaIcon} />
            <Text style={styles.metaText}>{report.createdBy}</Text>
          </View>
          <Text style={styles.description}>{report.description || 'No description provided.'}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.gray[700],
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.gray[700],
    textAlign: 'center',
  },
  scrollContent: {
    padding: 16,
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 10,
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  metaText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 5,
  },
  metaIcon: {
    marginLeft: 15,
  },
  description: {
    fontSize: 16,
    color: colors.gray[700],
    lineHeight: 24,
    marginBottom: 20,
  },
});