// import React, { useCallback, useMemo, useRef, useState, useEffect } from 'react';
// import {
//   View,
//   Text,
//   TouchableOpacity,
//   FlatList,
//   StyleSheet,
// } from 'react-native';
// import BottomSheet from '@gorhom/bottom-sheet';
// import colors from '@/constants/colors';

// interface DropdownBottomSheetProps {
//   label: string;
//   options: string[];
//   selectedValue?: string | string[];
//   isMultiple?: boolean;
//   onSelect: (value: string | string[]) => void;
// }

// const DropdownBottomSheet: React.FC<DropdownBottomSheetProps> = ({
//   label,
//   options,
//   selectedValue,
//   isMultiple = false,
//   onSelect,
// }) => {
//   const bottomSheetRef = useRef<BottomSheet>(null);
//   const snapPoints = useMemo(() => ['40%', '70%'], []);

//   const [selected, setSelected] = useState<string[]>([]);

//   // Initialize selected state from prop
//   useEffect(() => {
//     if (selectedValue) {
//       setSelected(Array.isArray(selectedValue) ? selectedValue : [selectedValue]);
//     }
//   }, [selectedValue]);

//   const openSheet = () => {
//   bottomSheetRef.current?.present();
// };
//   const handleOpen = useCallback(() => {
//     //   bottomSheetRef.current?.present();
//     bottomSheetRef.current?.expand();
//   }, []);

//   const handleToggleItem = (item: string) => {
//     if (isMultiple) {
//       const updated = selected.includes(item)
//         ? selected.filter((i) => i !== item)
//         : [...selected, item];
//       setSelected(updated);
//       onSelect(updated);
//     } else {
//       setSelected([item]);
//       onSelect(item);
//       bottomSheetRef.current?.close();
//     }
//   };

//   const isSelected = (item: string) => selected.includes(item);

//   const renderItem = ({ item }: { item: string }) => (
//     <TouchableOpacity
//       style={[
//         styles.optionItem,
//         isSelected(item) && styles.optionItemSelected,
//       ]}
//       onPress={() => handleToggleItem(item)}
//     >
//       <Text
//         style={[
//           styles.optionText,
//           isSelected(item) && styles.optionTextSelected,
//         ]}
//       >
//         {item}
//       </Text>
//     </TouchableOpacity>
//   );

//   const displayText = isMultiple
//     ? selected.length > 0
//       ? selected.join(', ')
//       : 'Select options'
//     : selected[0] || 'Select an option';

//   return (
//     <>
//       <Text style={styles.label}>{label}</Text>
//       <TouchableOpacity style={styles.dropdownButton} onPress={handleOpen}>
//         <Text style={styles.dropdownText}>{displayText}</Text>
//       </TouchableOpacity>

//       <BottomSheet
//         ref={bottomSheetRef}
//         index={-1}
//         snapPoints={snapPoints}
//         enablePanDownToClose
//       >
//         <View style={styles.sheetContent}>
//           <Text style={styles.sheetTitle}>Select {label}</Text>
//           <FlatList
//             data={options}
//             keyExtractor={(item) => item}
//             renderItem={renderItem}
//             showsVerticalScrollIndicator={false}
//           />
//         </View>
//       </BottomSheet>
//     </>
//   );
// };

// const styles = StyleSheet.create({
//   label: {
//     // fontSize: 16,
//     // marginBottom: 4,
//     fontSize: 14,
//         fontWeight: '500',
//         color: colors.gray[700],
//         marginBottom: 8,
//   },
//   dropdownButton: {
//     padding: 12,
//     borderWidth: 1,
//     borderColor: '#ccc',
//     borderRadius: 8,
//   },
//   dropdownText: {
//     // fontSize: 16,
//     // color: '#333',
//     fontSize: 14,
//         fontWeight: '500',
//         color: colors.gray[700],
//         marginBottom: 8,
//   },
//   sheetContent: {
//     flex: 1,
//     paddingHorizontal: 20,
//   },
//   sheetTitle: {
//     fontSize: 18,
//     fontWeight: '600',
//     marginBottom: 12,
//   },
//   optionItem: {
//     paddingVertical: 12,
//     borderBottomWidth: 1,
//     borderBottomColor: '#eee',
//   },
//   optionItemSelected: {
//     backgroundColor: '#e0f7e9',
//   },
//   optionText: {
//     fontSize: 16,
//   },
//   optionTextSelected: {
//     fontWeight: 'bold',
//     color: '#007a4d',
//   },
// });

// export default DropdownBottomSheet;

// components/DropdownBottomSheet.tsx
import React, { useCallback, useRef, useMemo, useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from 'react-native';
import {
  BottomSheetFlatList,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { Check } from 'lucide-react-native';

// type Option = {
//   label: string;
//   value: string;
// };

interface DropdownProps {
  label: string;
  options: Option[];
  onSelect: (selected: string[] | string) => void;
  selectedValue: string[] | string;
  isMultiple?: boolean;
}
type Option = {
  label: string;
  value: string | number;
};

type Props = {
  item: Option;
  selectedValue: string | number | (string | number)[];
  onSelect: (value: string | number) => void;
};
const RenderItem: React.FC<Props> = ({ item, selectedValue, onSelect }) => {
  const isSelected = Array.isArray(selectedValue)
    ? selectedValue.includes(item.value)
    : selectedValue === item.value;

  return (
    <TouchableOpacity
      style={[styles.itemContainer, isSelected && styles.itemSelected]}
      onPress={() => onSelect(item.value)}
    >
      {/* <Icon name="pets" size={24} color={isSelected ? 'green' : '#555'} /> */}
      <Text style={[styles.label, isSelected && { color: 'green' }]}>
        {item.label}
      </Text>
      {/* {isSelected && <Icon name="check" size={24} color="green" />} */}
    </TouchableOpacity>
  );
};
const DropdownBottomSheet: React.FC<DropdownProps> = ({
  label,
  options,
  onSelect,
  selectedValue,
  isMultiple = false,
}) => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['40%', '70%'], []);
  const [selected, setSelected] = useState<string[]>([]);

  useEffect(() => {
    if (Array.isArray(selectedValue)) {
      setSelected(selectedValue);
    } else if (typeof selectedValue === 'string') {
      setSelected([selectedValue]);
    }
  }, [selectedValue]);

  const openSheet = () => {
    bottomSheetRef.current?.present();
  };

  const handleOptionPress = (value: string) => {
    if (isMultiple) {
      setSelected((prev) =>
        prev.includes(value) ? prev.filter((v) => v !== value) : [...prev, value]
      );
    } else {
      onSelect(value);
      bottomSheetRef.current?.dismiss();
    }
  };

  const confirmSelection = () => {
    onSelect(selected);
    bottomSheetRef.current?.dismiss();
  };

  const renderOption = ({ item }: { item: Option }) => {
    const isSelected = selected.includes(item.value);
    return (
      <TouchableOpacity
        style={[styles.option, isSelected && styles.optionSelected]}
        onPress={() => handleOptionPress(item.value)}
      >
        <Text style={styles.optionLabel}>
          {item.label} {isSelected ? '✓' : ''}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <TouchableOpacity style={styles.input} onPress={openSheet}>
        <Text style={styles.inputLabel}>
          {isMultiple
            ? selected.map((val) => options.find((o) => o.value === val)?.label).join(', ') || label
            : options.find((o) => o.value === selected[0])?.label || label}
        </Text>
      </TouchableOpacity>

      <BottomSheetModal ref={bottomSheetRef} index={1}
        snapPoints={snapPoints}
        // index={1}
        // snapPoints={snapPoints}
        backgroundStyle={{ borderRadius: 20 }}
        style={{ zIndex: 9999 }}
      >
        <View style={styles.sheetContent}>
          <BottomSheetFlatList
            data={options}
            ismultiple={true}
            keyExtractor={(item) => item.value.toString()}
            // renderItem={({ item }) => (
            renderItem={({ item }) => (
              <RenderItem
                item={item}
                selectedValue={selectedValue}
                onSelect={(val)=>onSelect(val as any)}
              />
            )}
          // )}
          />
          {/* <BottomSheetScrollView>
          {options.map((item) => (
            <TouchableOpacity key={item.value} onPress={() => onSelect(item?.value)}>
              <Text>{item.label}</Text>
            </TouchableOpacity>
          ))}
        </BottomSheetScrollView> */}
        </View>
        {/* 
          <Text style={styles.sheetTitle}>{label}</Text>
          <FlatList
            data={options}
            keyExtractor={(item) => item.value}
            renderItem={renderOption}
          />
          {isMultiple && (
            <TouchableOpacity style={styles.confirmBtn} onPress={confirmSelection}>
              <Text style={styles.confirmText}>Confirm</Text>
            </TouchableOpacity>
          )}
        </View> */}
      </BottomSheetModal>
    </>
  );
};

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: '#999',
    padding: 12,
    borderRadius: 8,
    marginVertical: 10,
  },
  inputLabel: {
    fontSize: 16,
  },
  sheetContent: {
    padding: 16,
    flex: 1,
  },
  sheetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  option: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  optionSelected: {
    backgroundColor: '#e0f7fa',
  },
  optionLabel: {
    fontSize: 16,
  },
  confirmBtn: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#007aff',
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  itemContainer: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
  },
  itemSelected: {
    backgroundColor: '#e6ffe6',
  },
  label: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
});
export default DropdownBottomSheet