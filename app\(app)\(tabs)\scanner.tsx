import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  SafeAreaView,
  ScrollView,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { Scan, Camera, Upload, Info, QrCode, X, CheckCircle, AlertCircle, Flashlight } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useTranslation } from '@/i18n/useTranslation';
import Button from '@/components/Button';
import { parseUniversalLink } from '@/utils/qrcode';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { firestore } from '@/firebase/config';
import { addDoc, collection, serverTimestamp } from 'firebase/firestore';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';

export default function ScannerScreen() {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { setCurrentFarm, fetchFields, fetchGardens, fetchAnimals, fetchEquipment, fetchPlants } = useFarmStore();
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [scanning, setScanning] = useState(false);
  const [scanResult, setScanResult] = useState<{
    itemType: string;
    itemId: string;
    farmId: string;
  } | null>(null);
  const [torch, setTorch] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    if (!permission) {
      requestPermission();
    }
  }, [permission, requestPermission]);
  
  const handleOpenScanner = () => {
    // This function is now redundant as we're already on the scanner screen
    // But we'll keep it for compatibility
  };
  
  // Add this useEffect to reset scanning state when component mounts or unmounts
  useEffect(() => {
    // Reset scanning state when component mounts
    setScanning(false);
    setScanned(false);
    setIsLoading(false);
    setScanResult(null);
    
    // Clean up function to reset state when component unmounts
    return () => {
      setScanning(false);
      setScanned(false);
      setIsLoading(false);
      setScanResult(null);
    };
  }, []);

  const handleBarCodeScanned = async ({ data }) => {
    if (scanned || scanning) return;
    
    setScanning(true);
    
    try {
      // Parse the QR code data
      const result = parseUniversalLink(data);
      console.log("Parsed QR result:", result);
      
      if (result && result.farmId) {
        setIsLoading(true);
        setScanResult(result);
        setScanned(true);
        
        // Set the current farm and load its data
        await setCurrentFarm(result.farmId);
        
        // Fetch data for the specific farm
        await Promise.all([
          fetchFields(result.farmId),
          fetchGardens(result.farmId),
          fetchAnimals(result.farmId),
          fetchEquipment(result.farmId),
          fetchPlants(result.farmId)
        ]);
        
        setIsLoading(false);
        
        // Log the scan event to Firestore
        logScanEvent(result.itemType, result.itemId);
      } else {
        Alert.alert(
          t('scanner.invalidQR'),
          t('scanner.invalidQRMessage'),
          [{ text: t('scanner.tryAgain'), onPress: () => {
            setScanning(false);
            setScanned(false);
          }}]
        );
      }
    } catch (error) {
      console.error('QR code parsing error:', error);
      setIsLoading(false);
      Alert.alert(
        t('scanner.invalidQR'),
        t('scanner.invalidQRMessage'),
        [{ text: t('scanner.tryAgain'), onPress: () => {
          setScanning(false);
          setScanned(false);
        }}]
      );
    }
  };
  
  const logScanEvent = async (itemType, itemId) => {
    try {
      await addDoc(collection(firestore, 'scanLogs'), {
        userId: user?.uid,
        userRole: user?.role || 'unknown',
        itemType,
        itemId,
        timestamp: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error logging scan event:', error);
    }
  };
  
  const handleViewDetails = () => {
    if (!scanResult) return;
 
    const { itemType, itemId } = scanResult;
    
    // Show loading indicator while navigating
    setIsLoading(true);
    
    // Navigate to the appropriate details page based on item type
    switch (itemType) {
      case 'plant':
        router.push(`/plant/${itemId}`);
        break;
      case 'animal':
        router.push(`/animal/${itemId}`);
        break;
      case 'field':
        router.push(`/field/${itemId}`);
        break;
      case 'garden':
        router.push(`/garden/${itemId}`);
        break;
      case 'equipment':
        router.push(`/equipment/${itemId}`);
        break;
      case 'task':
        router.push(`/task/${itemId}`);
        break;
      default:
        // If itemType is not recognized, use the generic approach
        router.push(`/${itemType}/${itemId}`);
    }
     setIsLoading(false);
  };
  
  /**
   * Resets the scanner state and UI to allow scanning another QR code.
   * @return {void}
   */
  const handleScanAnother = () => {
    setScanned(false);
    setScanResult(null);
    setScanning(false);
    setIsLoading(false);
  };
  
  const handleClose = () => {
    router.back();
  };
  
  const handlePickImage = async () => {
    // Reset scanning state before picking an image
    setScanning(false);
    setScanned(false);
    setIsLoading(false);
    
    try {
      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          t('common.error'),
          t('scanner.permissionNeeded')
        );
        return;
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Process the image to scan for QR codes
        try {
          // We'll need to implement a different approach for scanning from images
          // since we're not using BarCodeScanner anymore
          Alert.alert(
            t('scanner.featureNotAvailable'),
            t('scanner.scanFromImageNotAvailable'),
            [{ text: 'OK', onPress: () => {
              // Reset scanning state after alert is dismissed
              setScanning(false);
              setScanned(false);
            }}]
          );
        } catch (error) {
          console.error('QR code scanning error:', error);
          Alert.alert(
            t('scanner.error'),
            t('scanner.scanningErrorMessage'),
            [{ text: 'OK', onPress: () => {
              // Reset scanning state after alert is dismissed
              setScanning(false);
              setScanned(false);
            }}]
          );
        }
      } else {
        // User canceled image picking, reset scanning state
        setScanning(false);
        setScanned(false);
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert(
        t('common.error'),
        t('scanner.imagePickerError'),
        [{ text: 'OK', onPress: () => {
          // Reset scanning state after alert is dismissed
          setScanning(false);
          setScanned(false);
        }}]
      );
    }
  };
  
  if (!permission) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen 
          options={{ 
            title: t('scanner.title'),
            headerLeft: () => (
              <TouchableOpacity onPress={handleClose}>
                <X size={24} color={colors.gray[800]} />
              </TouchableOpacity>
            ),
          }} 
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }
  
  if (!permission.granted) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen 
          options={{ 
            title: t('scanner.title'),
            headerLeft: () => (
              <TouchableOpacity onPress={handleClose}>
                <X size={24} color={colors.gray[800]} />
              </TouchableOpacity>
            ),
          }} 
        />
        <View style={styles.permissionContainer}>
          <AlertCircle size={60} color={colors.warning} style={styles.permissionIcon} />
          <Text style={styles.permissionText}>{t('scanner.permissionNeeded')}</Text>
          <Button
            title={t('scanner.grantPermission')}
            onPress={requestPermission}
            style={styles.permissionButton}
          />
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: t('scanner.title'),
          headerLeft: () => (
            <TouchableOpacity onPress={handleClose}>
              <X size={24} color={colors.gray[800]} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      {!scanned ? (
        <View style={styles.cameraContainer}>
          <CameraView
            style={styles.camera}
            facing="back"
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
            barcodeScannerSettings={{
              barcodeTypes: ['qr'],
            }}
            flashMode={torch ? 'torch' : 'off'}
          >
            <View style={styles.overlay}>
              {isLoading ? (
                <View style={styles.loadingOverlay}>
                  <ActivityIndicator size="large" color={colors.white} />
                  <Text style={styles.loadingText}>{t('scanner.loading')}</Text>
                </View>
              ) : (
                <>
                  <View style={styles.scannerFrame} />
                  
                  <Text style={styles.scannerText}>
                    {scanning ? t('scanner.processing') : t('scanner.alignQRCode')}
                  </Text>
                </>
              )}
              
              <View style={styles.controlsContainer}>
                <TouchableOpacity 
                  style={styles.controlButton}
                  onPress={() => setTorch(!torch)}
                >
                  <Flashlight size={24} color={colors.white} />
                  <Text style={styles.controlText}>{torch ? t('scanner.torchOff') : t('scanner.torchOn')}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.controlButton}
                  onPress={handleClose}
                >
                  <X size={24} color={colors.white} />
                  <Text style={styles.controlText}>{t('common.cancel')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </CameraView>
        </View>
      ) : (
        <View style={styles.resultContainer}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.loadingText}>{t('scanner.loadingData')}</Text>
            </View>
          ) : (
            <>
              <View style={styles.resultHeader}>
                <CheckCircle size={60} color={colors.success} style={styles.resultIcon} />
                <Text style={styles.resultTitle}>{t('scanner.scanSuccess')}</Text>
              </View>
              
              <View style={styles.resultDetails}>
                <View style={styles.resultItem}>
                  <Text style={styles.resultItemLabel}>{t('scanner.itemType')}</Text>
                  <Text style={styles.resultItemValue}>{scanResult?.itemType}</Text>
                </View>
                
                <View style={styles.resultItem}>
                  <Text style={styles.resultItemLabel}>{t('scanner.itemId')}</Text>
                  <Text style={styles.resultItemValue}>{scanResult?.itemId}</Text>
                </View>
                
                <View style={styles.resultItem}>
                  <Text style={styles.resultItemLabel}>{t('scanner.farmId')}</Text>
                  <Text style={styles.resultItemValue}>{scanResult?.farmId}</Text>
                </View>
              </View>
              
              <View style={styles.resultActions}>
                <Button
                  title={t('scanner.viewDetails')}
                  onPress={handleViewDetails}
                  style={styles.viewDetailsButton}
                />
                
                <Button
                  title={t('scanner.scanAnother')}
                  onPress={handleScanAnother}
                  style={styles.scanAnotherButton}
                  textStyle={styles.scanAnotherButtonText}
                  variant="outline"
                />
              </View>
            </>
          )}
        </View>
      )}
      
      {!scanned && Platform.OS !== 'web' && (
        <View style={styles.uploadButtonContainer}>
          <TouchableOpacity 
            style={styles.uploadButton}
            onPress={handlePickImage}
          >
            <Upload size={20} color={colors.white} style={styles.uploadIcon} />
            <Text style={styles.uploadText}>{t('scanner.uploadFromGallery')}</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: '100%',
    height: 200,
    marginBottom: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primaryLight,
    borderRadius: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 32,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 24,
  },
  scanButton: {
    marginBottom: 12,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  uploadButtonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
  uploadIcon: {
    marginRight: 8,
  },
  uploadText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionIcon: {
    marginBottom: 20,
  },
  permissionText: {
    fontSize: 16,
    color: colors.gray[700],
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    width: '100%',
    maxWidth: 300,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: colors.white,
    backgroundColor: 'transparent',
    marginBottom: 20,
  },
  scannerText: {
    color: colors.white,
    fontSize: 16,
    marginBottom: 40,
    textAlign: 'center',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 40,
  },
  controlButton: {
    alignItems: 'center',
    padding: 10,
  },
  controlText: {
    color: colors.white,
    marginTop: 8,
    fontSize: 14,
  },
  resultContainer: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  resultHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  resultIcon: {
    marginBottom: 16,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    textAlign: 'center',
  },
  resultDetails: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
  },
  resultItem: {
    marginBottom: 16,
  },
  resultItemLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  resultItemValue: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  resultActions: {
    gap: 12,
  },
  viewDetailsButton: {
    marginBottom: 12,
  },
  scanAnotherButton: {
    borderColor: colors.primary,
  },
  scanAnotherButtonText: {
    color: colors.primary,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.white,
    fontSize: 16,
    marginTop: 12,
  },
});




















