import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import MultipleImagePicker from '@/components/MultipleImagePicker';
import DatePicker from '@/components/DatePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import {
  ChevronDown,
  Ruler,
  Home,
  Check,
  QrCode,
  Rabbit,
  Tag,
  Leaf,
  Clipboard,
  Map,
  MapPin,
} from 'lucide-react-native';
import { Field, Farm } from '@/types';
import { analyzeImageWithVision } from '@/utils/openai-vision';
import { generateSuggestedItemId, validateItemId } from '@/utils/qrcode';
import DropdownPicker from '@/components/DropdownPicker';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useTranslation } from '@/i18n/useTranslation';
import { MaterialCommunityIcons } from '@expo/vector-icons';

// import MaterialCommunityIcons from '@expo/vector-icons/build/MaterialCommunityIcons';
import { analyzeImageWithAI } from '@/utils/ai-utils';
import { useLookupStore } from '@/store/lookup-store';
import { logEvent } from '@/store/logging';
// import { useLocalSearchParams } from 'expo-router';

export default function CreateAnimalScreen() {
  const { user } = useAuthStore();
  const {
    addAnimal,
    fields,
    fetchFields,
    currentFarm,
    farms,
    fetchFarms,
    setCurrentFarm,
    getAnimal,
    updateAnimal,
    getAnimalUpdated
  } = useFarmStore();
  const { getLookupsByCategory, getLookupsByCategoryParssedData } = useLookupStore();
  const { id } = useLocalSearchParams();
  const isEditMode = id ? true : false;
  const { t, isRTL } = useTranslation()
  const [name, setName] = useState('');
  const [species, setSpecies] = useState('');
  const [breed, setBreed] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState<Date | null>(null);
  const [gender, setGender] = useState<'male' | 'female' | 'unknown'>('unknown');
  const [status, setStatus] = useState<'healthy' | 'sick' | 'pregnant' | 'nursing' | 'quarantined'>('healthy');
  const [purpose, setPurpose] = useState<'dairy' | 'meat' | 'wool' | 'eggs' | 'breeding' | 'work' | 'pet'>('meat');
  const [weight, setWeight] = useState('');
  const [weightUnit, setWeightUnit] = useState<'kg' | 'lb'>('kg');
  const [fieldId, setFieldId] = useState('');
  const [fieldName, setFieldName] = useState('');
  const [notes, setNotes] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Item ID for QR code
  const [itemId, setItemId] = useState('');
  const [itemIdError, setItemIdError] = useState('');

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    name: '',
    species: '',
    images: ''
  });

  const [showGenderDropdown, setShowGenderDropdown] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showPurposeDropdown, setShowPurposeDropdown] = useState(false);
  const [showFieldModal, setShowFieldModal] = useState(false);
  const [showFarmModal, setShowFarmModal] = useState(false);
  const [identificationNumber, setIdentificationNumber] = useState('')
  // useEffect(() => {
  //   if (user?.id) {
  //     fetchFarms(user.id);
  //   }
  // }, [user]);

  useEffect(() => {
    if (currentFarm?.id) {
      fetchFields(currentFarm.id);
    }
  }, [currentFarm]);

  // Generate suggested item ID when name or species changes
  useEffect(() => {
    if (name || species) {
      const nameToUse = name || species;
      setItemId(generateSuggestedItemId('animal', nameToUse));
    }
  }, [name, species]);

  const [animal, setAnimal] = useState({} as any)
  const loadAnimalData = async (animalIdString: string) => {
    const res = await getAnimalUpdated(currentFarm?.id, animalIdString)
    setAnimal(res)
  }
  useEffect(() => {
    if (animal) {
      setName(animal.name || '');
      setSpecies(animal.species || '');
      setBreed(animal.breed || '');
      setGender(animal.gender || 'female');
      setStatus(animal.status || 'healthy');
      setPurpose(animal.purpose || 'meat');
      setDateOfBirth(animal.dateOfBirth ? new Date(animal.dateOfBirth) : null);
      setWeight(animal.weight ? animal.weight.toString() : '');
      setWeightUnit(animal.weightUnit || 'kg');
      setNotes(animal.notes || '');
      setItemId(animal.id || ''); // Use animal ID as item ID

      // Set field if available
      if (animal.fieldId) {
        setFieldId(animal.fieldId);
        const field = fields.find(f => f.id === animal.fieldId);
        if (field) setFieldName(field.name);
      }

      // Set image if available
      if (animal.image) {
        setImageUri(animal.image);
      }
    }
  }, [animal])
  useEffect(() => {
    if (isEditMode && id) {
      const animalIdString = Array.isArray(id) ? id[0] : id as string;
      loadAnimalData(animalIdString);


    }
  }, [isEditMode, id, fields]);

  const validateForm = () => {
    const errors = {
      name: '',
      species: '',
      images: ''
    };

    let hasErrors = false;

    // Validate item ID
    if (!itemId) {
      setItemIdError('Item ID is required');
      hasErrors = true;
    } else if (!validateItemId(itemId)) {
      setItemIdError('Item ID must be 3-20 characters and can only contain letters, numbers, hyphens, and underscores');
      hasErrors = true;
    } else {
      setItemIdError('');
    }

    // Validate required fields
    if (!name.trim()) {
      errors.name = t('animal.nameRequired');
      hasErrors = true;
    }

    if (!species.trim()) {
      errors.species = t('animal.speciesRequired');
      hasErrors = true;
    }

    // Breed is optional - no validation needed
    // Images are optional - no validation needed

    // Validate farm
    if (!currentFarm) {
      Alert.alert('Error', 'Please select a farm first');
      hasErrors = true;
    }

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleCreateAnimal = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      // Upload images if selected
      let imageUrls: string[] = [];
      if (images.length > 0) {
        for (const imageUri of images) {
          if (imageUri && !imageUri.startsWith('http')) {
            const uploadedUrl = await uploadImageAsync(imageUri, 'animals');
            imageUrls.push(uploadedUrl);
          } else if (imageUri) {
            imageUrls.push(imageUri);
          }
        }
      }

      // Use first image as main image
      const mainImage = imageUrls.length > 0 ? imageUrls[0] : '';

      // Create animal data object
      const animalData: any = {
        id: isEditMode ? id as string : itemId, // Use itemId as ID for new animals
        species,
        gender,
        status,
        purpose,
        farmId: currentFarm.id,
        identificationNumber: itemId,
        image: mainImage,
        images: imageUrls,
        updatedAt: new Date(),
      };

      // Only add createdAt for new animals
      if (!isEditMode) {
        animalData.createdAt = new Date();
      }

      // Only add these fields if they have values
      if (name) animalData.name = name;
      if (breed) animalData.breed = breed;
      if (dateOfBirth) animalData.dateOfBirth = dateOfBirth.toISOString();
      if (weight && !isNaN(Number(weight))) animalData.weight = Number(weight);
      if (weightUnit) animalData.weightUnit = weightUnit;
      if (fieldId) animalData.fieldId = fieldId;
      if (notes) animalData.notes = notes;
      // if(itemId)
      if (isEditMode) {
        await updateAnimal(animalData);
        Alert.alert('Success', 'Animal updated successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        // Add photos array with the uploaded image for new animals
        if (imageUrl) {
          animalData.photos = [{
            url: imageUrl,
            timestamp: new Date(),
            takenBy: ''
          }];
        }

        await addAnimal(animalData);
        logEvent('create_animal', {
          animalId: animalData.id,
          species: animalData.species,
          farmId: currentFarm.id,
        });
        Alert.alert('Success', 'Animal added successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      }
    } catch (error: any) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} animal:`, error);
      Alert.alert('Error', error.message || `Failed to ${isEditMode ? 'update' : 'add'} animal. Please try again.`);
      logEvent('error', {
        context: isEditMode ? 'update_animal' : 'create_animal',
        error: error.message,
        animalId: isEditMode ? id : itemId,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderFieldItem = ({ item }: { item: Field }) => (
    <TouchableOpacity
      style={[
        styles.modalItem,
        isRTL && { flexDirection: 'row-reverse' }
      ]}
      onPress={() => {
        setFieldId(item.id);
        setFieldName(item.name);
        setShowFieldModal(false);
      }}
    >
      <View style={[styles.modalItemContent, isRTL && { flexDirection: 'row-reverse' }]}>
        <MaterialIcons
          name="nature-people"
          size={20}
          color={colors.gray[600]}
          style={[styles.modalItemIcon, isRTL && { marginLeft: 8, marginRight: 0 }]}
        />
        <Text
          style={[
            styles.modalItemText,
            isRTL && { textAlign: 'right', marginRight: 8 }
          ]}
        >
          {t(item.name)}
        </Text>
      </View>

      {fieldId === item.id && (
        <MaterialIcons name="check" size={20} color={colors.primary} />
      )}
    </TouchableOpacity>

  );

  const renderFarmItem = ({ item }: { item: Farm }) => (
    <TouchableOpacity
      style={[
        styles.modalItem,
        isRTL && { flexDirection: 'row-reverse' }
      ]}
      onPress={() => {
        setCurrentFarm(item.id);
        setShowFarmModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <MaterialIcons
          name="home"
          size={20}
          color={colors.gray[600]}
          style={[styles.modalItemIcon, isRTL && { marginLeft: 8, marginRight: 0 }]}
        />
        <Text
          style={[
            styles.modalItemText,
            isRTL && { textAlign: 'right' }
          ]}
        >
          {t(item.name)}
        </Text>
      </View>

      {currentFarm?.id === item.id && (
        <MaterialIcons name="check" size={20} color={colors.primary} />
      )}
    </TouchableOpacity>

  );

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return colors.success;
      case 'sick':
        return colors.danger;
      case 'pregnant':
        return colors.info;
      case 'nursing':
        return colors.warning;
      case 'quarantined':
        return colors.secondary;
      default:
        return colors.gray[500];
    }
  };
  const handleAnalysisComplete = (response: any) => {
    // console.log({ response });

    if (response && response.details) {
      const details = response.details;

      // Set species if available
      if (details.species) {
        setSpecies(details.species);
      }

      // Set breed if available
      if (details.breed) {
        setBreed(details.breed);
      }

      // Set gender if available
      if (details.gender) {
        if (details.gender.toLowerCase().includes('female')) {
          setGender('female');
        } else if (details.gender.toLowerCase().includes('male')) {
          setGender('male');
        }
      }

      // Set status based on health status
      if (details.healthStatus) {
        const healthStatus = details.healthStatus.toLowerCase();
        if (healthStatus.includes('pregnant')) {
          setStatus('pregnant');
        } else if (healthStatus.includes('sick') ||
          healthStatus.includes('ill') ||
          healthStatus.includes('disease')) {
          setStatus('sick');
        } else if (healthStatus.includes('nursing')) {
          setStatus('nursing');
        } else if (healthStatus.includes('quarantine')) {
          setStatus('quarantined');
        } else {
          setStatus('healthy');
        }
      }

      // Set age if available
      // if (details.ageEstimate) {
      //   const ageEstimate = details.ageEstimate.toLowerCase();
      //   if (ageEstimate.includes('calf') || ageEstimate.includes('young')) {
      //     setAge('young');
      //   } else if (ageEstimate.includes('adult')) {
      //     setAge('adult');
      //   } else if (ageEstimate.includes('senior') || ageEstimate.includes('old')) {
      //     setAge('senior');
      //   }
      // }

      // Add analysis to notes if empty
      if (!notes && response.analysis) {
        setNotes(response.analysis);

        // Add detailed information to notes
        const detailsText = [];
        if (details.species) detailsText.push(`Species: ${details.species}`);
        if (details.breed) detailsText.push(`Breed: ${details.breed}`);
        if (details.gender) detailsText.push(`Gender: ${details.gender}`);
        if (details.ageEstimate) detailsText.push(`Age: ${details.ageEstimate}`);
        if (details.healthStatus) detailsText.push(`Health: ${details.healthStatus}`);
        if (details.bodyCondition) detailsText.push(`Body Condition: ${details.bodyCondition}`);
        if (details.notes) detailsText.push(`Additional Notes: ${details.notes}`);

        if (detailsText.length > 0) {
          setNotes(response.analysis + '\n\n' + detailsText.join('\n'));
        }
      }
    }
  };
  const iconColor = colors.gray[700] || 'black';

  const [animalSepeciesArray, setAnimalSepeciesArray] = useState([] as any)
  const [animalBreedArray, setAnimalBreedArray] = useState([] as any)
  const [animalGenderArray, setAnimalGenderArray] = useState([] as any)
  const [animalStatusArray, setAnimalStatusArray] = useState([] as any)
  const [animalPurposeArray, setAnimalPurposeArray] = useState([] as any)

  useEffect(() => {
    // 
    setAnimalSepeciesArray(getLookupsByCategoryParssedData('animalSpecies', 'form.'))
    setAnimalBreedArray(getLookupsByCategoryParssedData('animalBreed', 'breeds.'))
    setAnimalGenderArray(getLookupsByCategoryParssedData('animalGender', 'form.'))
    setAnimalStatusArray(getLookupsByCategoryParssedData('animalHealthStatus', 'form.'))
    setAnimalPurposeArray(getLookupsByCategoryParssedData('animalPurpose', 'form.'))
  }, [])


  const speciesArray = [
    { label: t('form.cow'), value: 'cow', icon: '' },
    { label: t('form.goat'), value: 'goat', icon: '' },
    { label: t('form.sheep'), value: 'sheep', icon: '' },
    { label: t('form.chicken'), value: 'chicken', icon: '' },
    { label: t('form.deer'), value: 'deer', icon: '' },
    { label: t('form.horse'), value: 'horse', icon: '' },
    { label: t('form.donkey'), value: 'donkey', icon: '' },
    { label: t('form.mule'), value: 'mule', icon: '' },
    { label: t('form.buffalo'), value: 'buffalo', icon: '' },
    { label: t('form.rabbit'), value: 'rabbit', icon: '' },
  ];


  const breedArray = [
    // Cow Breeds
    { value: 'sahiwal', label: t('breeds.sahiwal'), speciesId: 'cow', icon: '' },
    { value: 'holstein_friesian', label: t('breeds.holstein_friesian'), speciesId: 'cow', icon: '' },
    { value: 'jersey', label: t('breeds.jersey'), speciesId: 'cow', icon: '' },
    { value: 'red_sindhi', label: t('breeds.red_sindhi'), speciesId: 'cow', icon: '' },
    { value: 'ayrshire', label: t('breeds.ayrshire'), speciesId: 'cow', icon: '' },
    { value: 'brown_swiss', label: t('breeds.brown_swiss'), speciesId: 'cow', icon: '' },
    { value: 'guernsey', label: t('breeds.guernsey'), speciesId: 'cow', icon: '' },
    { value: 'tharparkar', label: t('breeds.tharparkar'), speciesId: 'cow', icon: '' },
    { value: 'gir', label: t('breeds.gir'), speciesId: 'cow', icon: '' },
    { value: 'kankrej', label: t('breeds.kankrej'), speciesId: 'cow', icon: '' },
    { value: 'droughtmaster', label: t('breeds.droughtmaster'), speciesId: 'cow', icon: '' },
    { value: 'nelore', label: t('breeds.nelore'), speciesId: 'cow', icon: '' },
    { value: 'brahman', label: t('breeds.brahman'), speciesId: 'cow', icon: '' },
    { value: 'angus', label: t('breeds.angus'), speciesId: 'cow', icon: '' },
    { value: 'hereford', label: t('breeds.hereford'), speciesId: 'cow', icon: '' },
    { value: 'charolais', label: t('breeds.charolais'), speciesId: 'cow', icon: '' },
    { value: 'limousin', label: t('breeds.limousin'), speciesId: 'cow', icon: '' },
    { value: 'simmental', label: t('breeds.simmental'), speciesId: 'cow', icon: '' },
    { value: 'montbeliarde', label: t('breeds.montbeliarde'), speciesId: 'cow', icon: '' },
    { value: 'bazadaise', label: t('breeds.bazadaise'), speciesId: 'cow', icon: '' },

    // Goat Breeds
    { value: 'beetal', label: t('breeds.beetal'), speciesId: 'goat', icon: '' },
    { value: 'boer', label: t('breeds.boer'), speciesId: 'goat', icon: '' },
    { value: 'jamunapari', label: t('breeds.jamunapari'), speciesId: 'goat', icon: '' },
    { value: 'black_bengal', label: t('breeds.black_bengal'), speciesId: 'goat', icon: '' },
    { value: 'barbari', label: t('breeds.barbari'), speciesId: 'goat', icon: '' },
    { value: 'kachhi', label: t('breeds.kachhi'), speciesId: 'goat', icon: '' },
    { value: 'osmanabadi', label: t('breeds.osmanabadi'), speciesId: 'goat', icon: '' },
    { value: 'sirohi', label: t('breeds.sirohi'), speciesId: 'goat', icon: '' },
    { value: 'malabari', label: t('breeds.malabari'), speciesId: 'goat', icon: '' },
    { value: 'teddy', label: t('breeds.teddy'), speciesId: 'goat', icon: '' },
    { value: 'kachchhi', label: t('breeds.kachchhi'), speciesId: 'goat', icon: '' },
    { value: 'nachi', label: t('breeds.nachi'), speciesId: 'goat', icon: '' },
    { value: 'makhi_chella', label: t('breeds.makhi_chella'), speciesId: 'goat', icon: '' },
    { value: 'pateri', label: t('breeds.pateri'), speciesId: 'goat', icon: '' },
    { value: 'rajhanpuri', label: t('breeds.rajhanpuri'), speciesId: 'goat', icon: '' },
    { value: 'dhanni', label: t('breeds.dhanni'), speciesId: 'goat', icon: '' },
    { value: 'kaghani', label: t('breeds.kaghani'), speciesId: 'goat', icon: '' },
    { value: 'kiko', label: t('breeds.kiko'), speciesId: 'goat', icon: '' },
    { value: 'toggenburg', label: t('breeds.toggenburg'), speciesId: 'goat', icon: '' },
    { value: 'angora', label: t('breeds.angora'), speciesId: 'goat', icon: '' },

    // Chicken Breeds
    { value: 'rhode_island_red', label: t('breeds.rhode_island_red'), speciesId: 'chicken', icon: '' },
    { value: 'leghorn', label: t('breeds.leghorn'), speciesId: 'chicken', icon: '' },
    { value: 'aseel', label: t('breeds.aseel'), speciesId: 'chicken', icon: '' },
    { value: 'silkie', label: t('breeds.silkie'), speciesId: 'chicken', icon: '' },
    { value: 'fayoumi', label: t('breeds.fayoumi'), speciesId: 'chicken', icon: '' },
    { value: 'sussex', label: t('breeds.sussex'), speciesId: 'chicken', icon: '' },
    { value: 'plymouth_rock', label: t('breeds.plymouth_rock'), speciesId: 'chicken', icon: '' },
    { value: 'cornish', label: t('breeds.cornish'), speciesId: 'chicken', icon: '' },
    { value: 'cochin', label: t('breeds.cochin'), speciesId: 'chicken', icon: '' },
    { value: 'hubbard', label: t('breeds.hubbard'), speciesId: 'chicken', icon: '' },
    { value: 'langshan', label: t('breeds.langshan'), speciesId: 'chicken', icon: '' },
    { value: 'orloff', label: t('breeds.orloff'), speciesId: 'chicken', icon: '' },
    { value: 'brahma', label: t('breeds.brahma'), speciesId: 'chicken', icon: '' },
    { value: 'minorca', label: t('breeds.minorca'), speciesId: 'chicken', icon: '' },
    { value: 'ancona', label: t('breeds.ancona'), speciesId: 'chicken', icon: '' },
    { value: 'marans', label: t('breeds.marans'), speciesId: 'chicken', icon: '' },
    { value: 'isa_brown', label: t('breeds.isa_brown'), speciesId: 'chicken', icon: '' },
    { value: 'lohmann_brown', label: t('breeds.lohmann_brown'), speciesId: 'chicken', icon: '' },
    { value: 'golden_comet', label: t('breeds.golden_comet'), speciesId: 'chicken', icon: '' },

    // Fish Breeds
    { value: 'rohu', label: t('breeds.rohu'), speciesId: 'fish', icon: '' },
    { value: 'tilapia', label: t('breeds.tilapia'), speciesId: 'fish', icon: '' },
    { value: 'catla', label: t('breeds.catla'), speciesId: 'fish', icon: '' },
    { value: 'pangasius', label: t('breeds.pangasius'), speciesId: 'fish', icon: '' },
    { value: 'common_carp', label: t('breeds.common_carp'), speciesId: 'fish', icon: '' },
    { value: 'mrigal', label: t('breeds.mrigal'), speciesId: 'fish', icon: '' },
    { value: 'silver_carp', label: t('breeds.silver_carp'), speciesId: 'fish', icon: '' },
    { value: 'grass_carp', label: t('breeds.grass_carp'), speciesId: 'fish', icon: '' },
    { value: 'mahseer', label: t('breeds.mahseer'), speciesId: 'fish', icon: '' },
    { value: 'trout', label: t('breeds.trout'), speciesId: 'fish', icon: '' }
  ];


  const genderArray = [
    { label: t('form.male'), value: 'male', icon: <MaterialIcons name="male" size={20} color={iconColor} /> },
    { label: t('form.female'), value: 'female', icon: <MaterialIcons name="female" size={20} color={iconColor} /> },
    { label: t('form.unknown'), value: 'unknown', icon: <MaterialIcons name="help-outline" size={20} color={iconColor} /> },
  ];

  const statusArray = [
    { label: t('form.healthy'), value: 'healthy', icon: <MaterialIcons name="favorite" size={20} color={colors.success} /> },
    { label: t('form.sick'), value: 'sick', icon: <MaterialIcons name="sick" size={20} color={colors.danger} /> },
    { label: t('form.pregnant'), value: 'pregnant', icon: <MaterialIcons name="pregnant-woman" size={20} color={iconColor} /> },
    { label: t('form.nursing'), value: 'nursing', icon: <MaterialIcons name="child-friendly" size={20} color={iconColor} /> },
    { label: t('form.quarantined'), value: 'quarantined', icon: <MaterialIcons name="do-not-disturb" size={20} color={colors.warning} /> },
  ];

  const purposeArray = [
    { label: t('form.dairy'), value: 'dairy', icon: <MaterialIcons name="local-drink" size={20} color={iconColor} /> },
    { label: t('form.meat'), value: 'meat', icon: <MaterialIcons name="restaurant" size={20} color={iconColor} /> },
    { label: t('form.wool'), value: 'wool', icon: <MaterialIcons name="checkroom" size={20} color={iconColor} /> },
    { label: t('form.eggs'), value: 'eggs', icon: <MaterialIcons name="egg" size={20} color={iconColor} /> },
    { label: t('form.breeding'), value: 'breeding', icon: <MaterialIcons name="group-work" size={20} color={iconColor} /> },
    { label: t('form.work'), value: 'work', icon: <MaterialIcons name="work" size={20} color={iconColor} /> },
    { label: t('form.pet'), value: 'pet', icon: <MaterialIcons name="pets" size={20} color={iconColor} /> },
  ];
  return (
    <>
      <Stack.Screen
        options={{
          title: isEditMode ? t('entity.animal.edit') : t('entity.animal.add'),
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Farm Selection */}
          {farms.length > 0 && (
            <View style={styles.farmSelectionContainer}>
              <Text style={[styles.farmSelectionLabel, isRTL && { textAlign: 'right' }]}>{t('farm.selectFarm')}</Text>
              <TouchableOpacity
                style={[
                  styles.farmSelectionButton,
                  { flexDirection: isRTL ? 'row-reverse' : 'row' },

                ]}
                onPress={() => setShowFarmModal(true)}
              >
                <MaterialIcons
                  name="home"
                  size={20}
                  color={colors.gray[500]}
                  style={styles.farmIcon}
                />
                <Text
                  style={[
                    styles.farmSelectionText,
                    { textAlign: isRTL ? 'right' : 'left', flex: 1 },
                    isRTL && { marginRight: 8 }
                  ]}
                >
                  {currentFarm?.name || t('Select a farm')}
                </Text>
                <MaterialIcons
                  name="expand-more"
                  size={20}
                  color={colors.gray[500]}
                  style={{ transform: [{ rotate: isRTL ? '180deg' : '0deg' }] }}
                />
              </TouchableOpacity>
            </View>
          )}

          <MultipleImagePicker
            label={t('animal.images')}
            images={images}
            onImagesChange={(newImages) => {
              setImages(newImages);
              if (fieldErrors.images) {
                setFieldErrors(prev => ({ ...prev, images: '' }));
              }
            }}
            maxImages={3}
            placeholder={t('animal.addPhoto')}
            error={fieldErrors.images}
          />

          <View style={styles.formContainer}>
            {/* Item ID field for QR code */}
            <View style={styles.itemIdContainer}>
              <View
                style={[
                  styles.itemIdHeader,
                  { flexDirection: isRTL ? 'row-reverse' : 'row' }
                ]}
              >
                <Text style={styles.label}>{t('form.itemIdForQR')}</Text>
                <MaterialIcons
                  name="qr-code"
                  size={20}
                  color={colors.primary}
                  style={{ marginStart: isRTL ? 0 : 8, marginEnd: isRTL ? 8 : 0 }}
                />
              </View>

              <Input
                placeholder={t('entity.animal.enterUniqueId')}
                value={itemId}
                onChangeText={(text) => {
                  setItemId(text);
                  if (text && !validateItemId(text)) {
                    setItemIdError(
                      t(
                        'entity.animal.itemIdValidation'
                      )
                    );
                  } else {
                    setItemIdError('');
                  }
                }}
                containerStyle={itemIdError ? styles.inputContainerError : styles.inputContainer}
              />

              {itemIdError ? (
                <Text style={styles.errorText}>{itemIdError}</Text>
              ) : (
                <Text style={[styles.helperText, isRTL && { textAlign: 'right' }]}>
                  {t('entity.animal.itemIdNote')}
                </Text>
              )}
            </View>

            <Input
              label={t('form.Animal Name')}
              placeholder={t('form.Enter animal name')}
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (fieldErrors.name) {
                  setFieldErrors(prev => ({ ...prev, name: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              leftIcon={
                <MaterialIcons
                  name="pets"
                  size={20}
                  color={colors.gray[500]}
                  style={{ marginEnd: isRTL ? 0 : 8, marginStart: isRTL ? 8 : 0 }}
                />
              }
              required={true}
              error={fieldErrors.name}
            />

            {/* <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
              {t('form.Species')}
            </Text> */}
            <DropdownPicker
              label={t('form.Species')}
              options={animalSepeciesArray || []}
              onSelect={(val) => {
                setSpecies(val as any);
                if (fieldErrors.species) {
                  setFieldErrors(prev => ({ ...prev, species: '' }));
                }
              }}
              selectedValue={species}
              isMultiple={false}
              required={true}
              error={fieldErrors.species}
            />

            {/* <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
              {t('form.Breed (Optional)')}
            </Text> */}
            <DropdownPicker
              label={t('form.Breed (Optional)')}
              options={animalBreedArray.filter((item) => item.parentId === species) || []}
              onSelect={(val) => setBreed(val as any)}
              selectedValue={breed}
              isMultiple={false}
              required={false}
            />

            <DatePicker
              label={t('form.Birth Date (Optional)')}
              value={dateOfBirth}
              onChange={setDateOfBirth}
              placeholder={t('form.Select a date')}
              maximumDate={new Date()}
            />


            <DropdownPicker
              label={t('form.Gender')}
              options={animalGenderArray || []}
              onSelect={(val) => setGender(val as any)}
              selectedValue={gender}
              isMultiple={false}
            />
            {/* <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
              {t('form.Health Status')}
            </Text> */}
            <DropdownPicker
              label={t('form.Health Status')}
              options={animalStatusArray || []}
              onSelect={(val) => setStatus(val as any)}
              selectedValue={status}
              isMultiple={false}
            />

            {/* <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
              {t('form.Purpose')}
            </Text> */}
            <DropdownPicker
              label={t('form.Purpose')}
              options={animalPurposeArray || []}
              onSelect={(val) => setPurpose(val as any)}
              selectedValue={purpose}
              isMultiple={true}
            />
            {/* <Input
              label={t('form.Identification Number (Optional)')}
              placeholder={t('form.Enter animal ID number')}
              value={identificationNumber}
              onChangeText={setIdentificationNumber}
              containerStyle={styles.inputContainer}
              leftIcon={
                <MaterialIcons
                  name="tag"
                  size={20}
                  color={colors.gray[500]}
                  style={{ marginEnd: isRTL ? 0 : 8, marginStart: isRTL ? 8 : 0 }}
                />
              }
            /> */}

            <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
              {t('entity.animal.fieldLocationOptional')}
            </Text>
            <TouchableOpacity
              style={[styles.fieldButton, isRTL && { flexDirection: "row-reverse" }]}
              onPress={() => {
                if (fields.length === 0) {
                  Alert.alert(
                    t('No Fields Available'),
                    t('Please create a field first.'),
                    [
                      { text: t('Cancel'), style: 'cancel' },
                      {
                        text: t('Create Field'),
                        onPress: () => router.push('/field/create')
                      }
                    ]
                  );
                } else {
                  setShowFieldModal(true);
                }
              }}
            >
              <MaterialIcons
                name="agriculture"
                size={20}
                color={colors.gray[500]}
                style={[styles.fieldIcon, isRTL && { marginStart: 8, marginEnd: 0 }]}
              />
              <Text style={[styles.fieldText, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                {fieldName || t('form.selectField')}
              </Text>
              <MaterialIcons name="arrow-drop-down" size={20} color={colors.gray[500]} />
            </TouchableOpacity>

            <Input
              label={t('form.Notes (Optional)')}
              placeholder={t('form.Enter any additional notes')}
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
              leftIcon={
                <MaterialIcons
                  name="note"
                  size={20}
                  color={colors.gray[500]}
                  style={{ marginEnd: isRTL ? 0 : 8, marginStart: isRTL ? 8 : 0 }}
                />
              }
            />

            <View style={styles.buttonContainer}>
              <Button
                title={t('form.Cancel')}
                variant="outline"
                onPress={() => router.back()}
                style={styles.cancelButton}
                disabled={isLoading}
              />
              <Button
                title={isLoading ? t('form.Adding...') : t('form.Add Animal')}
                onPress={handleCreateAnimal}
                style={styles.createButton}
                disabled={isLoading}
                leftIcon={
                  isLoading ? (
                    <ActivityIndicator size="small" color={colors.white} />
                  ) : undefined
                }
              />
            </View>
          </View>
        </ScrollView>

        {/* Field Selection Modal */}
        <Modal
          visible={showFieldModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFieldModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={[styles.modalHeader, isRTL && { flexDirection: 'row-reverse' }]}>
                <Text style={styles.modalTitle}>{t('form.selectField')}</Text>
                <TouchableOpacity onPress={() => setShowFieldModal(false)}>
                  <Text style={styles.modalCloseText}>{t('common.close')}</Text>
                </TouchableOpacity>
              </View>
              {fields.length > 0 ? (
                <FlatList
                  data={fields}
                  renderItem={renderFieldItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No fields available. Please create a field first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>

        {/* Farm Selection Modal */}
        <Modal
          visible={showFarmModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFarmModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Farm</Text>
                <TouchableOpacity onPress={() => setShowFarmModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {farms.length > 0 ? (
                <FlatList
                  data={farms}
                  renderItem={renderFarmItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No farms available. Please create a farm first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputContainerError: {
    marginBottom: 4,
    borderColor: colors.danger,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  dropdownText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  dropdownOptions: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  fieldButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 16,
  },
  fieldIcon: {
    marginRight: 8,
  },
  fieldText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  emptyListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  farmSelectionContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmSelectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  farmSelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  farmIcon: {
    marginRight: 8,
  },
  farmSelectionText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  itemIdContainer: {
    marginBottom: 16,
  },
  itemIdHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginBottom: 8,
  },
  helperText: {
    color: colors.gray[500],
    fontSize: 12,
    marginTop: 4,
  },
});
