import React, { ReactNode } from 'react';
import { View, ViewStyle, I18nManager } from 'react-native';
import { useTranslation } from '@/i18n/useTranslation';

interface RTLWrapperProps {
  children: ReactNode;
  style?: ViewStyle;
}

/**
 * A wrapper component that handles RTL layout direction
 * Use this for row layouts that need to be reversed in RTL mode
 */
const RTLWrapper: React.FC<RTLWrapperProps> = ({ children, style }) => {
  const { isRTL } = useTranslation();
  
  return (
    <View 
      style={[
        { 
          flexDirection: isRTL ? 'row-reverse' : 'row',
        },
        style
      ]}
    >
      {children}
    </View>
  );
};

export default RTLWrapper;