import { en } from './translations/en';
import { ur } from './translations/ur';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Default to English
let currentLocale: 'en' | 'ur' = 'en';

// Initialize from AsyncStorage if available
(async () => {
  try {
    const storedLocale = await AsyncStorage.getItem('appLanguage');
    if (storedLocale === 'en' || storedLocale === 'ur') {
      currentLocale = storedLocale;
      // Notify listeners of initial language
      listeners.forEach(cb => cb(storedLocale));
    }
  } catch (error) {
    console.error('Error initializing language from storage:', error);
  }
})();

const translations: Record<string, any> = {
  en,
  ur,
};

// Helper to get value from nested keys like 'auth.login'
const getNestedTranslation = (obj: any, key: string): string | undefined => {
  return key.split('.').reduce((acc, part) => acc?.[part], obj);
};

export const t = (key: string, params?: Record<string, any>): string => {
  const translation = getNestedTranslation(translations[currentLocale], key);
  if (!translation) return key;

  // Basic variable substitution if params are provided
  if (params) {
    return Object.keys(params).reduce((result, paramKey) => {
      return result.replace(`{{${paramKey}}}`, params[paramKey]);
    }, translation);
  }

  return translation;
};

export const changeLanguage = async (newLocale: string) => {
  if (newLocale === 'en' || newLocale === 'ur') {
    currentLocale = newLocale;
    // Store in AsyncStorage
    try {
      await AsyncStorage.setItem('appLanguage', newLocale);
    } catch (error) {
      console.error('Error storing language preference:', error);
    }
    // Notify listeners
    listeners.forEach((cb) => cb(newLocale));
  }
};

export const getCurrentLocale = () => currentLocale;

export const isRTL = () => ['ur', 'ar'].includes(currentLocale);

// Simple pub-sub for language changes
const listeners: ((lang: string) => void)[] = [];

export const onLanguageChange = (cb: (lang: string) => void) => {
  listeners.push(cb);
};

export const offLanguageChange = (cb: (lang: string) => void) => {
  const index = listeners.indexOf(cb);
  if (index > -1) listeners.splice(index, 1);
};


