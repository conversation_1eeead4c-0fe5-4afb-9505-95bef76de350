import { 
  collection, 
  query, 
  where, 
  getDocs, 
  addDoc, 
  deleteDoc, 
  doc, 
  orderBy,
  updateDoc 
} from 'firebase/firestore';
// import { firestore } from '@/utils/firebase';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { firestore } from '@/firebase/config';

export interface GalleryPhoto {
  id?: string;
  url: string;
  timestamp: Date | string;
  takenBy?: string;
  description?: string;
  entityId: string;
  entityType: string;
  farmId: string;
  createdAt: Date;
}

export class GalleryService {
  private static getGalleryRef(farmId: string) {
    return collection(firestore, `farms/${farmId}/gallery`);
  }

  // Load images for specific entity
  static async loadEntityImages(
    farmId: string, 
    entityId: string, 
    entityType: string
  ): Promise<GalleryPhoto[]> {
    try {
      const galleryRef = this.getGalleryRef(farmId);
      const q = query(
        galleryRef,
        where('entityId', '==', entityId),
        where('entityType', '==', entityType),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as GalleryPhoto[];
    } catch (error) {
      console.error('Error loading entity images:', error);
      return [];
    }
  }

  // Add new image to gallery
  static async addImage(
    farmId: string,
    imageUri: string,
    entityId: string,
    entityType: string,
    metadata?: {
      takenBy?: string;
      description?: string;
    }
  ): Promise<GalleryPhoto> {
    try {
      // Upload image to storage
      const imageUrl = await uploadImageAsync(imageUri, `${entityType}s`);
      
      // Create photo document
      const photoData = {
        url: imageUrl,
        timestamp: new Date(),
        entityId,
        entityType,
        farmId,
        createdAt: new Date(),
        takenBy: metadata?.takenBy || '',
        description: metadata?.description || '',
      };
      
      const galleryRef = this.getGalleryRef(farmId);
      const docRef = await addDoc(galleryRef, photoData);
      
      return {
        id: docRef.id,
        ...photoData,
      };
    } catch (error) {
      console.error('Error adding image to gallery:', error);
      throw error;
    }
  }

  // Delete image from gallery
  static async deleteImage(farmId: string, photoId: string): Promise<void> {
    try {
      const photoRef = doc(firestore, `farms/${farmId}/gallery`, photoId);
      await deleteDoc(photoRef);
    } catch (error) {
      console.error('Error deleting image from gallery:', error);
      throw error;
    }
  }

  // Update image metadata
  static async updateImageMetadata(
    farmId: string,
    photoId: string,
    metadata: {
      description?: string;
      takenBy?: string;
    }
  ): Promise<void> {
    try {
      const photoRef = doc(firestore, `farms/${farmId}/gallery`, photoId);
      await updateDoc(photoRef, {
        ...metadata,
        updatedAt: new Date(),
      });
    } catch (error) {
      console.error('Error updating image metadata:', error);
      throw error;
    }
  }

  // Load images for multiple entities (useful for bulk operations)
  static async loadMultipleEntityImages(
    farmId: string,
    entities: Array<{ entityId: string; entityType: string }>
  ): Promise<Record<string, GalleryPhoto[]>> {
    try {
      const results: Record<string, GalleryPhoto[]> = {};
      
      await Promise.all(
        entities.map(async ({ entityId, entityType }) => {
          const images = await this.loadEntityImages(farmId, entityId, entityType);
          results[`${entityType}_${entityId}`] = images;
        })
      );
      
      return results;
    } catch (error) {
      console.error('Error loading multiple entity images:', error);
      return {};
    }
  }

  // Get image count for entity
  static async getImageCount(
    farmId: string,
    entityId: string,
    entityType: string
  ): Promise<number> {
    try {
      const images = await this.loadEntityImages(farmId, entityId, entityType);
      return images.length;
    } catch (error) {
      console.error('Error getting image count:', error);
      return 0;
    }
  }
}