{"expo": {"name": "kissan-dost-app", "slug": "kissan-dost-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.kissan-dost.app", "config": {"googleMapsApiKey": "AIzaSyCh95eH1qW50A_VHelipdtwyGc2xh4bluo"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.malik123.kissandostapp", "config": {"googleMaps": {"apiKey": "AIzaSyCh95eH1qW50A_VHelipdtwyGc2xh4bluo"}}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}, "updates": {"enabled": true, "fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/671a529b-7799-4186-8da9-1c5e22698337"}, "runtimeVersion": {"policy": "appVersion"}, "extra": {"router": {"origin": false}, "eas": {"projectId": "671a529b-7799-4186-8da9-1c5e22698337"}}, "owner": "malik123"}}