import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Wifi, WifiOff } from 'lucide-react-native';
import { useFarmStore } from '@/store/farm-store';
import { colors } from '@/constants/colors';

export const RealTimeSyncIndicator = () => {
  const { isRealTimeEnabled } = useFarmStore();

  return (
    <View style={[styles.container, isRealTimeEnabled ? styles.connected : styles.disconnected]}>
      {isRealTimeEnabled ? (
        <Wifi size={12} color={colors.success} />
      ) : (
        <WifiOff size={12} color={colors.gray[500]} />
      )}
      <Text style={[styles.text, isRealTimeEnabled ? styles.connectedText : styles.disconnectedText]}>
        {isRealTimeEnabled ? 'Live' : 'Offline'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  connected: {
    backgroundColor: colors.successLight,
  },
  disconnected: {
    backgroundColor: colors.gray[100],
  },
  text: {
    fontSize: 10,
    fontWeight: '500',
  },
  connectedText: {
    color: colors.success,
  },
  disconnectedText: {
    color: colors.gray[500],
  },
});