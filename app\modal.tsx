import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, SafeAreaView } from 'react-native';
import { router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { 
  Leaf, 
  TreeDeciduous, 
  Sprout, 
  Rabbit, 
  Tractor, 
  Wheat, 
  ListTodo, 
  X 
} from 'lucide-react-native';

export default function Modal() {
  const items = [
    {
      title: 'Add Field',
      icon: <Leaf size={24} color={colors.white} />,
      color: colors.success,
      route: '/field/create',
      description: 'Add a new field or plot of land'
    },
    {
      title: 'Add Garden',
      icon: <TreeDeciduous size={24} color={colors.white} />,
      color: colors.primary,
      route: '/garden/create',
      description: 'Add a new garden area'
    },
    {
      title: 'Add Plant',
      icon: <Sprout size={24} color={colors.white} />,
      color: colors.info,
      route: '/plant/create',
      description: 'Add a new plant or crop'
    },
    {
      title: 'Add Animal',
      icon: <Rabbit size={24} color={colors.white} />,
      color: colors.warning,
      route: '/animal/create',
      description: 'Add a new animal to your farm'
    },
    {
      title: 'Add Equipment',
      icon: <Tractor size={24} color={colors.white} />,
      color: colors.secondary,
      route: '/equipment/create',
      description: 'Add new farm equipment'
    },
    {
      title: 'Add Yield',
      icon: <Wheat size={24} color={colors.white} />,
      color: colors.success,
      route: '/yield/create',
      description: 'Record a new harvest yield'
    },
    {
      title: 'Add Task',
      icon: <ListTodo size={24} color={colors.white} />,
      color: colors.primary,
      route: '/task/create',
      description: 'Create a new task or activity'
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Add New Item',
          headerRight: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <X size={24} color={colors.gray[800]} />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.title}>What would you like to add?</Text>
        <Text style={styles.subtitle}>Select an option below to add a new item to your farm</Text>
        
        <View style={styles.grid}>
          {items.map((item, index) => (
            <TouchableOpacity 
              key={index}
              style={styles.item}
              onPress={() => router.push(item.route)}
            >
              <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
                {item.icon}
              </View>
              <Text style={styles.itemTitle}>{item.title}</Text>
              <Text style={styles.itemDescription}>{item.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 24,
    textAlign: 'center',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  item: {
    width: '48%',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 12,
    color: colors.gray[600],
  },
});