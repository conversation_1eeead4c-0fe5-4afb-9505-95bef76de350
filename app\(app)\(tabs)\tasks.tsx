import React, { useState, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  Modal,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import TaskCard from '@/components/TaskCard';
import { Task } from '@/types';
import { Plus, Filter } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';

export default function TasksScreen() {
  const { tasks } = useFarmStore();
  const { user } = useAuthStore();
  const { t, isRTL } = useTranslation();
  const [activeFilter, setActiveFilter] = useState('today');
  const [priorityFilter, setPriorityFilter] = useState('all');

  // console.log(tasks.length)
  // const filteredTasks = tasks.filter(task => {
  //   // Status filter
  //   if (activeFilter === 'completed' && task.status !== 'completed') return false;
  //   if (activeFilter === 'pending' && task.status !== 'pending') return false;
  //   if (activeFilter === 'overdue' && task.status !== 'overdue') return false;
  //   if (activeFilter === 'assigned' && task.assignedTo !== user?.id) return false;

  //   // Priority filter
  //   if (priorityFilter !== 'all' && task.priority !== priorityFilter) return false;

  //   return true;
  // });
  const filteredTasks = tasks.filter((task) => {
    const dueDate = new Date(task.dueDate); // assumes ISO format or valid date string
    const today = new Date();

    // Normalize both to compare date only (ignore time)
    const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Role-based filter
    if (user?.role === 'caretaker' && task.assignedTo !== user.id) return false;

    // Status/date filters
    switch (activeFilter) {
      case 'today':
        if (dueDateOnly.getTime() !== todayOnly.getTime()) return false;
        break;
      case 'overdue':
        if (dueDateOnly < todayOnly && task.status !== 'completed') {
          // ok
        } else {
          return false;
        }
        break;
      case 'pending':
        if (task.status !== 'pending') return false;
        break;
      case 'completed':
        if (task.status !== 'completed') return false;
        break;
      case 'assigned':
        if (task.assignedTo !== user?.id) return false;
        break;
    }

    // Priority filter
    if (priorityFilter !== 'all' && task.priority !== priorityFilter) return false;

    return true;
  });

  const handleTaskPress = (task: Task) => {
    router.push(`/task/${task.id}`);
  };

  const renderTaskItem = ({ item }: { item: Task }) => (
    <TaskCard task={item} onPress={() => handleTaskPress(item)} />
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.tabsContainer}>
        <View style={[styles.tabs, isRTL && { flexDirection: 'row-reverse' }]}>
          {['today', 'overdue', 'pending', 'completed'].map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.tab,
                activeFilter === filter && styles.activeTab,
              ]}
              onPress={() => setActiveFilter(filter)}
            >
              <Text style={[
                styles.tabText,
                activeFilter === filter && styles.activeTabText,
              ]}>
                {t(`tasks.tabs.${filter}`, filter.charAt(0).toUpperCase() + filter.slice(1))}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* </ScrollView> */}
      </View>
      <FlatList
        data={filteredTasks}
        renderItem={renderTaskItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>{t('entity.farm.noTasksAdded')}</Text>
          </View>
        }
      />

      {(user?.role === 'owner' || user?.role === 'admin') && (
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/task/create')}
        >
          <Plus size={24} color={colors.white} />
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 8,
    // marginHorizontal: 16,
    backgroundColor: '#f0f2f5',
    borderRadius: 12,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    marginHorizontal: 4,
    alignItems: 'center',
    borderRadius: 24,
    backgroundColor: 'transparent',
  },
  activeTab: {
    backgroundColor: colors.success, // Tailwind 'blue-600'
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3, // Android shadow
  },
  tabText: {
    fontSize: 14,
    color: '#555',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '700',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[800],
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: colors.white,
  },
  activeFilterTab: {
    backgroundColor: colors.primary,
  },
  filterTabText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  activeFilterTabText: {
    color: colors.white,
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 5,
  },
  rtlText: {
    textAlign: 'right',
  },
  rtlRow: {
    flexDirection: 'row-reverse',
  },
  tabsContainer: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  // tabs: {
  //   // flex: 1,
  //   flexDirection: 'row',
  //   // alignItems: "space-between",
  //   paddingHorizontal: 16,
  // },
  // // tab: {
  // //   paddingVertical: 12,
  // //   paddingHorizontal: 16,
  // //   marginRight: 8,
  // // },
  // // activeTab: {
  // //   borderBottomWidth: 2,
  // //   borderBottomColor: colors.primary,
  // // },
  // // tabText: {
  // //   fontSize: 16,
  // //   fontWeight: '500',
  // //   color: colors.gray[600],
  // // },
  // // activeTabText: {
  // //   color: colors.primary,
  // //   fontWeight: '600',
  // // },

});
