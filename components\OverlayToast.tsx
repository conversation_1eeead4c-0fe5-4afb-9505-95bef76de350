import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';

const OverlayToast = ({ text1, text2, ...rest }) => {
  return (
    <View style={styles.overlayContainer}>
      <View style={[styles.toastContent, text1=== 'Error' ? {borderLeftColor: 'red'} : {borderLeftColor: 'green'}]}>
        <Text style={styles.title}>{text1}</Text>
        {text2 ? <Text style={styles.subtitle}>{text2}</Text> : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    bottom: 100, // Position from bottom with some spacing
    left: 20,
    right: 20,
    zIndex: 9999,
  },
  toastContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    elevation: 5,
    borderLeftWidth: 5,
    
    maxWidth: '80%',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 10,
    textAlign: 'center',
  },
});

export default OverlayToast;
