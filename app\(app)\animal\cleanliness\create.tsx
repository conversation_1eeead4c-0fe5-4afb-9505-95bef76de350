// ... imports ...
import { useState } from "react";
import { View, Text, Switch, TextInput, Button } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
// import { firestore } from "../../../firebase/index";
import { collection, addDoc } from "firebase/firestore";
import React from "react";

export default function AddCleanlinessScreen() {
  const router = useRouter();
  const { animal_id } = useLocalSearchParams();
  const [bathGiven, setBathGiven] = useState(false);
  const [shelterCleaned, setShelterCleaned] = useState(false);
  const [groomingDone, setGroomingDone] = useState(false);
  const [date, setDate] = useState(new Date().toISOString().slice(0, 10));
  const [remarks, setRemarks] = useState("");

  const handleSubmit = async () => {
    // await addDoc(collection(firestore, "animal_cleanliness"), {
    //   animal_id,
    //   bath_given: bathGiven,
    //   shelter_cleaned: shelterCleaned,
    //   grooming_done: groomingDone,
    //   date: new Date(date).toISOString(),
    //   remarks,
    // });
    // router.back();
  };

  return (
    <View>
      <Text>Bath Given</Text>
      <Switch value={bathGiven} onValueChange={setBathGiven} />
      <Text>Shelter Cleaned</Text>
      <Switch value={shelterCleaned} onValueChange={setShelterCleaned} />
      <Text>Grooming Done</Text>
      <Switch value={groomingDone} onValueChange={setGroomingDone} />
      <Text>Date</Text>
      <TextInput value={date} onChangeText={setDate} />
      <Text>Remarks</Text>
      <TextInput value={remarks} onChangeText={setRemarks} />
      <Button title="Save Cleanliness Record" onPress={handleSubmit} />
    </View>
  );
}