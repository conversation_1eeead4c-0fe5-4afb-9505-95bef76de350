import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Trash2 } from 'lucide-react-native'; // or appropriate icon lib
import { StyleSheet } from 'react-native';
// import styles from './styles'; // your shared styles
import colors from '@/constants/colors'; // adjust import based on your project
import { useTranslation } from '@/i18n/useTranslation';



type Props = {
  item: {
    id: string;
    avatar?: string;
    displayName: string;
    email: string;
    phone?: string;
    role: 'owner' | 'admin' | 'user';
  };
  userRole: 'owner' | 'admin' | 'user';
  onRemoveUser: (id: string) => void;
};

const TeamMemberCard: React.FC<Props> = ({ item, userRole, onRemoveUser }) => {
  const getBadgeColor = () => {
    switch (item.role) {
      case 'owner':
        return colors.primary + '20';
      case 'admin':
        return colors.warning + '20';
      default:
        return colors.info + '20';
    }
  };

  const getTextColor = () => {
    switch (item.role) {
      case 'owner':
        return colors.primary;
      case 'admin':
        return colors.warning;
      default:
        return colors.info;
    }
  };
  const { isRTL } = useTranslation()
  return (
    <View style={[styles.teamMemberCard, isRTL && { flexDirection: 'row-reverse' }]}>
      <Image
        source={{
          uri: item.avatar ||
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?...',
        }}
        style={styles.teamMemberCardAvatar}
      />
      <View style={styles.teamMemberCardContent}>
        <Text style={[styles.teamMemberCardName,isRTL && {textAlign:'right',marginRight:8}]}>{item.displayName||item?.name}</Text>
        {/* <Text style={[styles.teamMemberCardEmail,isRTL && {textAlign:'right',marginRight:8}]}>{item.email}</Text> */}
        {item.phone && <Text style={[styles.teamMemberCardPhone,isRTL && {textAlign:'right',marginRight:8}]}>{item.phone}</Text>}

        <View style={[styles.roleBadge,isRTL &&{alignSelf:'flex-end'}, { backgroundColor: getBadgeColor() }]}>
          <Text style={[styles.roleText, { color: getTextColor() },isRTL && {textAlign:'right',marginRight:8}]}>
            {item.role.charAt(0).toUpperCase() + item.role.slice(1)}
          </Text>
        </View>
      </View>

      {userRole === 'owner' && item.role !== 'owner' && (
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => onRemoveUser(item.id)}
        >
          <Trash2 size={20} color={colors.danger} />
        </TouchableOpacity>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  teamMemberCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  teamMemberCardAvatar: {
    width: 40,
    height: 40,
    borderRadius: 25,
    marginRight: 12,
  },
  teamMemberCardContent: {
    flex: 1,
  },
  teamMemberCardName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 2,
  },
  teamMemberCardEmail: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },
  teamMemberCardPhone: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  // teamMemberCard: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   padding: 16,
  //   backgroundColor: '#fff',
  //   borderRadius: 8,
  //   marginBottom: 12,
  //   shadowColor: '#000',
  //   shadowOffset: { width: 0, height: 2 },
  //   shadowOpacity: 0.1,
  //   shadowRadius: 4,
  //   elevation: 2,
  // },
  // teamMemberCardAvatar: {
  //   width: 48,
  //   height: 48,
  //   borderRadius: 24,
  //   marginRight: 16,
  //   backgroundColor: '#eee',
  // },
  // teamMemberCardContent: {
  //   flex: 1,
  // },
  // teamMemberCardName: {
  //   fontSize: 16,
  //   fontWeight: 'bold',
  //   color: '#222',
  // },
  // teamMemberCardEmail: {
  //   fontSize: 14,
  //   color: '#666',
  // },
  // teamMemberCardPhone: {
  //   fontSize: 14,
  //   color: '#888',
  // },
  roleBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 6,
  },
  roleText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  removeButton: {
    marginLeft: 12,
    padding: 8,
    borderRadius: 16,
    backgroundColor: '#fff',
  },
});

export default TeamMemberCard;
