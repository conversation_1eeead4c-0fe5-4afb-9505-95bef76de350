import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, I18nManager } from 'react-native';
import { useTranslation } from '@/i18n/useTranslation';
import { CheckCircle2, AlertCircle } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { Task } from '@/types';

interface TaskCardProps {
  task: Task;
  onPress?: (task: Task) => void;
}

// const placeholderImage = require('@/assets/images/placeholder.png');

export default function TaskCard({ task, onPress }: TaskCardProps) {
  const { t, isRTL } = useTranslation();

  // Get status color based on task status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return colors.success;
      case 'in_progress':
        return colors.warning;
      case 'overdue':
        return colors.danger;
      case 'pending':
      default:
        return colors.info;
    }
  };

  const statusColor = getStatusColor(task.status);
  const statusIcon = task.status === 'completed' ? (
    <CheckCircle2 size={16} color={statusColor} />
  ) : (
    <AlertCircle size={16} color={statusColor} />
  );

  return (
    <TouchableOpacity
      onPress={() => onPress?.(task)}
      activeOpacity={0.7}
      style={[
        styles.card,
        isRTL && styles.cardRtl,
        { borderLeftColor: statusColor, borderLeftWidth: 8 }
      ]}
    >
      <View style={[styles.content, isRTL && styles.contentRtl]}>
        <View style={styles.header}>
          <Text style={[styles.title, isRTL && styles.textAlignRight]} numberOfLines={1}>
            {task.title}
          </Text>
          <View style={[styles.priorityBadge, { backgroundColor: `${statusColor}20` }]}>
            <Text style={[styles.priorityText, { color: statusColor }]}>
              {task.priority?.toUpperCase()}
            </Text>
          </View>
        </View>

        {/* {task.description && ( */}
          <Text style={[styles.description, isRTL && styles.textAlignRight]} numberOfLines={2}>
            {task.description ||"description not added..."}
          </Text>
        {/* )} */}

        <View style={[styles.footer, isRTL && styles.footerRtl]}>
          <View style={[styles.statusContainer, isRTL && styles.statusContainerRtl]}>
            {statusIcon}
            <Text style={[styles.status, { color: statusColor }]}>
              {t(`task.status.${task.status}`)}
            </Text>
          </View>

          <Text style={[styles.date, isRTL && styles.textAlignRight]}>
            {new Date(task.dueDate).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginVertical: 2,
    // marginHorizontal: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    borderLeftWidth: 4,
    minHeight: 120,
    width:'98%'
  },
  cardRtl: {
    borderLeftWidth: 0,
    borderRightWidth: 4,
  },
  content: {
    padding: 16,
    flex: 1,
  },
  contentRtl: {
    alignItems: 'flex-end',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    flex: 1,
    marginRight: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  description: {
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 20,
    marginBottom: 12,
  },
  footer: {
    // bottom:8,
    // position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerRtl: {
    flexDirection: 'row-reverse',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusContainerRtl: {
    flexDirection: 'row-reverse',
  },
  status: {
    fontSize: 12,
    fontWeight: '600',
  },
  date: {
    fontSize: 12,
    color: colors.gray[500],
    fontWeight: '500',
  },
  textAlignRight: {
    textAlign: 'right',
  },
});
