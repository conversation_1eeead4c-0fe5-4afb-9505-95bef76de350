import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import { router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import MultipleImagePicker from '@/components/MultipleImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import {
  MapPin,
  ChevronDown,
  Ruler,
} from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';
import Dropdown from '@/components/Dropdown';
import DropdownPicker from '@/components/DropdownPicker';
import { useLookupStore } from '@/store/lookup-store';
import Toast from 'react-native-toast-message';

export default function CreateFarmScreen() {
  const { user } = useAuthStore();
  const { addFarm } = useFarmStore();
  const { t, isRTL } = useTranslation();
  const { getLookupsByCategory } = useLookupStore();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<'mixed' | 'crop' | 'livestock' | 'orchard'>('mixed');
  const [size, setSize] = useState('');
  const [sizeUnit, setSizeUnit] = useState<'acres' | 'hectares'>('acres');
  const [location, setLocation] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [showTypeDropdown, setShowTypeDropdown] = useState(false);
  const [showSizeUnitDropdown, setShowSizeUnitDropdown] = useState(false);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    name: '',
    size: '',
    type: '',
    location: '',
    images: ''
  });

  const validateFields = () => {
    const errors = {
      name: '',
      size: '',
      type: '',
      location: '',
      images: ''
    };

    let hasErrors = false;

    if (!name.trim()) {
      errors.name = t('farm.nameRequired');
      hasErrors = true;
    }

    if (!size || isNaN(Number(size)) || Number(size) <= 0) {
      errors.size = t('farm.validSizeRequired');
      hasErrors = true;
    }

    if (!type) {
      errors.type = t('farm.typeRequired');
      hasErrors = true;
    }

    if (!location.trim()) {
      errors.location = t('farm.locationRequired');
      hasErrors = true;
    }

    // Images are optional, but if provided should be valid
    if (images.length === 0) {
      errors.images = t('farm.imagesOptional');
    }

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleCreateFarm = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    if (!user) {
      Alert.alert(t('error'), t('auth.loginRequired'));
      return;
    }

    try {
      setIsLoading(true);

      // Upload images if selected
      let imageUrls: string[] = [];
      if (images.length > 0) {
        for (const imageUri of images) {
          if (imageUri && !imageUri.startsWith('http')) {
            const uploadedUrl = await uploadImageAsync(imageUri, 'farms');
            imageUrls.push(uploadedUrl);
          } else if (imageUri) {
            imageUrls.push(imageUri);
          }
        }
      }

      // Use first image as main image, or default if none
      const mainImage = imageUrls.length > 0
        ? imageUrls[0]
        : 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80';

      // Create farm data object
      const farmData: any = {
        name,
        description,
        type,
        size: Number(size),
        sizeUnit,
        location,
        ownerId: user.id,
        image: mainImage,
        images: imageUrls,
        status: 'active',
      };

      await addFarm(farmData);

      Alert.alert(t('success'), t('farm.createSuccess'), [
        { text: t('common.ok'), onPress: () => router.push('/') }
      ]);
    } catch (error: any) {
      console.error('Error creating farm:', error);
      Alert.alert(t('error'), error.message || t('farm.createError'));
    } finally {
      setIsLoading(false);
    }
  };

  const [farmTypesArray, setFarmTypesArray] = useState([] as any)
  const [landSizeUnitsArray, setLandSizeUnitsArray] = useState([] as any)
  useEffect(() => {
    const types=getLookupsByCategory('farmType')
    // console.log({types})
    setFarmTypesArray(types.map(item=>({label:item?.title,value:item?.id})))
    const units=getLookupsByCategory('areaUnit')
    // console.log({units})
    setLandSizeUnitsArray(units.map(item=>({label:item?.title,value:item?.id})))

  }, [])


  const landSizeUnits = [
    {
      label: "Marla (PK)",
      value: "marla_pk",
      equivalent: "25.2929 m² or 272.25 ft²"
    },
    {
      label: "Kanal (PK)",
      value: "kanal_pk",
      equivalent: "505.857 m² (20 Marla)"
    },
    {
      label: "Acre (PK)",
      value: "acre_pk",
      equivalent: "4,046.86 m²"
    },
    {
      label: "Square Foot (ft²)",
      value: "sqft",
      equivalent: "0.0929 m²"
    },
    {
      label: "Square Yard (yd²)",
      value: "sqyd",
      equivalent: "0.8361 m²"
    },
    {
      label: "Acre (US)",
      value: "acre_us",
      equivalent: "4,840 yd² or 43,560 ft²"
    },
    {
      label: "Square Mile",
      value: "sqmile",
      equivalent: "640 acres = 2.59 km²"
    }
  ];

  const farmTypes = [
    {
      label: t("farm.farmtypes.mixed"),
      value: "mixed",
      icon: "shuffle" // Icon for mixed type (e.g., Lucide or FontAwesome)
    },
    {
      label: t("farm.farmtypes.crop"),
      value: "crop",
      icon: "leaf" // Represents crop farming
    },
    {
      label: t("farm.farmtypes.livestock"),
      value: "livestock",
      icon: "cow" // Represents livestock
    },
    {
      label: t("farm.farmtypes.orchard"),
      value: "orchard",
      icon: "apple" // Represents orchards/fruits
    }
  ];


  return (
    <>
      <Stack.Screen
        options={{
          title: t('farm.createNew'),
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <MultipleImagePicker
            label={t('farm.images')}
            images={images}
            onImagesChange={(newImages) => {
              setImages(newImages);
              if (fieldErrors.images) {
                setFieldErrors(prev => ({ ...prev, images: '' }));
              }
            }}
            maxImages={3}
            placeholder={t('farm.addPhoto')}
            error={fieldErrors.images}
          />

          <View style={styles.formContainer}>
            <Input
              label={t('farm.name')}
              placeholder={t('farm.enterName')}
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (fieldErrors.name) {
                  setFieldErrors(prev => ({ ...prev, name: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              required={true}
              error={fieldErrors.name}
            />

            {/* <Text style={[styles.label, isRTL && styles.labelRTL]}>{t('farm.type')}</Text> */}
            <DropdownPicker
              label={t("farm.type")}
              options={farmTypesArray}
              onSelect={(val) => {
                setType(val as any);
                if (fieldErrors.type) {
                  setFieldErrors(prev => ({ ...prev, type: '' }));
                }
              }}
              selectedValue={type}
              isMultiple={false}
              required={true}
              error={fieldErrors.type}
            />

            <View style={[styles.rowContainer, isRTL && styles.rowContainerRTL]}>
              <View style={styles.sizeContainer}>
                <Text style={[styles.label, isRTL && styles.labelRTL]}>{t('farm.size')}</Text>
                <View style={styles.sizeInputContainer}>
                  <Input
                    placeholder="0.0"
                    value={size}
                    onChangeText={(text) => {
                      setSize(text);
                      if (fieldErrors.size) {
                        setFieldErrors(prev => ({ ...prev, size: '' }));
                      }
                    }}
                    keyboardType="numeric"
                    containerStyle={styles.sizeInput}
                    leftIcon={<Ruler size={20} color={colors.gray[500]} />}
                    isRTL={isRTL}
                    required={true}
                    error={fieldErrors.size}
                  />
                </View>
              </View>

              <View style={[styles.unitContainer, { marginRight: isRTL ? 10 : 0, marginLeft: isRTL ? 0 : 10 }]}>
                {/* <Text style={[styles.label, isRTL && styles.labelRTL]}>{t('farm.unit')}</Text> */}
                <DropdownPicker
                  label={t("common.selectUnit")}
                  options={landSizeUnitsArray}
                  onSelect={(val) => setSizeUnit(val as any)}
                  selectedValue={sizeUnit}
                  isMultiple={false}
                />
              </View>
            </View>

            <Input
              label={t('farm.location')}
              placeholder={t('farm.enterLocation')}
              value={location}
              onChangeText={(text) => {
                setLocation(text);
                if (fieldErrors.location) {
                  setFieldErrors(prev => ({ ...prev, location: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              leftIcon={<MapPin size={20} color={colors.gray[500]} />}
            // isRTL={isRTL}
              required={true}
              error={fieldErrors.location}
            />

            <Input
              label={t('farm.description')}
              placeholder={t('farm.enterDescription')}
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
            // isRTL={isRTL}
            />
          </View>

          <View style={[styles.buttonContainer, isRTL && styles.buttonContainerRTL]}>
            <Button
              title={t('common.cancel')}
              variant="outline"
              onPress={() => router.back()}
              style={[styles.cancelButton, { marginLeft: isRTL ? 0 : 20 }]}
              disabled={isLoading}
              isRTL={isRTL}
            />
            <Button
              title={isLoading ? t('farm.creating') : t('farm.create')}
              onPress={handleCreateFarm}
              style={[styles.createButton, { marginRight: isRTL ? 20 : 0 }]}
              disabled={isLoading}
              leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
              isRTL={isRTL}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
    textAlign: 'left',
  },
  labelRTL: {
    textAlign: 'right',
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dropdownHeaderRTL: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
  },
  dropdownText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
    textAlign: 'left',
  },
  dropdownTextRTL: {
    textAlign: 'right',
  },
  dropdownOptions: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownOption: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
    textAlign: 'left',
  },
  dropdownOptionTextRTL: {
    textAlign: 'right',
  },
  rowContainer: {
    justifyContent: "space-between",
    flexDirection: 'row',
    // marginBottom: 4,
  },
  rowContainerRTL: {
    flexDirection: 'row-reverse',
  },
  sizeContainer: {
    flex: 1,
    // marginRight: I18nManager.isRTL ? 0 : 8,
    // marginLeft: I18nManager.isRTL ? 8 : 0,
  },
  unitContainer: {
    flex: 1,
  },
  sizeInputContainer: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  sizeInput: {
    flex: 1,
  },
  unitDropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
  },
  unitDropdownContainerRTL: {
    flexDirection: 'row-reverse',
  },
  unitDropdownText: {
    fontSize: 14,
    color: colors.gray[800],
    textAlign: 'left',
  },
  unitDropdownTextRTL: {
    textAlign: 'right',
  },
  unitDropdownOptions: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: colors.gray[300],
    zIndex: 10,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  unitDropdownOption: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  unitDropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
    textAlign: 'left',
  },
  unitDropdownOptionTextRTL: {
    textAlign: 'right',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buttonContainerRTL: {
    flexDirection: 'row-reverse',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
    // width: '48%',
    // marginRight: I18nManager.isRTL ? 0 : 8,
    // marginLeft: I18nManager.isRTL ? 8 : 0,
  },
  createButton: {
    flex: 1,
    marginLeft: 8
    // width: '48%',
    // marginLeft: I18nManager.isRTL ? 0 : 8,
    // marginRight: I18nManager.isRTL ? 8 : 0,
  },
});