import { useCallback, useEffect, useState } from 'react';
import {
  changeLanguage,
  getCurrentLocale,
  isRTL as checkRTL,
  onLanguageChange,
  offLanguageChange,
  t as translate,
} from './index';
import AsyncStorage from '@react-native-async-storage/async-storage';

export function useTranslation() {
  const [locale, setLocale] = useState(getCurrentLocale());
  const [rtl, setRtl] = useState(checkRTL());

  // Initialize from AsyncStorage on first render
  useEffect(() => {
    const initFromStorage = async () => {
      try {
        const storedLocale = await AsyncStorage.getItem('appLanguage');
        if (storedLocale && storedLocale !== locale) {
          setLocale(storedLocale);
          setRtl(storedLocale === 'ur');
        }
      } catch (error) {
        console.error('Error reading language from AsyncStorage:', error);
      }
    };
    
    initFromStorage();
  }, []);

  // Listen for language changes
  useEffect(() => {
    const handleLanguageChange = (newLocale: string) => {
      setLocale(newLocale);
      setRtl(checkRTL());
    };

    onLanguageChange(handleLanguageChange);
    return () => offLanguageChange(handleLanguageChange);
  }, []);

  const t = useCallback(
    (key: string, fallback?: string | Record<string, any>) => {
      if (fallback && typeof fallback === 'object') {
        return translate(key, fallback) || key;
      }
      const result = translate(key);
      return result !== key ? result : fallback || key;
    },
    [] // no locale dependency needed anymore
  );

  const setLocaleAsync = useCallback(async (newLocale: string) => {
    try {
      // Store in AsyncStorage
      await AsyncStorage.setItem('appLanguage', newLocale);
      // Change language in i18n system
      await changeLanguage(newLocale);
    } catch (error) {
      console.error('Error setting language:', error);
    }
  }, []);

  return { t, locale, setLocale: setLocaleAsync, isRTL: rtl };
}

