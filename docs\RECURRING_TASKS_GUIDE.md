# Recurring Tasks System Guide

## Overview
The recurring tasks system allows users to create tasks that automatically generate multiple instances based on a specified frequency (daily, weekly, monthly) until a specified end date.

## How It Works

### 1. Creating a Recurring Task

When creating a task, users can:
1. Toggle the "Recurring" switch to enable recurring functionality
2. Select frequency: Daily, Weekly, or Monthly
3. Set a "Repeat Until" date to specify when the recurring should stop

### 2. Task Generation Process

When a recurring task is created:

```javascript
// Example: Daily task from Jan 1 to Jan 7
const baseTask = {
  title: "Water Plants",
  dueDate: "2024-01-01",
  frequency: "daily",
  repeatUntil: "2024-01-07"
}

// System generates:
// - Task 1: Due Jan 1, 2024
// - Task 2: Due Jan 2, 2024  
// - Task 3: Due Jan 3, 2024
// - Task 4: Due Jan 4, 2024
// - Task 5: Due Jan 5, 2024
// - Task 6: Due Jan 6, 2024
// - Task 7: Due Jan 7, 2024
```

### 3. Task Instance Properties

Each generated task instance has:
- `parentTaskId`: Links back to the original recurring task
- `isRecurringInstance`: Boolean flag indicating it's a recurring instance
- `dueDate`: Calculated based on frequency
- All other properties copied from the original task

### 4. Daily Task Display

Tasks appear in "Today's Tasks" based on their individual due dates:

**Today (Jan 3, 2024):**
- Shows the task instance with dueDate = "2024-01-03"
- Status can be: pending, in_progress, completed, overdue

**Tomorrow (Jan 4, 2024):**
- Shows the task instance with dueDate = "2024-01-04"
- Appears as a fresh "pending" task

### 5. Task Completion Behavior

**Individual Completion:**
- Each task instance is completed independently
- Completing today's instance doesn't affect tomorrow's instance
- Each instance maintains its own status, evidence, and completion data

**Progress Tracking:**
```
Water Plants (Recurring Task)
├── Jan 1: ✅ Completed
├── Jan 2: ✅ Completed  
├── Jan 3: 🔄 In Progress (Today)
├── Jan 4: ⏳ Pending
├── Jan 5: ⏳ Pending
├── Jan 6: ⏳ Pending
└── Jan 7: ⏳ Pending
```

### 6. Frequency Types

**Daily:**
- Creates one task for each day
- Next task = current date + 1 day

**Weekly:**
- Creates one task for each week
- Next task = current date + 7 days

**Monthly:**
- Creates one task for each month
- Next task = current date + 1 month

### 7. Database Structure

```javascript
// Original recurring task
{
  id: "task_123",
  title: "Water Plants",
  isRecurring: true,
  frequency: "daily",
  dueDate: "2024-01-01",
  repeatUntil: "2024-01-07"
}

// Generated instances
{
  id: "task_123_instance_1",
  title: "Water Plants",
  parentTaskId: "task_123",
  isRecurringInstance: true,
  dueDate: "2024-01-01",
  status: "completed"
}
{
  id: "task_123_instance_2", 
  title: "Water Plants",
  parentTaskId: "task_123",
  isRecurringInstance: true,
  dueDate: "2024-01-02",
  status: "pending"
}
```

### 8. User Experience

**Creating:**
1. User creates task "Water Plants"
2. Enables recurring toggle
3. Selects "Daily" frequency
4. Sets end date to next week
5. System generates 7 task instances

**Daily Usage:**
1. User opens app on Monday
2. Sees "Water Plants" in today's tasks
3. Completes the task with evidence
4. Task marked as completed

**Next Day:**
1. User opens app on Tuesday
2. Sees fresh "Water Plants" task for Tuesday
3. Previous day's completion is preserved
4. Can complete Tuesday's instance independently

### 9. Benefits

- **Consistency:** Ensures regular tasks are never forgotten
- **Flexibility:** Each instance can be completed independently
- **Tracking:** Full history of completion across all instances
- **Automation:** No manual task creation needed for routine work

### 10. Implementation Notes

- All recurring instances are created upfront when the original task is saved
- Each instance is a separate database document
- Filtering by date shows relevant instances for each day
- Completion status is tracked per instance, not per recurring task
