import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import MultipleImagePicker from '@/components/MultipleImagePicker';
import DatePicker from '@/components/DatePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import * as Location from 'expo-location';
import MapView, { Marker, Polygon } from 'react-native-maps';
import { reverseGeocodeAsync } from 'expo-location';
import {
  ChevronDown,
  Ruler,
  Home,
  Check,
  Map,
  MapPin,
} from 'lucide-react-native';
import { Farm } from '@/types';
import { analyzeImageWithVision } from '@/utils/openai-vision';
import { calculatePolygonArea } from '@/utils/map-utils';
import { v4 as uuidv4 } from 'uuid';
import * as DocumentPicker from 'expo-document-picker';
// import { unzip, subscribe } from 'react-native-zip-archive';
import * as FileSystem from 'expo-file-system';
import { unzipSync, strFromU8 } from 'fflate';
// import * as FileSystem from 'expo-file-system';
import { DOMParser } from 'xmldom';
import * as toGeoJSON from '@tmcw/togeojson';
import { useTranslation } from '@/i18n/useTranslation';
import {
  Upload,
  FileText,
  AlertCircle,
  Volume2
} from 'lucide-react-native';
import { isRTL } from '@/i18n';
import DropdownBottomSheet from '@/components/DropDownBottomSheet';
import {
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import DropdownPicker from '@/components/DropdownPicker';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import AntDesign from '@expo/vector-icons/AntDesign';
import PolygonModal from '@/components/PolygonModal';
import { useLookupStore } from '@/store/lookup-store';
import Toast from 'react-native-toast-message';
// Soil type options
const SOIL_TYPES = [
  'Clay',
  'Sandy',
  'Loam',
  'Silt',
  'Peat',
  'Chalk',
  'Sandy Loam',
  'Clay Loam',
  'Silty Clay',
  'Sandy Clay',
  'Other'
];

export default function CreateFieldScreen() {
  const { id } = useLocalSearchParams();
  const isEditMode = id ? true : false;
  const { t, locale, setLocale, isRTL } = useTranslation();

  const {
    addField,
    updateField,
    getField,
    currentFarm,
    farms,
    fetchFarms,
    setCurrentFarm,
    getFieldUpdated
  } = useFarmStore();
  const { getLookupsByCategory, getLookupsByCategoryParssedData } = useLookupStore();
  const [name, setName] = useState('');
  const [type, setType] = useState<string>('nSEVmRWD9OqffU6uwqPp');
  const [size, setSize] = useState('');
  const [sizeUnit, setSizeUnit] = useState<string>('ezVe7GdWq6SAxwaREyIw');
  const [status, setStatus] = useState<string>('b5LJdACC4zX5qvt328Sb');
  const [images, setImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [showTypeDropdown, setShowTypeDropdown] = useState(false);
  const [showSizeUnitDropdown, setShowSizeUnitDropdown] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showFarmModal, setShowFarmModal] = useState(false);

  // Add new state variables for location
  const [latitude, setLatitude] = useState('');
  const [longitude, setLongitude] = useState('');
  const [address, setAddress] = useState('');
  const [showMapModal, setShowMapModal] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    name: '',
    size: '',
    type: '',
    images: ''
  });
  const [initialRegion, setInitialRegion] = useState({
    latitude: 30.3753, // Default to center of Pakistan
    longitude: 69.3451,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  const [drawingMode, setDrawingMode] = useState(false);
  const [polygonPoints, setPolygonPoints] = useState<Array<{
    latitude: number;
    longitude: number;
  }>>([]);
  const [calculatedArea, setCalculatedArea] = useState(0);

  const { user } = useAuthStore();

  const [isImportingKMZ, setIsImportingKMZ] = useState(false);
  const [kmzError, setKmzError] = useState('');
  const [showVoiceInstructions, setShowVoiceInstructions] = useState(false);
  const [listofFieldbyKMZ, setListOfFieldbyKMZ] = useState([] as any)
  const [fieldTypeArray, setFieldTypeArray] = useState([] as any)
  const [areaSizeUnitArray, setAreaSizeUnitArray] = useState([] as any)
  const [statusArray, setStatusArray] = useState([] as any)

  useEffect(() => {
    setFieldTypeArray(getLookupsByCategoryParssedData('fieldType', 'entity.field.type'))
    setAreaSizeUnitArray(getLookupsByCategoryParssedData('areaUnit', 'common.areaUnit.'))
    setStatusArray(getLookupsByCategoryParssedData('status', 'entity.field.status'))
  }, [])

  function convertGeoJSONPolygonToCoordinates(feature) {
    if (
      feature.geometry.type === 'Polygon' &&
      Array.isArray(feature.geometry.coordinates)
    ) {
      // We take the first ring (outer boundary)
      const ring = feature.geometry.coordinates[0];

      return ring.map(([lng, lat]) => ({
        latitude: lat,
        longitude: lng,
      }));
    }

    return [];
  }
  function mapFeaturesToPolygons(features) {
    return features
      .filter(f => f.geometry?.type === 'Polygon')
      .map(feature => ({
        name: feature.properties?.name || 'Unnamed Area',
        description: feature.properties?.description || '',
        strokeColor: feature.properties?.stroke || '#0000FF',
        fillColor: feature.properties?.fill || 'rgba(0,0,255,0.2)',
        coordinates: convertGeoJSONPolygonToCoordinates(feature),
      }));
  }

  const pickKMZFile = async () => {
    try {
      setIsImportingKMZ(true);
      setKmzError('');

      // Step 1: Pick KMZ file
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/vnd.google-earth.kmz',
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return setIsImportingKMZ(false);
      }

      const fileUri = result.assets[0].uri;
      const fileName = result.assets[0].name;

      if (!fileName.toLowerCase().endsWith('.kmz')) {
        setKmzError(t('field.invalidFileType'));
        return setIsImportingKMZ(false);
      }

      // Step 2: Extract KML from KMZ
      const kmlString = await unzipKMZinExpo(fileUri);
      const kmlDom = new DOMParser().parseFromString(kmlString, 'text/xml');
      const geoJSON = toGeoJSON.kml(kmlDom);

      // Step 3: Convert to list of polygon objects
      if (!geoJSON.features || geoJSON.features.length === 0) {
        setKmzError(t('field.noFeaturesFound'));
        return;
      }

      const polygons = mapFeaturesToPolygons(geoJSON.features);
      if (!polygons || polygons.length === 0) {
        setKmzError(t('field.noPolygonFound'));
        return;
      }

      // Step 4: One polygon = auto-fill form & show map modal
      if (polygons.length === 1) {
        const selected = polygons[0];

        setPolygonPoints(selected.coordinates);
        setName(selected.name);
        // setDescription(selected.description || '');

        const areaInSqMeters = calculatePolygonArea(selected.coordinates);
        setCalculatedArea(areaInSqMeters);

        const areaInAcres = areaInSqMeters * 0.000247105;
        // console.log({ areaInAcres })
        setSize(areaInAcres.toFixed(2));
        setSizeUnit('acres');

        const centerLat = selected.coordinates.reduce((sum, p) => sum + p.latitude, 0) / selected.coordinates.length;
        const centerLng = selected.coordinates.reduce((sum, p) => sum + p.longitude, 0) / selected.coordinates.length;

        setLatitude(centerLat.toString());
        setLongitude(centerLng.toString());

        try {
          const location = await reverseGeocodeAsync({ latitude: centerLat, longitude: centerLng });
          if (location?.length > 0) {
            const loc = location[0];
            const addressParts = [loc.street, loc.city, loc.region, loc.country].filter(Boolean);
            setAddress(addressParts.join(', '));
          }
        } catch (error) {
          console.log('Reverse geocode failed:', error);
        }

        setInitialRegion({
          latitude: centerLat,
          longitude: centerLng,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        });
        setSelectedLocation({ latitude: centerLat, longitude: centerLng });
        setShowMapModal(true);
      } else {
        // Step 5: Multiple polygons = show selection modal
        setListOfFieldbyKMZ(polygons);
        setModalVisible(true);
      }

    } catch (error) {
      console.error('KMZ import error:', error);
      setKmzError(t('field.importError'));
    } finally {
      setIsImportingKMZ(false);
    }
  };

  const unzipKMZinExpo = async (uri) => {
    const fileData = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    const binary = Uint8Array.from(atob(fileData), c => c.charCodeAt(0));
    const unzipped = unzipSync(binary);
    // console.log(unzipped);

    // 1. Extract the filename and byte array
    const filenames = Object.keys(unzipped);
    // console.log({ filenames })

    const filename = Object.keys(unzipped)[0];
    const byteArray = unzipped[filename];

    // 2. Convert to Uint8Array
    const uint8Array = new Uint8Array(byteArray);

    // 3. Decode Uint8Array to a string (UTF-8)
    const kmlText = new TextDecoder().decode(uint8Array);
    if (kmlText.includes('<kml')) {
      return kmlText;
    } else {
      throw new Error("doc.kml not found in KMZ");
    }
    // if (unzipped['doc.kml']) {
    //   const kmlString = strFromU8(unzipped['doc.kml']);
    //   console.log('KML:', kmlString);
    //   return kmlString;
    // } else {
    //   throw new Error("doc.kml not found in KMZ");
    // }
  };
  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required to select field location');
        return;
      }

      try {
        const location = await Location.getCurrentPositionAsync({});
        const { latitude, longitude } = location.coords;
        setInitialRegion(prev => ({
          ...prev,
          latitude,
          longitude,
        }));
      } catch (error) {
        console.error('Error getting location:', error);
      }
    })();
  }, []);


  // Load field data if in edit mode
  const [field, setField] = useState({} as any)
  const loadFieldData = async (fieldIdString: string) => {
    const res = await getFieldUpdated(currentFarm?.id, fieldIdString)
    setField(res)
  }
  useEffect(() => {
    if (field) {
      setName(field.name || '');
      setType(field.type || 'cropland');
      setSize(field.size ? field.size.toString() : '');
      setSizeUnit(field.sizeUnit || 'acres');
      setStatus(field.status || 'active');
      // setCropType(field.cropType || '');
      // setSoilType(field.soilType || '');
      // setPlantedDate(field.plantedDate ? new Date(field.plantedDate) : null);
      // setHarvestDate(field.harvestDate ? new Date(field.harvestDate) : null);
      // setHealth(field.health || '');

      // Set location data if available
      if (field.location) {
        if (field.location.latitude) setLatitude(field.location.latitude.toString());
        if (field.location.longitude) setLongitude(field.location.longitude.toString());
        if (field.location.address) setAddress(field.location.address);
      }

      // Set polygon points if available
      if (field.boundary && field.boundary.length > 0) {
        setPolygonPoints(field.boundary);
        setCalculatedArea(field.calculatedArea || 0);
      }

      // Set image if available
      if (field.image) {
        setImageUri(field.image);
      }

      // Set farm if available
      if (field.farmId) {
        const fieldFarm = farms.find(f => f.id === field.farmId);
        if (fieldFarm) {
          setCurrentFarm(fieldFarm.id);
        }
      }
    }
  }, [field])
  useEffect(() => {
    if (isEditMode && id) {
      const fieldIdString = Array.isArray(id) ? id[0] : id as string;
      loadFieldData(fieldIdString);
    }
  }, [isEditMode, id, farms, getField]);

  const validateFields = () => {
    const errors = {
      name: '',
      size: '',
      type: '',
      images: ''
    };

    let hasErrors = false;

    if (!name.trim()) {
      errors.name = t('field.nameRequired');
      hasErrors = true;
    }

    if (!size || isNaN(Number(size)) || Number(size) <= 0) {
      errors.size = t('field.sizeRequired');
      hasErrors = true;
    }

    if (!type) {
      errors.type = t('field.typeRequired');
      hasErrors = true;
    }

    // Status is optional - no validation needed
    // Location is optional - no validation needed
    // Images are optional - no validation needed

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleCreateField = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    if (!currentFarm) {
      Alert.alert('Error', 'Please select a farm first');
      return;
    }

    try {
      setIsLoading(true);

      // Upload images if selected
      let imageUrls: string[] = [];
      if (images.length > 0) {
        for (const imageUri of images) {
          if (imageUri && !imageUri.startsWith('http')) {
            const uploadedUrl = await uploadImageAsync(imageUri, 'fields');
            imageUrls.push(uploadedUrl);
          } else if (imageUri) {
            imageUrls.push(imageUri);
          }
        }
      }

      // Use first image as main image
      const mainImage = imageUrls.length > 0 ? imageUrls[0] : '';

      const fieldData: any = {
        id: isEditMode ? (Array.isArray(id) ? id[0] : id as string) : uuidv4(),
        name,
        type,
        size: Number(calculatedArea) || Number(size),
        sizeUnit,
        status,
        farmId: currentFarm.id,
        image: mainImage,
        images: imageUrls,
        updatedAt: new Date(),
      };

      // Only add createdAt for new fields
      if (!isEditMode) {
        fieldData.createdAt = new Date();
      }

      // Add location data if available


      if (latitude && longitude) {
        fieldData.location = {
          latitude: parseFloat(latitude),
          longitude: parseFloat(longitude),
          address: address || '',
          boundaries: polygonPoints,
          // boundarySource = 'kmz_import';
        };

        // Add boundary data if available
        if (polygonPoints.length > 0) {
          fieldData.boundary = polygonPoints;
          fieldData.calculatedArea = calculatedArea;

          // Add center point for easier querying
          const centerLat = polygonPoints.reduce((sum, point) => sum + point.latitude, 0) / polygonPoints.length;
          const centerLng = polygonPoints.reduce((sum, point) => sum + point.longitude, 0) / polygonPoints.length;

          fieldData.centerPoint = {
            latitude: centerLat,
            longitude: centerLng
          };

        }
        // Add source information
        fieldData.boundarySource = 'kmz_import';
      }

      // Only add these fields if they have values
      // if (cropType) fieldData.cropType = cropType;
      // if (soilType) fieldData.soilType = soilType;
      // if (plantedDate) fieldData.plantedDate = plantedDate.toISOString();
      // if (harvestDate) fieldData.harvestDate = harvestDate.toISOString();
      // if (health) fieldData.health = health;

      if (isEditMode) {
        await updateField(fieldData.id, fieldData);
        Alert.alert('Success', 'Field updated successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {

        // console.log('fieldData', fieldData);
        await addField(fieldData);
        Alert.alert('Success', 'Field created successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      }
    } catch (error: any) {
      console.error('Error with field:', error);
      Alert.alert('Error', error.message || `Failed to ${isEditMode ? 'update' : 'create'} field. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  const renderFarmItem = ({ item }: { item: Farm }) => (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => {
        setCurrentFarm(item.id);
        setShowFarmModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <Home size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item.name}</Text>
      </View>
      {currentFarm?.id === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  // Handle image analysis results
  const handleAnalysisComplete = (analysis: any) => {
    if (!analysis) return;

    // Apply results if it's a field or crop
    if (analysis.category === 'garden' || analysis.type.toLowerCase().includes('field')) {
      // Set field name if empty
      if (!name && analysis.type) {
        setName(analysis.type.charAt(0).toUpperCase() + analysis.type.slice(1));
      }

      // Set field type based on analysis
      if (analysis.details) {
        const details = analysis.details.toLowerCase();

        if (details.includes('crop') || details.includes('wheat') || details.includes('corn') || details.includes('rice')) {
          setType('cropland');

          // Try to extract crop type
          const cropMatches = details.match(/(wheat|corn|rice|barley|soybean|cotton|potato|tomato)/i);
          if (cropMatches) {
            setCropType(cropMatches[0].charAt(0).toUpperCase() + cropMatches[0].slice(1));
          }
        } else if (details.includes('orchard') || details.includes('fruit') || details.includes('tree')) {
          setType('orchard');
        } else if (details.includes('animal') || details.includes('livestock') || details.includes('grazing')) {
          setType('livestock');
        } else if (details.includes('garden') || details.includes('vegetable')) {
          setType('garden');
        }

        // Set soil type if detected
        for (const soil of SOIL_TYPES) {
          if (details.includes(soil.toLowerCase())) {
            setSoilType(soil);
            break;
          }
        }

        // Set status based on details
        if (details.includes('fallow') || details.includes('resting')) {
          setStatus('fallow');
        } else if (details.includes('maintenance') || details.includes('preparing')) {
          setStatus('maintenance');
        } else {
          setStatus('active');
        }

        // Set health if mentioned
        if (details.includes('health')) {
          const healthMatch = details.match(/(\d+)%\s*health/i);
          if (healthMatch) {
            setHealth(healthMatch[1]);
          }
        }
      }
    }
  };

  useEffect(() => {
    // if (polygonPoints.length > 0) {
    // console.log('Polygon points:', polygonPoints);c
    // }
  }, [polygonPoints]);

  // Add map press handler
  const handleMapPress = async (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;

    if (drawingMode) {
      setPolygonPoints(prev => [...prev, { latitude, longitude }]);

      // If we have at least 3 points, calculate area
      if (polygonPoints.length >= 2) {
        const tempPoints = [...polygonPoints, { latitude, longitude }];
        const areaInSqMeters = calculatePolygonArea(tempPoints);
        setCalculatedArea(areaInSqMeters);

        // Automatically update the size field
        const areaInAcres = areaInSqMeters * 0.000247105; // convert to acres
        // console.log({ areaInAcres })
        setSize(areaInAcres.toFixed(2));
        setSizeUnit('acres');
      }
    } else {
      setSelectedLocation({ latitude, longitude });
      try {
        const response = await reverseGeocodeAsync({ latitude, longitude });
        if (response && response[0]) {
          const location = response[0];
          const addressComponents = [
            location.name,
            location.street,
            location.district,
            location.city,
            location.region,
            location.country
          ].filter(Boolean);

          const formattedAddress = addressComponents.join(', ');
          setAddress(formattedAddress);
        }
      } catch (error) {
        console.error('Error getting address:', error);
      }

      setLatitude(latitude.toString());
      setLongitude(longitude.toString());
    }
  };

  const completePolygon = () => {
    if (polygonPoints.length >= 3) {
      setDrawingMode(false);
      // First point becomes the last point to close the polygon

      // console.log(polygonPoints)
      setPolygonPoints(prev => [...prev, prev[0]]);
    } else {
      Alert.alert('Error', 'Please mark at least 3 points to create a field boundary');
    }
  };

  const clearPolygon = () => {
    setPolygonPoints([]);
    setCalculatedArea(0);
    setDrawingMode(false);
  };

  // const FieldType = [
  //   {
  //     icon: <MaterialIcons name="crop-landscape" size={24} color="black" />,
  //     label: t('entity.field.typeCropland'),
  //     value: 'cropland',
  //   },
  //   {
  //     icon: <MaterialIcons name="park" size={24} color="black" />,
  //     label: t('entity.field.typeGarden'),
  //     value: 'garden',
  //   },
  //   {
  //     icon: <MaterialIcons name="local-florist" size={24} color="black" />,
  //     label: t('entity.field.typeOrchard'),
  //     value: 'orchard',
  //   },
  //   {
  //     icon: <MaterialIcons name="pets" size={24} color="black" />,
  //     label: t('entity.field.typeLivestock'),
  //     value: 'livestock',
  //   },
  // ]

  // const Unit = [
  //   {
  //     label: t('entity.field.unitAcres'),
  //     value: 'acres',
  //     icon: <MaterialIcons name="square-foot" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.field.unitHectares'),
  //     value: 'hectares',
  //     icon: <MaterialIcons name="landscape" size={24} color="black" />,
  //   },
  // ];

  const Unit = [
    {
      label: t("common.unitMarlaPK"),
      value: "marla_pk",
      equivalent: "25.2929 m² or 272.25 ft²"
    },
    {
      label: t("common.unitKanalPK"),
      value: "kanal_pk",
      equivalent: "505.857 m² (20 Marla)"
    },
    {
      label: t("common.unitAcrePK"),
      value: "acre_pk",
      equivalent: "4,046.86 m²"
    },
    {
      label: t("common.unitSqFt"),
      value: "sqft",
      equivalent: "0.0929 m²"
    },
    {
      label: t("common.unitSqYd"),
      value: "sqyd",
      equivalent: "0.8361 m²"
    },
    {
      label: t("common.unitAcreUS"),
      value: "acre_us",
      equivalent: "4,840 yd² or 43,560 ft²"
    },
    {
      label: t("common.unitSqMile"),
      value: "sqmile",
      equivalent: "640 acres = 2.59 km²"
    }
  ];

  const SoilType = [
    { label: 'Clay', value: 'clay', icon: <MaterialIcons name="landscape" size={24} color="black" /> },
    { label: 'Sandy', value: 'sandy', icon: <MaterialIcons name="beach-access" size={24} color="black" /> },
    { label: 'Loam', value: 'loam', icon: <MaterialIcons name="grass" size={24} color="black" /> },
    { label: 'Silt', value: 'silt', icon: <MaterialIcons name="waves" size={24} color="black" /> },
    { label: 'Peat', value: 'peat', icon: <MaterialIcons name="compost" size={24} color="black" /> },
    { label: 'Chalk', value: 'chalk', icon: <MaterialIcons name="terrain" size={24} color="black" /> },
    { label: 'Sandy Loam', value: 'sandyLoam', icon: <MaterialIcons name="filter-sand" size={24} color="black" /> },
    { label: 'Clay Loam', value: 'clayLoam', icon: <MaterialIcons name="foundation" size={24} color="black" /> },
    { label: 'Silty Clay', value: 'siltyClay', icon: <MaterialIcons name="opacity" size={24} color="black" /> },
    { label: 'Sandy Clay', value: 'sandyClay', icon: <MaterialIcons name="layers" size={24} color="black" /> },
    { label: 'Other', value: 'other', icon: <MaterialIcons name="more-horiz" size={24} color="black" /> },

  ]
  // Add missing state and handler for PolygonModal
  const [modalVisible, setModalVisible] = useState(false);
  const [polygonList, setPolygonList] = useState<any[]>([]);
  const handleSelect = async (polygon: any) => {
    // Implement your logic for selecting a polygon here
    // console.log({ polygon })
    setPolygonPoints(polygon?.coordinates);

    setName(polygon?.name);
    // setDescription(polygon?.description || '');

    const areaInSqMeters = calculatePolygonArea(polygon?.coordinates);
    setCalculatedArea(areaInSqMeters);

    // const areaInAcres = areaInSqMeters * 0.000247105;
    // console.log(areaInSqMeters.toFixed(2))
    setSize(areaInSqMeters.toFixed(2));
    setSizeUnit('acres');

    const centerLat = polygon?.coordinates.reduce((sum, p) => sum + p.latitude, 0) / polygon?.coordinates.length;
    const centerLng = polygon?.coordinates.reduce((sum, p) => sum + p.longitude, 0) / polygon?.coordinates.length;

    setLatitude(centerLat.toString());
    setLongitude(centerLng.toString());



    setInitialRegion({
      latitude: centerLat,
      longitude: centerLng,
      latitudeDelta: 0.09,
      longitudeDelta: 0.09,
    });
    setSelectedLocation({ latitude: centerLat, longitude: centerLng });
    setShowMapModal(true);


    setModalVisible(false);


    try {
      const location = await reverseGeocodeAsync({ latitude: centerLat, longitude: centerLng });
      if (location?.length > 0) {
        const loc = location[0];
        const addressParts = [loc.street, loc.city, loc.region, loc.country].filter(Boolean);
        setAddress(addressParts.join(', '));
      }
    } catch (error) {
      console.log('Reverse geocode failed:', error);
    }
  };
  // const
  return (
    <>
      <Stack.Screen
        options={{
          title: isEditMode ? t('entity.field.edit') : t('add.field'),
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Farm Selection */}
          {farms.length > 0 && (
            <View style={[styles.farmSelectionContainer]}>
              <Text style={[styles.farmSelectionLabel, { textAlign: isRTL ? "right" : 'left' }]}>{t('farm.selectfarm')}</Text>
              <TouchableOpacity
                style={[styles.farmSelectionButton, { flexDirection: isRTL ? "row-reverse" : 'row' }]}
                onPress={() => setShowFarmModal(true)}
              >
                <Home size={20} color={colors.gray[500]} style={styles.farmIcon} />
                <Text style={[styles.farmSelectionText, { textAlign: isRTL ? "right" : 'left', marginRight: isRTL ? 8 : 0 }]}>
                  {currentFarm?.name || "Select a farm"}
                </Text>
                <ChevronDown size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            </View>
          )}
          <MultipleImagePicker
            label={t('field.images')}
            images={images}
            onImagesChange={(newImages) => {
              setImages(newImages);
              if (fieldErrors.images) {
                setFieldErrors(prev => ({ ...prev, images: '' }));
              }
            }}
            maxImages={3}
            placeholder={t('field.addPhoto')}
            error={fieldErrors.images}
          />

          <View style={styles.kmzImportContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{t('field.importBoundary')}</Text>
              <TouchableOpacity
                style={styles.voiceInstructionButton}
                onPress={() => setShowVoiceInstructions(!showVoiceInstructions)}
              >
                <Volume2 size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>

            {showVoiceInstructions && (
              <View style={styles.voiceInstructionsContainer}>
                <Text style={styles.voiceInstructionsText}>
                  {t('field.voiceInstructions')}
                </Text>
              </View>
            )}

            <TouchableOpacity
              style={styles.kmzImportButton}
              onPress={pickKMZFile}
              disabled={isImportingKMZ}
            >
              {isImportingKMZ ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <>
                  <Upload size={20} color={colors.white} style={styles.kmzImportIcon} />
                  <Text style={styles.kmzImportText}>{t('field.uploadKMZ')}</Text>
                </>
              )}
            </TouchableOpacity>

            {kmzError ? (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={colors.error} />
                <Text style={styles.errorText}>{kmzError}</Text>
              </View>
            ) : null}

            {polygonPoints.length > 0 && (
              <View style={styles.importSuccessContainer}>
                <FileText size={16} color={colors.success} />
                <Text style={styles.importSuccessText}>
                  {t('field.boundaryImported')}
                </Text>
                <TouchableOpacity
                  style={styles.editBoundaryButton}
                  onPress={() => {
                    setDrawingMode(true);
                    setShowMapModal(true);
                  }}
                >
                  <Text style={styles.editBoundaryText}>
                    {t('field.editBoundary')}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>

          <View style={styles.formContainer}>
            <Input
              label={t('entity.field.name')}
              placeholder={t('entity.field.namePlaceholder')}
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (fieldErrors.name) {
                  setFieldErrors(prev => ({ ...prev, name: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              required={true}
              error={fieldErrors.name}
            />

            {/* <Text style={[styles.label, { textAlign: isRTL ? "right" : 'left' }]}>{t('entity.field.type')}</Text> */}

            <DropdownPicker
              label={t('entity.field.type')}
              options={fieldTypeArray}
              onSelect={(val) => {
                setType(val as any);
                if (fieldErrors.type) {
                  setFieldErrors(prev => ({ ...prev, type: '' }));
                }
              }}
              selectedValue={type}
              isMultiple={false}
              required={true}
              error={fieldErrors.type}
            />

            <View style={styles.rowContainer}>
              <View style={styles.sizeContainer}>
                <Text style={[styles.label, { textAlign: isRTL ? "right" : 'left' }]}>{t('entity.field.size')}</Text>
                <View style={styles.sizeInputContainer}>
                  <Input
                    placeholder="0.0"
                    value={size}
                    onChangeText={(text) => {
                      setSize(text);
                      if (fieldErrors.size) {
                        setFieldErrors(prev => ({ ...prev, size: '' }));
                      }
                    }}
                    keyboardType="numeric"
                    containerStyle={styles.sizeInput}
                    leftIcon={<Ruler size={20} color={colors.gray[500]} />}
                    required={true}
                    error={fieldErrors.size}
                  />
                </View>
              </View>

              <View style={styles.unitContainer}>
                {/* <Text style={[styles.label, { textAlign: isRTL ? "right" : 'left' }]}>{t('entity.field.sizeUnit')}</Text> */}
                <DropdownPicker
                  label={t('entity.field.sizeUnit')}
                  options={areaSizeUnitArray}
                  onSelect={(val) => setSizeUnit(val as any)}
                  selectedValue={sizeUnit}
                  isMultiple={false}
                />
              </View>
            </View>

            {/* <Text style={[styles.label, { textAlign: isRTL ? "right" : 'left' }]}>{t('entity.field.status')}</Text> */}
            <DropdownPicker
              label={t('entity.field.status')}
              options={statusArray}
              onSelect={(val) => setStatus(val as any)}
              selectedValue={status}
              isMultiple={false}
            />



            {/* Add Location Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>{t('field.location')}</Text>
              <View style={styles.locationSection}>
                <View style={styles.coordinatesContainer}>
                  <Input
                    label={t('entity.field.latitude')}
                    placeholder={t('entity.field.latitudePlaceholder')}
                    value={latitude}
                    onChangeText={setLatitude}
                    keyboardType="numeric"
                    containerStyle={[styles.coordinateInput, { marginRight: 8 }]}
                  />

                  <Input
                    label={t('entity.field.longitude')}
                    placeholder={t('entity.field.longitudePlaceholder')}
                    value={longitude}
                    onChangeText={setLongitude}
                    keyboardType="numeric"
                    containerStyle={styles.coordinateInput}
                  />
                </View>

                <Input
                  label={t('entity.field.address')}
                  placeholder={t('entity.field.addressPlaceholder')}
                  value={address}
                  onChangeText={setAddress}
                  containerStyle={styles.inputContainer}
                  leftIcon={<MapPin size={20} color={colors.gray[500]} />}
                />

                <TouchableOpacity
                  style={styles.mapButton}
                  onPress={() => setShowMapModal(true)}
                >
                  <Map size={20} color={colors.white} style={styles.mapButtonIcon} />
                  <Text style={styles.mapButtonText}>{t('entity.field.selectOnMap')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>



          <View style={styles.buttonContainer}>
            <Button
              title={t('common.cancel')}
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? (isEditMode ? "Updating..." : "Creating...") : (isEditMode ? t('entity.field.updateField') : t('entity.field.createField'))}
              onPress={handleCreateField}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
            />
          </View>
        </ScrollView>

        {/* Farm Selection Modal */}
        <Modal
          visible={showFarmModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFarmModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Farm</Text>
                <TouchableOpacity onPress={() => setShowFarmModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {farms.length > 0 ? (
                <FlatList
                  data={farms}
                  renderItem={renderFarmItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No farms available. Please create a farm first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>

        {/* Map Modal */}
        <Modal
          visible={showMapModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowMapModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.mapModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {drawingMode ? 'Draw Field Boundary' : 'Select Location'}
                </Text>
                <View style={styles.modalHeaderButtons}>
                  {drawingMode && (
                    <>
                      <TouchableOpacity
                        style={styles.modalHeaderButton}
                        onPress={completePolygon}
                      >
                        <Text style={styles.modalHeaderButtonText}>Complete</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.modalHeaderButton}
                        onPress={clearPolygon}
                      >
                        <Text style={styles.modalHeaderButtonText}>Clear</Text>
                      </TouchableOpacity>
                    </>
                  )}
                  <TouchableOpacity
                    style={[styles.modalHeaderButton, styles.modalCloseButton]}
                    onPress={() => setShowMapModal(false)}
                  >
                    <Text style={styles.modalCloseText}>Done</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.mapContainer}>
                <MapView
                  style={styles.map}
                  initialRegion={initialRegion}
                  onPress={handleMapPress}
                  mapType="hybrid"
                >
                  {!drawingMode && selectedLocation && (
                    <Marker
                      coordinate={{
                        latitude: selectedLocation.latitude,
                        longitude: selectedLocation.longitude,
                      }}
                      pinColor={colors.primary}
                    />
                  )}
                  {polygonPoints.length >= 3 && (
                    <Polygon
                      coordinates={polygonPoints}
                      strokeColor={colors.primary}
                      fillColor={`${colors.primary}50`}
                      strokeWidth={2}
                    />
                  )}
                  {polygonPoints.map((point, index) => (
                    <Marker
                      key={index}
                      coordinate={point}
                      pinColor={colors.primary}
                      anchor={{ x: 0.5, y: 0.5 }}
                    />
                  ))}
                </MapView>

                <View style={styles.mapControls}>
                  <TouchableOpacity
                    style={[
                      styles.mapControlButton,
                      drawingMode && styles.mapControlButtonActive
                    ]}
                    onPress={() => setDrawingMode(!drawingMode)}
                  >
                    <Ruler size={24} color={drawingMode ? colors.white : colors.primary} />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.mapControlButton}
                    onPress={async () => {
                      try {
                        const location = await Location.getCurrentPositionAsync({});
                        const { latitude, longitude } = location.coords;
                        setInitialRegion(prev => ({
                          ...prev,
                          latitude,
                          longitude,
                        }));
                      } catch (error) {
                        console.error('Error getting current location:', error);
                      }
                    }}
                  >
                    <MapPin size={24} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.selectedLocationInfo}>
                {drawingMode ? (
                  <Text style={styles.selectedLocationText}>
                    {polygonPoints.length === 0
                      ? 'Tap on the map to start drawing field boundary'
                      : polygonPoints.length < 3
                        ? `Marked ${polygonPoints.length} points. Need at least 3 points.`
                        : `Area: ${(calculatedArea * 0.000247105).toFixed(2)} acres`}
                  </Text>
                ) : (
                  <Text style={styles.selectedLocationText}>
                    {selectedLocation
                      ? `Selected: ${selectedLocation.latitude.toFixed(6)}, ${selectedLocation.longitude.toFixed(6)}`
                      : 'Tap on the map to select location'}
                  </Text>
                )}
              </View>
            </View>
          </View>
        </Modal>
        <PolygonModal
          // visible={modalVisible}
          // onClose={() => setModalVisible(false)}
          // polygons={listofFieldbyKMZ}
          // onSelect={handleSelect}
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          polygons={listofFieldbyKMZ}
          onSelect={(polygon) => {
            // console.log()
            handleSelect(polygon)
          }}
        />
      </SafeAreaView>
      {/* */}
    </>

    // </BottomSheetModalProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dropdownText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  dropdownOptions: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxHeight: 200,
  },
  dropdownOption: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  rowContainer: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  sizeContainer: {
    flex: 2,
    marginRight: 8,
  },
  unitContainer: {
    flex: 1,
  },
  sizeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sizeInput: {
    flex: 1,
  },
  unitDropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
  },
  unitDropdownText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  unitDropdownOptions: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: colors.gray[300],
    zIndex: 10,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  unitDropdownOption: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  unitDropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 16,
  },
  dateIcon: {
    marginRight: 8,
  },
  dateText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  emptyListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  farmSelectionContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmSelectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  farmSelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  farmIcon: {
    marginRight: 8,
  },
  farmSelectionText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  // Add new styles for location section
  locationSection: {
    // gap: 16,
  },

  coordinatesContainer: {
    flexDirection: 'row',
    gap: 8,
  },

  coordinateInput: {
    flex: 1,
  },

  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },

  mapButtonText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 16,
  },

  mapButtonIcon: {
    marginRight: 4,
  },

  // // Modal styles
  // modalOverlay: {
  //   flex: 1,
  //   backgroundColor: 'rgba(0, 0, 0, 0.5)',
  // },

  mapModalContent: {
    flex: 1,
    backgroundColor: colors.white,
    marginTop: 50,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },

  // modalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   padding: 16,
  //   borderBottomWidth: 1,
  //   borderBottomColor: colors.gray[200],
  // },

  // modalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[900],
  // },

  modalHeaderButtons: {
    flexDirection: 'row',
    gap: 12,
  },

  modalHeaderButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: colors.primary,
  },

  modalHeaderButtonText: {
    color: colors.white,
    fontWeight: '500',
  },

  modalCloseButton: {
    backgroundColor: colors.gray[200],
  },

  // modalCloseText: {
  //   color: colors.gray[700],
  // },

  mapContainer: {
    flex: 1,
    position: 'relative',
  },

  map: {
    flex: 1,
  },

  currentLocationButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: colors.white,
    padding: 12,
    borderRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  selectedLocationInfo: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },

  selectedLocationText: {
    color: colors.gray[700],
    textAlign: 'center',
  },
  mapControls: {
    position: 'absolute',
    right: 16,
    top: 16,
    gap: 8,
  },
  mapControlButton: {
    backgroundColor: colors.white,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  mapControlButtonActive: {
    backgroundColor: colors.primary,
  },
  kmzImportContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: isRTL() ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  voiceInstructionButton: {
    padding: 4,
  },
  voiceInstructionsContainer: {
    backgroundColor: colors.gray[100],
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  voiceInstructionsText: {
    fontSize: 14,
    color: colors.gray[700],
    textAlign: isRTL() ? 'right' : 'left',
  },
  kmzImportButton: {
    flexDirection: isRTL() ? 'row-reverse' : 'row',
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  kmzImportIcon: {
    marginRight: isRTL() ? 0 : 8,
    marginLeft: isRTL() ? 8 : 0,
  },
  kmzImportText: {
    color: colors.white,
    fontWeight: '600',
    fontSize: 16,
  },
  errorContainer: {
    flexDirection: isRTL() ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 8,
    backgroundColor: colors.error + '10',
    borderRadius: 4,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    marginLeft: isRTL() ? 0 : 8,
    marginRight: isRTL() ? 8 : 0,
    textAlign: isRTL() ? 'right' : 'left',
  },
  importSuccessContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: colors.success + '10',
    borderRadius: 8,
    flexDirection: isRTL() ? 'row-reverse' : 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  importSuccessText: {
    color: colors.success,
    fontSize: 14,
    marginLeft: isRTL() ? 0 : 8,
    marginRight: isRTL() ? 8 : 0,
    flex: 1,
    textAlign: isRTL() ? 'right' : 'left',
  },
  editBoundaryButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.primary,
    marginTop: 8,
  },
  editBoundaryText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
});
