export type UserRole = 'owner' | 'admin' | 'caretaker';

// Add new interface for harvest records
export interface HarvestRecord {
  id: string;
  harvestDate: string;
  season: string;
  yieldQuantity: number;
  yieldUnit: string;
  cropType: string;
  notes?: string;
  images?: string[];
  fieldId: string;
  createdAt: string;
  createdBy: string;
}
export interface InactiveStatus {
  isInactive: boolean;
  inactiveReason?: string;
  inactiveDate?: string;
  inactiveNotes?: string;
  inactiveImage?: string;
  markedByUserId?: string;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  displayName?: string;
  role: UserRole;
  avatar?: string;
  photoURL?: string;
  farmId?: string;
  phone?: string;
  phoneNumber?: string;
  preferredLanguage?: string;
  // preferredLanguage?: string;
  isTemporaryPassword?: boolean;
  assignedBy?: string;
  assignedAt?: string;
  status?: 'active' | 'inactive';
  assignedFarmIds?: string[]; // Array of farm IDs the user has access to
}

export interface UserType {
  id: string;
  name?: string;
  displayName?: string;
  email: string;
  role: string;
  status: string;
  photoURL?: string;
}

export interface Farm {
  id: string;
  name: string;
  description?: string;
  location?: string;
  size?: number;
  sizeUnit?: 'acres' | 'hectares';
  type?: 'crop' | 'livestock' | 'mixed' | 'orchard';
  ownerId?: string;
  status?: 'active' | 'inactive';
  image?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Field {
  id: string;
  name: string;
  type?: string;
  size?: number;
  sizeUnit?: string;
  status?: string;
  farmId: string;
  location?: any;
  image?: string;
  isInactive?: boolean;
  inactiveReason?: string;
  inactiveDate?: string;
  inactiveNotes?: string;
  inactiveImage?: string;
  markedByUserId?: string;
  activeCropId?: string; // Reference to active crop
  harvestHistory?: HarvestRecord[];
}

export interface Garden {
  id: string;
  name: string;
  type?: string;
  size?: number;
  sizeUnit?: string;
  location?: any;
  status?: string;
  soilType?: string;
  irrigationSystem?: string;
  farmId: string;
  createdAt?: string;
  updatedAt?: string;
  image?: string;
  // existing fields...
  isInactive?: boolean;
  inactiveReason?: string;
  inactiveDate?: string;
  inactiveNotes?: string;
  inactiveImage?: string;
  markedByUserId?: string;
  gardenType?:string;
}

export interface Plant {
  id: string;
  name: string;
  species?: string;
  variety?: string;
  plantedDate?: string;
  expectedHarvestDate?: string;
  status?: string;
  health?: string;
  gardenId?: string;
  fieldId?: string;
  farmId: string;
  location?: any;
  notes?: string;
  image?: string;
  createdAt?: string;
  updatedAt?: string;
  isInactive?: boolean;
  inactiveReason?: string;
  inactiveDate?: string;
  inactiveNotes?: string;
  inactiveImage?: string;
  markedByUserId?: string;
  identificationID?: string;
  healthCheck?: any; // Latest health check data
  healthChecks?: any[]; // All health checks for history
}

export interface Animal {
  id: string;
  name?: string;
  species: string;
  breed?: string;
  birthDate?: string;
  gender?: string;
  status?: string;
  purpose?: string;
  identificationNumber?: string;
  farmId: string;
  fieldId?: string;
  notes?: string;
  image?: string;
  createdAt?: string;
  updatedAt?: string;

  // existing fields...
  isInactive?: boolean;
  inactiveReason?: string;
  inactiveDate?: string;
  inactiveNotes?: string;
  inactiveImage?: string;
  markedByUserId?: string;
  healthChecks?:any;
}

export interface Equipment {
  id: string;
  name: string;
  type?: string;
  manufacturer?: string;
  model?: string;
  purchaseDate?: string;
  purchasePrice?: number;
  status?: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  farmId: string;
  location?: string;
  notes?: string;
  image?: string;
  createdAt?: string;
  updatedAt?: string;
  // existing fields...
  isInactive?: boolean;
  inactiveReason?: string;
  inactiveDate?: string;
  inactiveNotes?: string;
  inactiveImage?: string;
  markedByUserId?: string;
}

export interface Task {
  checklistDetails: any;
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: string;
  assignedTo?: string;
  assignedBy?: string;
  assignedToName?: string; // Add this field
  assignedByName?: string; // Add this field
  fieldId?: string;
  gardenId?: string;
  plantId?: string;
  animalId?: string;
  equipmentId?: string;
  farmId: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  category?: string;
  checklist?: ChecklistItem[];
  evidence?: {
    notes: any;
    required: boolean;
    images: string[];
  };
  frequency?: 'once' | 'daily' | 'weekly' | 'monthly' | 'custom';
  repeatUntil?: string;
  relatedItems?: string[];
  steps?: string[];
  isRecurring?: boolean;
}

export interface ChecklistItem {
  id?: string;
  title: string;
  value: string;
  description?: string;
  completed: boolean;
  required?: boolean;
}
export interface EquipmentChecklistItem  {
  id: string;
  title: string;
  value: string;
  completed: boolean;
  image?: string;
};
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  status: string;
  farmId: string;
  acquisitionDate?: string;
  acquisitionCost?: number;
  location?: string;
  notes?: string;
  image?: string;
}

export interface WeatherForecast {
  date: string;
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
}

export interface Yield {
  id: string;
  name: string;
  cropType: string;
  harvestDate: string;
  quantity: number;
  unit: string;
  quality: string;
  fieldId?: string;
  gardenId?: string;
  farmId: string;
  notes?: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FarmPermission {
  id: string;
  farmId: string;
  userId: string;
  role: UserRole;
  status: 'active' | 'pending' | 'inactive';
  assignedBy: string;
  assignedAt: string;
  areas: string[];
}

export interface Notification {
  id: string;
  userId: string;
  farmId?: string;
  type: string;
  title: string;
  message: string;
  createdAt: string;
  read: boolean;
  data?: any;
}

// New interface for crop
export interface Crop {
  id: string;
  fieldId: string;
  cropType: string;
  status: 'active' | 'harvested' | 'failed';
  plantedDate: string;
  expectedHarvestDate?: string;
  actualHarvestDate?: string;
  notes?: string;
  images?: string[];
  soilType?: string;
  health?: string;
  createdAt: string;
  updatedAt: string;
  tasks?: string[]; // References to related tasks
  healthChecks?: any[];
}

// Add new transaction-related types

export type TransactionCategory =
  | 'seed_cost'
  | 'irrigation'
  | 'pesticide'
  | 'fertilizer'
  | 'labor'
  | 'equipment'
  | 'harvest_sale'
  | 'other';

export interface Transaction {
  id: string;
  description: string;
  amount: number;
  currency: string;
  category: TransactionCategory;
  date: string;
  receiptImage?: string;
  tags?: string[];
  farmId: string;
  createdBy: string;
  isExpense: boolean;
  linkedItemId: string;
  linkedItemType: 'plant' | 'garden' | 'field';
  createdAt: string;
  updatedAt: string;
}

export interface CropFinancials {
  cropId: string;
  fieldId: string;
  totalCosts: number;
  totalRevenue: number;
  expectedRevenue: number;
  revenuePerAcre: number;
  roi: number;
  costBreakdown: {
    [key in TransactionCategory]?: number;
  };
  inconsistencies?: {
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[];
}

// Animal cost tracking
export interface AnimalCost {
  id?: string;
  animal_id: string;
  cost_type: string;
  amount: number;
  date: string;
  notes?: string;
  farmId: string;
  createdAt?: string;
  createdBy?: string;
}

// Animal milk record
export interface AnimalMilkRecord {
  id?: string;
  animal_id: string;
  morning_milk: number;
  evening_milk: number;
  date: string;
  notes?: string;
  farmId: string;
  createdAt?: string;
  createdBy?: string;
}

// Animal cleanliness record
export interface AnimalCleanliness {
  id?: string;
  animal_id: string;
  bath_given: boolean;
  shelter_cleaned: boolean;
  grooming_done: boolean;
  date: string;
  remarks?: string;
  farmId: string;
  createdAt?: string;
  createdBy?: string;
}



// Add these types to your existing types
export interface FinanceRecord {
  id: string;
  farmId: string;
  entityType: string;
  entityId: string;
  transactionType: 'income' | 'expense';
  category: string;
  amount: number;
  currency: string;
  date: Timestamp;
  notes?: string;
  createdBy: string;
  createdAt: Timestamp;
}

export interface FinanceSummary {
  totalIncome: number;
  totalExpense: number;
  netBalance: number;
}

export interface MonthlyFinanceData {
  labels: string[];
  income: number[];
  expense: number[];
}

export interface ChecklistRecord {
  id: string;
  title: string;
  category: string;
  fields: any[];
  createdAt?: Date;
}
