import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
  Modal,
  ActivityIndicator,
  Alert,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Camera, Plus, X, Trash2, Calendar } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import ImagePicker from './ImagePicker';
import { useEntityGallery } from '@/hooks/useEntityGallery';
import { GalleryPhoto } from '@/services/galleryService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface EntityGalleryProps {
  entityId: string;
  entityType: 'plant' | 'animal' | 'garden' | 'field' | 'equipment' | 'task';
  isEditable?: boolean;
  maxPhotos?: number;
  showAddButton?: boolean;
  gridColumns?: number;
  imageSize?: 'small' | 'medium' | 'large';
  showTimestamp?: boolean;
  showDeleteOption?: boolean;
  emptyStateMessage?: string;
  style?: any;
  onPhotosChange?: (photos: GalleryPhoto[]) => void;
}

export const EntityGallery: React.FC<EntityGalleryProps> = ({
  entityId,
  entityType,
  isEditable = true,
  maxPhotos = 20,
  showAddButton = true,
  gridColumns = 3,
  imageSize = 'medium',
  showTimestamp = true,
  showDeleteOption = true,
  emptyStateMessage,
  style,
  onPhotosChange,
}) => {
  const { t } = useTranslation();
  
  const {
    photos,
    loading,
    uploading,
    error,
    addImage,
    deleteImage,
    refresh,
  } = useEntityGallery({ entityId, entityType });

  const [selectedImage, setSelectedImage] = useState<string>('');
  const [showImageModal, setShowImageModal] = useState(false);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [newImageUri, setNewImageUri] = useState('');

  // Notify parent component of photo changes
  React.useEffect(() => {
    if (onPhotosChange) {
      onPhotosChange(photos);
    }
  }, [photos, onPhotosChange]);

  // Calculate image dimensions based on size prop
  const getImageDimensions = () => {
    const spacing = 12;
    const containerPadding = 20;
    const availableWidth = SCREEN_WIDTH - (containerPadding * 2) - (spacing * (gridColumns - 1));
    const imageWidth = availableWidth / gridColumns;
    
    const sizeMultiplier = {
      small: 0.8,
      medium: 1,
      large: 1.2,
    };
    
    return {
      width: imageWidth * sizeMultiplier[imageSize],
      height: imageWidth * sizeMultiplier[imageSize],
    };
  };

  const imageDimensions = getImageDimensions();

  const handleAddImage = async () => {
    if (!newImageUri) return;
    
    try {
      await addImage(newImageUri);
      setNewImageUri('');
      setShowImagePicker(false);
      Alert.alert(t('common.success'), t('gallery.imageAddedSuccess'));
    } catch (error) {
      Alert.alert(t('common.error'), t('gallery.imageAddError'));
    }
  };

  const handleDeleteImage = async (photoId: string) => {
    Alert.alert(
      t('gallery.deleteImageTitle'),
      t('gallery.deleteImageMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteImage(photoId);
              Alert.alert(t('common.success'), t('gallery.imageDeletedSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('gallery.imageDeleteError'));
            }
          },
        },
      ]
    );
  };

  const renderImageItem = ({ item, index }: { item: Photo; index: number }) => (
    <View style={[styles.imageContainer, { width: imageDimensions.width, height: imageDimensions.height }]}>
      <TouchableOpacity
        style={styles.imageWrapper}
        onPress={() => {
          setSelectedImage(item.url);
          setShowImageModal(true);
        }}
      >
        <Image
          source={{ uri: item.url }}
          style={[styles.galleryImage, { width: '100%', height: '100%' }]}
          resizeMode="cover"
        />
        
        {showTimestamp && item.timestamp && (
          <View style={styles.timestampOverlay}>
            <Calendar size={10} color={colors.white} />
            <Text style={styles.timestampText}>
              {new Date(item.timestamp).toLocaleDateString()}
            </Text>
          </View>
        )}
        
        {isEditable && showDeleteOption && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteImage(index)}
          >
            <Trash2 size={14} color={colors.white} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderAddButton = () => {
    if (!isEditable || !showAddButton || photos.length >= maxPhotos) return null;
    
    return (
      <TouchableOpacity
        style={[
          styles.addImageButton,
          { width: imageDimensions.width, height: imageDimensions.height }
        ]}
        onPress={() => setShowImagePicker(true)}
      >
        <Plus size={24} color={colors.gray[400]} />
        <Text style={styles.addImageText}>{t('gallery.addPhoto')}</Text>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Camera size={48} color={colors.gray[300]} />
      <Text style={styles.emptyTitle}>
        {emptyStateMessage || t(`gallery.empty.${entityType}`)}
      </Text>
      <Text style={styles.emptySubtitle}>
        {t('gallery.emptySubtitle')}
      </Text>
      {isEditable && showAddButton && (
        <TouchableOpacity
          style={styles.emptyAddButton}
          onPress={() => setShowImagePicker(true)}
        >
          <Text style={styles.emptyAddButtonText}>{t('gallery.addFirstPhoto')}</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderGalleryContent = () => {
    if (photos.length === 0) {
      return renderEmptyState();
    }

    const data = [...photos];
    if (isEditable && showAddButton && photos.length < maxPhotos) {
      data.push({ url: 'ADD_BUTTON', timestamp: new Date() } as Photo);
    }

    return (
      <FlatList
        data={data}
        renderItem={({ item, index }) => {
          if (item.url === 'ADD_BUTTON') {
            return renderAddButton();
          }
          return renderImageItem({ item, index });
        }}
        keyExtractor={(item, index) => item.url === 'ADD_BUTTON' ? 'add-button' : `${item.url}-${index}`}
        numColumns={gridColumns}
        contentContainerStyle={styles.galleryGrid}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
      />
    );
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.title}>{t(`gallery.title.${entityType}`)}</Text>
        {photos.length > 0 && (
          <Text style={styles.photoCount}>
            {photos.length} {photos.length === 1 ? t('gallery.photo') : t('gallery.photos')}
          </Text>
        )}
      </View>

      {renderGalleryContent()}

      {/* Image Picker Modal */}
      <Modal
        visible={showImagePicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowImagePicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.imagePickerModal}>
            <View style={styles.imagePickerHeader}>
              <Text style={styles.imagePickerTitle}>{t('gallery.addPhoto')}</Text>
              <TouchableOpacity onPress={() => setShowImagePicker(false)}>
                <X size={24} color={colors.gray[700]} />
              </TouchableOpacity>
            </View>

            <View style={styles.imagePickerContainer}>
              <ImagePicker
                image={newImageUri}
                onImageSelected={setNewImageUri}
                placeholder={t('gallery.selectImage')}
                size={200}
              />
            </View>

            <View style={styles.imagePickerActions}>
              <TouchableOpacity
                style={[styles.actionButton, styles.cancelButton]}
                onPress={() => {
                  setNewImageUri('');
                  setShowImagePicker(false);
                }}
              >
                <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  styles.addButton,
                  (!newImageUri || uploading) && styles.disabledButton
                ]}
                onPress={handleAddImage}
                disabled={!newImageUri || uploading}
              >
                {uploading ? (
                  <ActivityIndicator size="small" color={colors.white} />
                ) : (
                  <Text style={styles.addButtonText}>{t('gallery.addPhoto')}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Full Screen Image Modal */}
      <Modal
        visible={showImageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageModal(false)}
      >
        <View style={styles.fullScreenModalOverlay}>
          <TouchableOpacity
            style={styles.closeFullScreenButton}
            onPress={() => setShowImageModal(false)}
          >
            <X size={24} color={colors.white} />
          </TouchableOpacity>

          <Image
            source={{ uri: selectedImage }}
            style={styles.fullScreenImage}
            resizeMode="contain"
          />

          {photos.length > 1 && (
            <View style={styles.thumbnailContainer}>
              <FlatList
                data={photos}
                horizontal
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.thumbnailWrapper,
                      selectedImage === item.url && styles.selectedThumbnail
                    ]}
                    onPress={() => setSelectedImage(item.url)}
                  >
                    <Image
                      source={{ uri: item.url }}
                      style={styles.thumbnail}
                      resizeMode="cover"
                    />
                  </TouchableOpacity>
                )}
                keyExtractor={(item, index) => `${item.url}-${index}`}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.thumbnailList}
              />
            </View>
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  photoCount: {
    fontSize: 14,
    color: colors.gray[600],
  },
  galleryGrid: {
    paddingBottom: 20,
  },
  imageContainer: {
    marginRight: 12,
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageWrapper: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  galleryImage: {
    borderRadius: 8,
  },
  timestampOverlay: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timestampText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: '500',
  },
  deleteButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(255,0,0,0.8)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageButton: {
    borderWidth: 2,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.gray[50],
    marginRight: 12,
    marginBottom: 12,
  },
  addImageText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 4,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[700],
    marginTop: 12,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.gray[500],
    marginTop: 4,
    textAlign: 'center',
  },
  emptyAddButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  emptyAddButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePickerModal: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    width: SCREEN_WIDTH * 0.9,
    maxHeight: SCREEN_HEIGHT * 0.7,
  },
  imagePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  imagePickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  imagePickerContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  imagePickerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.gray[100],
  },
  cancelButtonText: {
    color: colors.gray[700],
    fontSize: 16,
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: colors.primary,
  },
  addButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  disabledButton: {
    backgroundColor: colors.gray[300],
  },
  fullScreenModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeFullScreenButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 8,
  },
  fullScreenImage: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.8,
  },
  thumbnailContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
  },
  thumbnailList: {
    paddingHorizontal: 20,
  },
  thumbnailWrapper: {
    marginRight: 8,
    borderRadius: 4,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedThumbnail: {
    borderColor: colors.primary,
  },
  thumbnail: {
    width: 50,
    height: 50,
  },
});



