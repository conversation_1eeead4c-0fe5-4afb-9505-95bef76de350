import { firestore } from '@/firebase/config';
import { useAuthStore } from './auth-store';
import { useFarmStore } from './farm-store'
import { collection, addDoc, serverTimestamp } from "firebase/firestore";
export type LogEventName =
  | 'login'
  | 'logout'
  | 'view_task'
  | 'submit_task'
  | 'task_created'
  | 'task_updated'
  | 'task_completed'
  | 'data_sync'
  | 'navigate'
  | 'error'
  | 'api_error'
  | 'update_profile'
  | 'notification_clicked'
  | 'create_animal'
  | 'update_animal'
  | 'update_plant';

export interface LogPayload {
  [key: string]: any;
}

export const logEvent =async (eventName: LogEventName, payload: LogPayload = {}) => {
  const { user } = useAuthStore.getState();
  const { currentFarm } = useFarmStore.getState();

  const logData = {
    timestamp: new Date().toISOString(),
    eventName,
    userId: user?.id || null,
    userEmail: user?.email || null,
    farmId: currentFarm?.id || null,
    farmName: currentFarm?.name || null,
    ...payload,
  };

  try {
    await addDoc(collection(firestore, "analytics"),logData);
  } catch (error) {
    console.error("Failed to log analytics event:", error);
  }
  // await addDoc(collection(firestore, "analytics"),logData);
  // logAnalyticsEvent(logData)
  // For development, we'll log to the console.
  // In production, you would send this to a remote logging service
  // like Sentry, Firebase Analytics, Datadog, etc.
  console.log('EVENT_LOG:', JSON.stringify(logData, null, 2));
};