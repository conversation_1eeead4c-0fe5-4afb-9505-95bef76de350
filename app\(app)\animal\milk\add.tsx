import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert,Text } from 'react-native';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
// import { Text, TextInput } from '@/components/Themed';
import Button from '@/components/Button';
import Input from '@/components/Input';
import { useFarmStore } from '@/store/farm-store';
// import { useTranslation } from '@/i18n';
import DatePicker from '@/components/DatePicker';
import { useAuthStore } from '@/store/auth-store';
import Toast from 'react-native-toast-message';
import { useTranslation } from '@/i18n/useTranslation';

export default function AddAnimalMilkRecordScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { addAnimalMilkRecord, getAnimal } = useFarmStore();
  const { language } = useAuthStore();
  
  const [morningMilk, setMorningMilk] = useState('');
  const [eveningMilk, setEveningMilk] = useState('');
  const [date, setDate] = useState(new Date());
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const animal = getAnimal(id);
  
  const handleSubmit = async () => {
    if ((!morningMilk || isNaN(Number(morningMilk))) && 
        (!eveningMilk || isNaN(Number(eveningMilk)))) {
      Alert.alert(t('error'), t('animal.milk.errorNoMilkQuantity'));
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      await addAnimalMilkRecord(id, {
        morning_milk: Number(morningMilk) || 0,
        evening_milk: Number(eveningMilk) || 0,
        date: date.toISOString(),
        notes: notes.trim() || undefined,
      });
      
      Toast.show({
        type: 'success',
        text1: t('success'),
        text2: t('animal.milk.addSuccess'),
      });
      
      router.back();
    } catch (error) {
      console.error('Error adding milk record:', error);
      Alert.alert(t('error'), t('animal.milk.addError'));
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <ScrollView style={styles.container}>
      <Stack.Screen options={{ title: t('animal.milk.addTitle') }} />
      
      <View style={styles.header}>
        <Text style={styles.title}>{t('entity.animal.addMilkRecordFor', { name: animal?.name || id })}</Text>
      </View>
      
      <View style={styles.form}>
        <Text style={styles.label}>{t('entity.animal.morningMilk')}</Text>
        <Input
          style={styles.input}
          value={morningMilk}
          onChangeText={setMorningMilk}
          placeholder={t('entity.animal.enterLiters')}
          keyboardType="numeric"
        />
        
        <Text style={styles.label}>{t('entity.animal.eveningMilk')}</Text>
        <Input
          style={styles.input}
          value={eveningMilk}
          onChangeText={setEveningMilk}
          placeholder={t('entity.animal.enterLiters')}
          keyboardType="numeric"
        />
        
        <Text style={styles.label}>{t('entity.animal.date')}</Text>
        <DatePicker
          date={date}
          onDateChange={setDate}
          maximumDate={new Date()}
        />
        
        <Text style={styles.label}>{t('entity.animal.notes')}</Text>
        <Input
          style={[styles.input, styles.textArea]}
          value={notes}
          onChangeText={setNotes}
          placeholder={t('entity.animal.enterNotes')}
          multiline
          numberOfLines={4}
        />
        
        <Button
          title={isSubmitting ? t('submitting') : t('save')}
          onPress={handleSubmit}
          disabled={isSubmitting}
          style={styles.button}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  form: {
    gap: 12,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
  },
  input: {
    width: '100%',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  button: {
    marginTop: 16,
  },
});