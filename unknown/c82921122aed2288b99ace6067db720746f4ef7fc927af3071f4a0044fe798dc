import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image, 
  SafeAreaView,
  I18nManager,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { Globe, Check } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';

export default function LanguageSelectionScreen() {
  const { locale, setLocale } = useTranslation();
  const { setLanguage } = useAuthStore();
  
  const languages = [
    { id: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
    { id: 'ur', name: 'Urdu', nativeName: 'اردو', flag: '🇵🇰' },
  ];
  
  const handleLanguageSelect = async (languageId: string) => {
    try {
      // Update both i18n system and auth store
      await setLocale(languageId);
      await setLanguage(languageId);
      
      // Navigate to login screen
      router.replace('/(auth)');
    } catch (error) {
      console.error('Language selection error:', error);
    }
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      
      <View style={styles.header}>
        <Image
          source={require('@/assets/images/icon.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={styles.appName}>Kissan Dost</Text>
        <Text style={styles.tagline}>Smart Farming Solutions</Text>
      </View>
      
      <View style={styles.content}>
        <View style={styles.languageHeader}>
          <Globe size={24} color={colors.primary} />
          <Text style={styles.title}>Select Language / زبان منتخب کریں</Text>
        </View>
        
        <Text style={styles.subtitle}>
          Choose your preferred language for the app interface
        </Text>
        <Text style={styles.subtitleUrdu}>
          ایپ انٹرفیس کے لیے اپنی پسندیدہ زبان کا انتخاب کریں
        </Text>
        
        <View style={styles.languageContainer}>
          {languages.map((language) => (
            <TouchableOpacity
              key={language.id}
              style={[
                styles.languageButton,
                locale === language.id && styles.selectedLanguageButton,
              ]}
              onPress={() => handleLanguageSelect(language.id)}
            >
              <View style={styles.languageContent}>
                <Text style={styles.languageFlag}>{language.flag}</Text>
                <View style={styles.languageTextContainer}>
                  <Text style={[
                    styles.languageName,
                    locale === language.id && styles.selectedLanguageText,
                  ]}>
                    {language.name}
                  </Text>
                  <Text style={[
                    styles.languageNativeName,
                    locale === language.id && styles.selectedLanguageText,
                  ]}>
                    {language.nativeName}
                  </Text>
                </View>
              </View>
              
              {locale === language.id && (
                <Check size={20} color={colors.primary} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        <TouchableOpacity
          style={styles.continueButton}
          onPress={() => router.replace('/(auth)')}
        >
          <Text style={styles.continueButtonText}>
            {locale === 'en' ? 'Continue' : 'جاری رکھیں'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          {locale === 'en' 
            ? 'You can change the language later in Profile settings'
            : 'آپ بعد میں پروفائل کی ترتیبات میں زبان تبدیل کر سکتے ہیں'
          }
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  languageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[800],
    marginLeft: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 8,
    textAlign: 'left',
  },
  subtitleUrdu: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 24,
    textAlign: 'right',
  },
  languageContainer: {
    marginBottom: 32,
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  selectedLanguageButton: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 16,
  },
  languageTextContainer: {
    flexDirection: 'column',
  },
  languageName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  languageNativeName: {
    fontSize: 14,
    color: colors.gray[600],
  },
  selectedLanguageText: {
    color: colors.primary,
  },
  continueButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  footer: {
    padding: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: colors.gray[500],
    textAlign: 'center',
  },
});