import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  ActivityIndicator,
  SafeAreaView,
  Alert,
  Modal,
  FlatList,
} from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { 
  Wheat, 
  Calendar, 
  BarChart3, 
  MoreHorizontal,
  Edit,
  Trash2,
  X,
  Plus,
  Camera,
  History,
  MapPin,
  ChevronRight,
  Scale,
  Star,
  Leaf,
  Sprout,
} from 'lucide-react-native';
import Button from '@/components/Button';
import { Yield } from '@/types';
import ImagePicker from '@/components/ImagePicker';

export default function YieldDetailScreen() {
  const { id } = useLocalSearchParams();
  const { user } = useAuthStore();
  const { 
    getYield, 
    updateYield, 
    deleteYield, 
    currentFarm,
    isLoading 
  } = useFarmStore();
  
  const [yieldData, setYieldData] = useState<Yield | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'gallery'>('overview');
  const [showOptions, setShowOptions] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  
  // Mock history data
  const [history, setHistory] = useState([
    {
      id: '1',
      type: 'note_added',
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      userId: '1',
      userName: 'John Doe',
      title: 'Quality Assessment',
      description: 'Yield quality is excellent. Good color and size consistency.',
      images: ['https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80'],
    },
    {
      id: '2',
      type: 'status_change',
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      userId: '1',
      userName: 'John Doe',
      title: 'Harvest Completed',
      description: 'Harvested and recorded yield data',
    },
  ]);
  
  // Mock gallery images
  const [gallery, setGallery] = useState([
    'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    'https://images.unsplash.com/photo-1598170997322-6fe04cc3a24d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    'https://images.unsplash.com/photo-1598170996491-8fea47af5963?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
  ]);
  
  useEffect(() => {
    if (id && currentFarm) {
      const yieldId = Array.isArray(id) ? id[0] : id;
      const yieldItem = getYield(yieldId);
      
      if (yieldItem) {
        setYieldData(yieldItem);
      } else {
        Alert.alert('Error', 'Yield record not found');
        router.back();
      }
    }
  }, [id, currentFarm]);
  
  if (!yieldData || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }
  
  const handleDeleteYield = async () => {
    try {
      await deleteYield(yieldData.id);
      router.back();
    } catch (error) {
      console.error('Error deleting yield:', error);
      Alert.alert('Error', 'Failed to delete yield record');
    }
  };
  
  const handleAddImage = (uri: string) => {
    setGallery([uri, ...gallery]);
    setShowImagePickerModal(false);
  };
  
  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Crop Type</Text>
            <Text style={styles.infoValue}>{yieldData.cropType}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Harvest Date</Text>
            <Text style={styles.infoValue}>
              {new Date(yieldData.harvestDate).toLocaleDateString()}
            </Text>
          </View>
        </View>
        
        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Quantity</Text>
            <Text style={styles.infoValue}>
              {yieldData.quantity} {yieldData.unit}
            </Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Quality</Text>
            <View style={styles.statusContainer}>
              <View style={[styles.statusIndicator, { backgroundColor: getQualityColor(yieldData.quality) }]} />
              <Text style={styles.statusText}>{formatQuality(yieldData.quality)}</Text>
            </View>
          </View>
        </View>
        
        {yieldData.fieldId && (
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Field</Text>
              <TouchableOpacity 
                style={styles.locationButton}
                onPress={() => router.push(`/field/${yieldData.fieldId}`)}
              >
                <MapPin size={16} color={colors.primary} />
                <Text style={styles.locationText}>View Field</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {yieldData.gardenId && (
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Garden</Text>
              <TouchableOpacity 
                style={styles.locationButton}
                onPress={() => router.push(`/garden/${yieldData.gardenId}`)}
              >
                <MapPin size={16} color={colors.primary} />
                <Text style={styles.locationText}>View Garden</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {yieldData.notes && (
          <View style={styles.notesContainer}>
            <Text style={styles.notesLabel}>Notes</Text>
            <Text style={styles.notesText}>{yieldData.notes}</Text>
          </View>
        )}
      </View>
      
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActions}>
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => router.push(`/yield/edit/${yieldData.id}`)}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.primary }]}>
              <Edit size={20} color={colors.white} />
            </View>
            <Text style={styles.quickActionText}>Edit Yield</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => setShowImagePickerModal(true)}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.info }]}>
              <Camera size={20} color={colors.white} />
            </View>
            <Text style={styles.quickActionText}>Add Photo</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => router.push('/reports/yield')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.warning }]}>
              <BarChart3 size={20} color={colors.white} />
            </View>
            <Text style={styles.quickActionText}>View Reports</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => router.push('/yield/create')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.success }]}>
              <Plus size={20} color={colors.white} />
            </View>
            <Text style={styles.quickActionText}>New Yield</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.yieldStatsContainer}>
        <Text style={styles.sectionTitle}>Yield Statistics</Text>
        
        <View style={styles.yieldStatsCard}>
          <View style={styles.yieldStatItem}>
            <View style={[styles.yieldStatIcon, { backgroundColor: colors.primary + '20' }]}>
              <Scale size={20} color={colors.primary} />
            </View>
            <View style={styles.yieldStatContent}>
              <Text style={styles.yieldStatValue}>{yieldData.quantity} {yieldData.unit}</Text>
              <Text style={styles.yieldStatLabel}>Total Yield</Text>
            </View>
          </View>
          
          <View style={styles.yieldStatItem}>
            <View style={[styles.yieldStatIcon, { backgroundColor: colors.success + '20' }]}>
              <Star size={20} color={colors.success} />
            </View>
            <View style={styles.yieldStatContent}>
              <Text style={styles.yieldStatValue}>{formatQuality(yieldData.quality)}</Text>
              <Text style={styles.yieldStatLabel}>Quality Rating</Text>
            </View>
          </View>
          
          <View style={styles.yieldStatItem}>
            <View style={[styles.yieldStatIcon, { backgroundColor: colors.info + '20' }]}>
              <Leaf size={20} color={colors.info} />
            </View>
            <View style={styles.yieldStatContent}>
              <Text style={styles.yieldStatValue}>{yieldData.cropType}</Text>
              <Text style={styles.yieldStatLabel}>Crop Type</Text>
            </View>
          </View>
          
          <View style={styles.yieldStatItem}>
            <View style={[styles.yieldStatIcon, { backgroundColor: colors.warning + '20' }]}>
              <Calendar size={20} color={colors.warning} />
            </View>
            <View style={styles.yieldStatContent}>
              <Text style={styles.yieldStatValue}>
                {new Date(yieldData.harvestDate).toLocaleDateString()}
              </Text>
              <Text style={styles.yieldStatLabel}>Harvest Date</Text>
            </View>
          </View>
        </View>
      </View>
      
      <View style={styles.comparisonContainer}>
        <Text style={styles.sectionTitle}>Comparison with Previous Yields</Text>
        
        <View style={styles.comparisonChart}>
          <View style={styles.chartLegend}>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: colors.primary }]} />
              <Text style={styles.legendText}>Current Yield</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: colors.gray[300] }]} />
              <Text style={styles.legendText}>Average Yield</Text>
            </View>
          </View>
          
          <View style={styles.chartBars}>
            <View style={styles.chartBarGroup}>
              <Text style={styles.chartLabel}>Quantity</Text>
              <View style={styles.chartBarContainer}>
                <View style={[styles.chartBar, { width: '80%', backgroundColor: colors.primary }]} />
                <View style={[styles.chartBar, { width: '70%', backgroundColor: colors.gray[300] }]} />
              </View>
            </View>
            
            <View style={styles.chartBarGroup}>
              <Text style={styles.chartLabel}>Quality</Text>
              <View style={styles.chartBarContainer}>
                <View style={[styles.chartBar, { width: '90%', backgroundColor: colors.primary }]} />
                <View style={[styles.chartBar, { width: '75%', backgroundColor: colors.gray[300] }]} />
              </View>
            </View>
            
            <View style={styles.chartBarGroup}>
              <Text style={styles.chartLabel}>Efficiency</Text>
              <View style={styles.chartBarContainer}>
                <View style={[styles.chartBar, { width: '85%', backgroundColor: colors.primary }]} />
                <View style={[styles.chartBar, { width: '80%', backgroundColor: colors.gray[300] }]} />
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
  
  const renderHistoryTab = () => (
    <View style={styles.tabContent}>
      <Text style={styles.tabTitle}>History</Text>
      
      {history.length > 0 ? (
        history.map((entry, index) => (
          <View key={entry.id} style={styles.historyItem}>
            <View style={styles.historyItemHeader}>
              <View style={styles.historyItemType}>
                {entry.type === 'note_added' && (
                  <Wheat size={16} color={colors.info} />
                )}
                {entry.type === 'status_change' && (
                  <Sprout size={16} color={colors.primary} />
                )}
                <Text style={styles.historyItemTypeText}>
                  {formatHistoryType(entry.type)}
                </Text>
              </View>
              <Text style={styles.historyItemDate}>
                {new Date(entry.date).toLocaleDateString()}
              </Text>
            </View>
            
            <Text style={styles.historyItemTitle}>{entry.title}</Text>
            <Text style={styles.historyItemDescription}>{entry.description}</Text>
            
            {entry.images && entry.images.length > 0 && (
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.historyImagesContainer}
              >
                {entry.images.map((image, imgIndex) => (
                  <Image 
                    key={imgIndex}
                    source={{ uri: image }}
                    style={styles.historyImage}
                  />
                ))}
              </ScrollView>
            )}
            
            <View style={styles.historyItemFooter}>
              <Text style={styles.historyItemUser}>By: {entry.userName}</Text>
            </View>
            
            {index < history.length - 1 && <View style={styles.historyItemDivider} />}
          </View>
        ))
      ) : (
        <View style={styles.emptyContainer}>
          <History size={40} color={colors.gray[400]} />
          <Text style={styles.emptyText}>No history records found</Text>
        </View>
      )}
    </View>
  );
  
  const renderGalleryTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.tabHeader}>
        <Text style={styles.tabTitle}>Gallery</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => setShowImagePickerModal(true)}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>Add Photo</Text>
        </TouchableOpacity>
      </View>
      
      {gallery.length > 0 ? (
        <View style={styles.galleryGrid}>
          {gallery.map((image, index) => (
            <TouchableOpacity 
              key={index}
              style={styles.galleryImageContainer}
              onPress={() => {
                // In a real app, you would show a full-screen image viewer
                Alert.alert('Image', 'View full image');
              }}
            >
              <Image 
                source={{ uri: image }}
                style={styles.galleryImage}
              />
            </TouchableOpacity>
          ))}
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <Camera size={40} color={colors.gray[400]} />
          <Text style={styles.emptyText}>No photos added yet</Text>
          <TouchableOpacity 
            style={styles.emptyButton}
            onPress={() => setShowImagePickerModal(true)}
          >
            <Text style={styles.emptyButtonText}>Add Photo</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: yieldData.name,
          headerRight: () => (
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => setShowOptions(!showOptions)}
            >
              <MoreHorizontal size={24} color={colors.gray[700]} />
            </TouchableOpacity>
          ),
        }}
      />
      
      {showOptions && (
        <View style={styles.optionsMenu}>
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={() => {
              setShowOptions(false);
              router.push(`/yield/edit/${yieldData.id}`);
            }}
          >
            <Edit size={20} color={colors.gray[700]} />
            <Text style={styles.optionText}>Edit Yield</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={() => {
              setShowOptions(false);
              setShowDeleteConfirm(true);
            }}
          >
            <Trash2 size={20} color={colors.danger} />
            <Text style={[styles.optionText, { color: colors.danger }]}>Delete Yield</Text>
          </TouchableOpacity>
        </View>
      )}
      
      <View style={styles.imageContainer}>
        <Image 
          source={{ 
            uri: yieldData.image || 
            'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80'
          }} 
          style={styles.yieldImage}
        />
        
        <View style={styles.imageOverlay}>
          <View style={styles.yieldStatus}>
            <View style={[
              styles.statusBadge, 
              { backgroundColor: getQualityColor(yieldData.quality) }
            ]}>
              <Text style={styles.statusBadgeText}>{formatQuality(yieldData.quality)}</Text>
            </View>
          </View>
        </View>
      </View>
      
      <View style={styles.tabsContainer}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabs}
        >
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
            onPress={() => setActiveTab('overview')}
          >
            <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>Overview</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'history' && styles.activeTab]}
            onPress={() => setActiveTab('history')}
          >
            <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>History</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'gallery' && styles.activeTab]}
            onPress={() => setActiveTab('gallery')}
          >
            <Text style={[styles.tabText, activeTab === 'gallery' && styles.activeTabText]}>Gallery</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
      
      <ScrollView style={styles.content}>
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'history' && renderHistoryTab()}
        {activeTab === 'gallery' && renderGalleryTab()}
      </ScrollView>
      
      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteConfirm}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDeleteConfirm(false)}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContent}>
            <Text style={styles.confirmModalTitle}>Delete Yield Record</Text>
            <Text style={styles.confirmModalText}>
              Are you sure you want to delete this yield record? This action cannot be undone.
            </Text>
            
            <View style={styles.confirmModalButtons}>
              <TouchableOpacity 
                style={[styles.confirmModalButton, styles.confirmModalCancelButton]}
                onPress={() => setShowDeleteConfirm(false)}
              >
                <Text style={styles.confirmModalCancelText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.confirmModalButton, styles.confirmModalDeleteButton]}
                onPress={handleDeleteYield}
              >
                <Text style={styles.confirmModalDeleteText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      
      {/* Image Picker Modal */}
      <Modal
        visible={showImagePickerModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowImagePickerModal(false)}
      >
        <View style={styles.imagePickerModalOverlay}>
          <View style={styles.imagePickerModalContent}>
            <View style={styles.imagePickerModalHeader}>
              <Text style={styles.imagePickerModalTitle}>Add Photo</Text>
              <TouchableOpacity onPress={() => setShowImagePickerModal(false)}>
                <X size={24} color={colors.gray[700]} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.imagePickerContainer}>
              <ImagePicker
                image=""
                onImageSelected={handleAddImage}
                placeholder="Tap to select an image"
                size={200}
              />
            </View>
            
            <Text style={styles.imagePickerText}>
              Take a photo or select one from your gallery
            </Text>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

// Helper functions
const getQualityColor = (quality: string) => {
  switch (quality) {
    case 'excellent':
      return colors.success;
    case 'good':
      return colors.primary;
    case 'fair':
      return colors.warning;
    case 'poor':
      return colors.danger;
    default:
      return colors.gray[500];
  }
};

const formatQuality = (quality: string) => {
  return quality.charAt(0).toUpperCase() + quality.slice(1);
};

const formatHistoryType = (type: string) => {
  switch (type) {
    case 'note_added':
      return 'Note';
    case 'status_change':
      return 'Status Update';
    case 'image_added':
      return 'Photo';
    default:
      return type.replace('_', ' ');
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
  },
  optionsMenu: {
    position: 'absolute',
    top: 50,
    right: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 8,
    zIndex: 10,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  optionText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  imageContainer: {
    height: 200,
    position: 'relative',
  },
  yieldImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  yieldStatus: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
  },
  tabsContainer: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tabs: {
    paddingHorizontal: 16,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  infoCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    color: colors.gray[800],
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    color: colors.gray[800],
    fontWeight: '500',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary + '10',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  locationText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 4,
  },
  notesContainer: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  notesLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: colors.gray[800],
    lineHeight: 20,
  },
  quickActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  yieldStatsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  yieldStatsCard: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  yieldStatItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  yieldStatIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  yieldStatContent: {
    flex: 1,
  },
  yieldStatValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 2,
  },
  yieldStatLabel: {
    fontSize: 12,
    color: colors.gray[600],
  },
  comparisonContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  comparisonChart: {
    marginTop: 8,
  },
  chartLegend: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 2,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: colors.gray[600],
  },
  chartBars: {
    marginBottom: 8,
  },
  chartBarGroup: {
    marginBottom: 16,
  },
  chartLabel: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 4,
  },
  chartBarContainer: {
    marginTop: 4,
  },
  chartBar: {
    height: 16,
    borderRadius: 4,
    marginBottom: 4,
  },
  tabHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tabTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
    marginLeft: 4,
  },
  historyItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyItemType: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  historyItemTypeText: {
    fontSize: 12,
    color: colors.gray[800],
    marginLeft: 4,
  },
  historyItemDate: {
    fontSize: 12,
    color: colors.gray[500],
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  historyItemDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 12,
    lineHeight: 20,
  },
  historyImagesContainer: {
    marginBottom: 12,
  },
  historyImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 8,
  },
  historyItemFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  historyItemUser: {
    fontSize: 12,
    color: colors.gray[500],
  },
  historyItemDivider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginVertical: 16,
  },
  galleryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  galleryImageContainer: {
    width: '32%',
    aspectRatio: 1,
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  galleryImage: {
    width: '100%',
    height: '100%',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    marginTop: 12,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  confirmModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmModalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 400,
  },
  confirmModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  confirmModalText: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 20,
    lineHeight: 20,
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  confirmModalButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  confirmModalCancelButton: {
    backgroundColor: colors.gray[200],
  },
  confirmModalCancelText: {
    color: colors.gray[800],
    fontWeight: '500',
  },
  confirmModalDeleteButton: {
    backgroundColor: colors.danger,
  },
  confirmModalDeleteText: {
    color: colors.white,
    fontWeight: '500',
  },
  imagePickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  imagePickerModalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  imagePickerModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  imagePickerModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  imagePickerContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  imagePickerText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 20,
  },
});