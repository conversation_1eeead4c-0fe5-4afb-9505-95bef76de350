import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  I18nManager,
} from 'react-native';
import { router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import {
  User,
  UserPlus,
  MoreVertical,
  Mail,
  Phone,
  Shield,
  UserX,
  ChevronRight,
  Edit,
} from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';
import { capitalizeFirstLetter } from '@/utils/util';

export default function UserListScreen() {
  const { farmPermissions, currentFarm, removeUserFromFarm } = useFarmStore();
  const { user, getUsersByFarm, removeFarmFromUser } = useAuthStore();
  const { t, isRTL } = useTranslation();

  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);

  useEffect(() => {
    loadUsers();
  }, [currentFarm]);

  const loadUsers = async () => {
    if (!currentFarm) return;

    setLoading(true);
    try {
      // Use the new method to get users by farm
      const farmUsers = await getUsersByFarm(currentFarm.id);
      setUsers(farmUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      Alert.alert(t('error'), t('user.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveUser = (userId: string) => {
    if (!currentFarm) return;

    Alert.alert(
      t('user.removeTitle'),
      t('user.removeConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('user.remove'),
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              // Use the new method to remove farm from user
              await removeFarmFromUser(userId, currentFarm.id);
              // Also call the farm store method to update permissions
              await removeUserFromFarm(currentFarm.id, userId);

              // Update the local users list
              setUsers(users.filter(user => user.id !== userId));
              Alert.alert(t('success'), t('user.removeSuccess'));
            } catch (error) {
              console.error('Error removing user:', error);
              Alert.alert(t('error'), t('user.removeError'));
            } finally {
              setLoading(false);
              setSelectedUser(null);
            }
          }
        }
      ]
    );
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return colors.primary;
      case 'admin':
        return colors.info;
      case 'caretaker':
        return colors.success;
      default:
        return colors.gray[500];
    }
  };
  // Inside the component, add this function to handle editing a user
  const handleEditUser = (userId) => {
    router.push({
      pathname: '/user/invite',
      params: { editMode: 'true', userId }
    });
  };

  // const renderUserItem = ({ item }: { item: any }) => {
  //   const isCurrentUser = item.id === user?.id;
  //   const canRemove = user?.role === 'owner' || (user?.role === 'admin' && item.role === 'caretaker');

  //   return (
  //     <View style={styles.userItem}>
  //       <View style={styles.userAvatar}>
  //         {item.avatar ? (
  //           <View style={styles.avatarContainer}>
  //             <Text style={styles.avatarText}>{item.name.charAt(0)}</Text>
  //           </View>
  //         ) : (
  //           <User size={24} color={colors.gray[500]} />
  //         )}
  //       </View>

  //       <View style={styles.userInfo}>
  //         <View style={[styles.userHeader, isRTL && styles.userHeaderRTL]}>
  //           <Text style={[styles.userName, isRTL && styles.userNameRTL]}>
  //             {item.name} {isCurrentUser && `(${t('user.you')})`}
  //           </Text>
  //           <View style={[styles.roleBadge, { backgroundColor: getRoleColor(item.role) + '20' }]}>
  //             <Text style={[styles.roleText, { color: getRoleColor(item.role) }]}>
  //               {t(`user.role.${item.role}`)}
  //             </Text>
  //           </View>
  //         </View>

  //         <View style={styles.userDetails}>
  //           <View style={[styles.userDetailItem, isRTL && styles.userDetailItemRTL]}>
  //             <Mail size={14} color={colors.gray[500]} style={[styles.userDetailIcon, isRTL && styles.userDetailIconRTL]} />
  //             <Text style={[styles.userDetailText, isRTL && styles.userDetailTextRTL]}>{item.email}</Text>
  //           </View>

  //           <View style={[styles.userDetailItem, isRTL && styles.userDetailItemRTL]}>
  //             <Phone size={14} color={colors.gray[500]} style={[styles.userDetailIcon, isRTL && styles.userDetailIconRTL]} />
  //             <Text style={[styles.userDetailText, isRTL && styles.userDetailTextRTL]}>{item.phone}</Text>
  //           </View>
  //         </View>
  //       </View>

  //       {canRemove && !isCurrentUser && (
  //         <TouchableOpacity
  //           style={styles.userAction}
  //           onPress={() => setSelectedUser(selectedUser === item.id ? null : item.id)}
  //         >
  //           <MoreVertical size={20} color={colors.gray[500]} />

  //           {/* // Update the action menu to include an edit option */}
  //           {selectedUser === item.id && (
  //             <View style={[styles.actionMenu, isRTL && styles.actionMenuRTL]}>
  //               {/* Add Edit option */}
  //               <TouchableOpacity
  //                 style={[styles.actionMenuItem, isRTL && styles.actionMenuItemRTL]}
  //                 onPress={() => {
  //                   setSelectedUser(null);
  //                   handleEditUser(item.id);
  //                 }}
  //               >
  //                 <Edit size={16} color={colors.primary} style={[styles.actionMenuItemIcon, isRTL && styles.actionMenuItemIconRTL]} />
  //                 <Text style={[styles.actionMenuItemText, { color: colors.primary }, isRTL && styles.actionMenuItemTextRTL]}>
  //                   {t('user.edit')}
  //                 </Text>
  //               </TouchableOpacity>

  //               {/* Existing Remove option */}
  //               <TouchableOpacity
  //                 style={[styles.actionMenuItem, isRTL && styles.actionMenuItemRTL]}
  //                 onPress={() => handleRemoveUser(item.id)}
  //               >
  //                 <UserX size={16} color={colors.danger} style={[styles.actionMenuItemIcon, isRTL && styles.actionMenuItemIconRTL]} />
  //                 <Text style={[styles.actionMenuItemText, { color: colors.danger }, isRTL && styles.actionMenuItemTextRTL]}>
  //                   {t('user.remove')}
  //                 </Text>
  //               </TouchableOpacity>
  //             </View>
  //           )}
  //           {/* {selectedUser === item.id && (
  //             <View style={[styles.actionMenu, isRTL && styles.actionMenuRTL]}>
  //               <TouchableOpacity
  //                 style={[styles.actionMenuItem, isRTL && styles.actionMenuItemRTL]}
  //                 onPress={() => handleRemoveUser(item.id)}
  //               >
  //                 <UserX size={16} color={colors.danger} style={[styles.actionMenuItemIcon, isRTL && styles.actionMenuItemIconRTL]} />
  //                 <Text style={[styles.actionMenuItemText, { color: colors.danger }, isRTL && styles.actionMenuItemTextRTL]}>
  //                   {t('user.remove')}
  //                 </Text>
  //               </TouchableOpacity>
  //             </View>
  //           )} */}
  //         </TouchableOpacity>
  //       )}

  //       {!canRemove && !isCurrentUser && (
  //         <ChevronRight size={20} color={colors.gray[400]} />
  //       )}
  //     </View>
  //   );
  // };
  // Fix the action menu positioning and improve the user card UI
  const renderUserItem = ({ item }) => {
    const isCurrentUser = item.id === user?.id;
    const canRemove = (user?.role === 'owner' || user?.role === 'admin') && !isCurrentUser;

    // Get role color function
    const getRoleColor = (role) => {
      switch (role) {
        case 'owner':
          return colors.primary;
        case 'admin':
          return colors.warning;
        default:
          return colors.info;
      }
    };

    return (
      <View style={styles.userItemContainer}>
        <TouchableOpacity
          style={styles.userItem}
          onPress={() => router.push(`/user/profile/${item.id}`)}
          activeOpacity={0.7}
        >
          <View style={styles.userAvatar}>
            {item.avatar ? (
              <Image
                source={{ uri: item.avatar }}
                style={styles.avatarImage}
              />
            ) : (
              <View style={[styles.avatarContainer, { backgroundColor: getRoleColor(item.role) + '20' }]}>
                <Text style={[styles.avatarText, { color: getRoleColor(item.role) }]}>
                  {item?.name ? item?.name?.charAt(0)?.toUpperCase() : item?.displayName?.charAt(0)?.toUpperCase()}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.userInfo}>
            <View style={[styles.userHeader, isRTL && styles.userHeaderRTL]}>
              <Text style={[styles.userName, isRTL && styles.userNameRTL]}>
                {capitalizeFirstLetter(item?.name) || capitalizeFirstLetter(item?.displayName)} {isCurrentUser && `(${t('user.you')})`}
              </Text>
              <View style={[styles.roleBadge, { backgroundColor: getRoleColor(item.role) + '20' }]}>
                <Text style={[styles.roleText, { color: getRoleColor(item.role) }]}>
                  {t(`user.role.${item.role}`)}
                </Text>
              </View>
            </View>

            <Text style={[styles.userEmail, isRTL && styles.userEmailRTL]}>
              {item.email}
            </Text>

            {item.phoneNumber && (
              <Text style={[styles.userPhone, isRTL && styles.userPhoneRTL]}>
                {item.phoneNumber}
              </Text>
            )}
          </View>

          {canRemove && !isCurrentUser && (
            <View style={styles.userActionContainer}>
              <TouchableOpacity
                style={styles.userAction}
                onPress={() => setSelectedUser(selectedUser === item.id ? null : item.id)}
              >
                <MoreVertical size={20} color={colors.gray[500]} />
              </TouchableOpacity>

              {selectedUser === item.id && (
                <View style={[styles.actionMenu, isRTL && styles.actionMenuRTL]}>
                  <TouchableOpacity
                    style={[styles.actionMenuItem, isRTL && styles.actionMenuItemRTL]}
                    onPress={() => {
                      setSelectedUser(null);
                      handleEditUser(item.id);
                    }}
                  >
                    <Edit size={16} color={colors.primary} style={[styles.actionMenuItemIcon, isRTL && styles.actionMenuItemIconRTL]} />
                    <Text style={[styles.actionMenuItemText, { color: colors.primary }, isRTL && styles.actionMenuItemTextRTL]}>
                      {t('user.edit')}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionMenuItem, isRTL && styles.actionMenuItemRTL]}
                    onPress={() => handleRemoveUser(item.id)}
                  >
                    <UserX size={16} color={colors.danger} style={[styles.actionMenuItemIcon, isRTL && styles.actionMenuItemIconRTL]} />
                    <Text style={[styles.actionMenuItemText, { color: colors.danger }, isRTL && styles.actionMenuItemTextRTL]}>
                      {t('user.remove')}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          )}

          {!canRemove && !isCurrentUser && (
            <ChevronRight size={20} color={colors.gray[400]} />
          )}
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <>
      <Stack.Screen
        options={{
          title: t('user.farmUsers'),
          headerShown: false
        }}
      />

      <SafeAreaView style={styles.container}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>{t('user.loading')}</Text>
          </View>
        ) : (
          <>
              <View style={[styles.header, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={styles.farmInfoContainer}>
                <Text style={[styles.farmInfoLabel, isRTL && styles.farmInfoLabelRTL]}>
                  {t('farm.current')}
                </Text>
                <Text style={[styles.farmInfoValue, isRTL && styles.farmInfoValueRTL]}>
                  {currentFarm?.name || t('farm.noFarmSelected')}
                </Text>
              </View>

              {(user?.role === 'owner' || user?.role === 'admin') && (
                <TouchableOpacity
                  style={[styles.inviteButton]}
                  onPress={() => router.push('/user/invite')}
                >
                  <UserPlus size={16} color={colors.white} style={styles.inviteButtonIcon} />
                  <Text style={styles.inviteButtonText}>{t('user.invite')}</Text>
                </TouchableOpacity>
              )}
            </View>

              <View style={[styles.rolesLegend, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={[styles.roleLegendItem, isRTL && styles.roleLegendItemRTL]}>
                <Shield size={16} color={colors.primary} style={[styles.roleLegendIcon, isRTL && styles.roleLegendIconRTL]} />
                <Text style={[styles.roleLegendText, isRTL && styles.roleLegendTextRTL]}>
                  {t('user.role.owner')}
                </Text>
              </View>

              <View style={[styles.roleLegendItem, isRTL && styles.roleLegendItemRTL]}>
                <Shield size={16} color={colors.info} style={[styles.roleLegendIcon, isRTL && styles.roleLegendIconRTL]} />
                <Text style={[styles.roleLegendText, isRTL && styles.roleLegendTextRTL]}>
                  {t('user.role.manager')}
                </Text>
              </View>

              <View style={[styles.roleLegendItem, isRTL && styles.roleLegendItemRTL]}>
                <Shield size={16} color={colors.success} style={[styles.roleLegendIcon, isRTL && styles.roleLegendIconRTL]} />
                <Text style={[styles.roleLegendText, isRTL && styles.roleLegendTextRTL]}>
                  {t('user.role.caretaker')}
                </Text>
              </View>
            </View>

            <FlatList
              data={users}
              renderItem={renderUserItem}
              keyExtractor={item => item.id}
                contentContainerStyle={styles.listContent}
                ItemSeparatorComponent={() => <View style={{ height: 12 }} />} // Add space between items
                removeClippedSubviews={false} // Prevent clipping of absolute positioned elements
                CellRendererComponent={({ children, index, style, ...props }) => (
                  <View style={[style, { zIndex: users.length - index }]} {...props}>
                    {children}
                  </View>
                )}
                ListEmptyComponent={
                  <View style={styles.emptyContainer}>
                    <User size={40} color={colors.gray[400]} />
                    <Text style={styles.emptyText}>{t('user.noUsers')}</Text>
                  </View>
                }
                // contentContainerStyle={styles.listContent}
                // ListEmptyComponent={
                //   <View style={styles.emptyContainer}>
                //     <User size={40} color={colors.gray[400]} />
                //     <Text style={styles.emptyText}>{t('user.noUsers')}</Text>
                //   </View>
                // }
            />
          </>
        )}
      </SafeAreaView>
    </>
  );
}

// Update the styles to improve the UI
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.gray[800],
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 4,
  },
  userList: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  userItemContainer: {
    marginBottom: 12,
    borderRadius: 12,
    backgroundColor: colors.white,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
  },
  userAvatar: {
    marginRight: 12,
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[200],
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[700],
  },
  userInfo: {
    flex: 1,
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  userHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
  },
  userNameRTL: {
    textAlign: 'right',
  },
  userEmail: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },
  userEmailRTL: {
    textAlign: 'right',
  },
  userPhone: {
    fontSize: 14,
    color: colors.gray[500],
  },
  userPhoneRTL: {
    textAlign: 'right',
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  userActionContainer: {
    position: 'relative',
    zIndex: 2, // Add zIndex to ensure it appears above other elements
  },
  userAction: {
    padding: 8,
  },
  actionMenu: {
    position: 'absolute',
    top: '100%',
    right: 0,
    backgroundColor: colors.white,
    borderRadius: 8,
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5, // Increase elevation for Android
    zIndex: 999, // High zIndex to ensure it's above everything
    width: 140,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  actionMenuRTL: {
    right: 'auto',
    left: 0,
  },
  actionMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  actionMenuItemRTL: {
    flexDirection: 'row-reverse',
  },
  actionMenuItemIcon: {
    marginRight: 8,
  },
  actionMenuItemIconRTL: {
    marginRight: 0,
    marginLeft: 8,
  },
  actionMenuItemText: {
    fontSize: 14,
    textAlign: 'left',
  },
  actionMenuItemTextRTL: {
    textAlign: 'right',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginTop: 40,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
    marginTop: 12,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.gray[600],
  },
  farmInfoContainer: {
    flex: 1,
  },
  farmInfoLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
    textAlign: 'left',
  },
  farmInfoLabelRTL: {
    textAlign: 'right',
  },
  farmInfoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    textAlign: 'left',
  },
  farmInfoValueRTL: {
    textAlign: 'right',
  },
  inviteButton: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  inviteButtonIcon: {
    marginRight: I18nManager.isRTL ? 0 : 6,
    marginLeft: I18nManager.isRTL ? 6 : 0,
  },
  inviteButtonText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 14,
  },
  rolesLegend: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    padding: 12,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  roleLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  roleLegendItemRTL: {
    flexDirection: 'row-reverse',
    marginRight: 0,
    marginLeft: 16,
  },
  roleLegendIcon: {
    marginRight: 4,
  },
  roleLegendIconRTL: {
    marginRight: 0,
    marginLeft: 4,
  },
  roleLegendText: {
    fontSize: 12,
    color: colors.gray[700],
    textAlign: 'left',
  },
  roleLegendTextRTL: {
    textAlign: 'right',
  },
  listContent: {
    padding: 16,
  },

});



// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.gray[50],
//   },
//   loadingContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },

//   header: {
//     flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     padding: 16,
//     backgroundColor: colors.white,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   farmInfoContainer: {
//     flex: 1,
//   },
//   farmInfoLabel: {
//     fontSize: 12,
//     color: colors.gray[500],
//     marginBottom: 4,
//     textAlign: 'left',
//   },
//   farmInfoLabelRTL: {
//     textAlign: 'right',
//   },
//   farmInfoValue: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     textAlign: 'left',
//   },
//   farmInfoValueRTL: {
//     textAlign: 'right',
//   },
//   inviteButton: {
//     flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
//     alignItems: 'center',
//     backgroundColor: colors.primary,
//     paddingHorizontal: 12,
//     paddingVertical: 8,
//     borderRadius: 8,
//   },
//   inviteButtonIcon: {
//     marginRight: I18nManager.isRTL ? 0 : 6,
//     marginLeft: I18nManager.isRTL ? 6 : 0,
//   },
//   inviteButtonText: {
//     color: colors.white,
//     fontWeight: '500',
//     fontSize: 14,
//   },
//   rolesLegend: {
//     flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
//     padding: 12,
//     backgroundColor: colors.white,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   roleLegendItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginRight: 16,
//   },
//   roleLegendItemRTL: {
//     flexDirection: 'row-reverse',
//     marginRight: 0,
//     marginLeft: 16,
//   },
//   roleLegendIcon: {
//     marginRight: 4,
//   },
//   roleLegendIconRTL: {
//     marginRight: 0,
//     marginLeft: 4,
//   },
//   roleLegendText: {
//     fontSize: 12,
//     color: colors.gray[700],
//     textAlign: 'left',
//   },
//   roleLegendTextRTL: {
//     textAlign: 'right',
//   },
//   listContent: {
//     padding: 16,
//   },
//   userItem: {
//     flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
//     alignItems: 'center',
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 12,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   userAvatar: {
//     width: 40,
//     height: 40,
//     borderRadius: 20,
//     backgroundColor: colors.gray[200],
//     justifyContent: 'center',
//     alignItems: 'center',
//     marginRight: I18nManager.isRTL ? 0 : 12,
//     marginLeft: I18nManager.isRTL ? 12 : 0,
//   },
//   avatarContainer: {
//     width: 40,
//     height: 40,
//     borderRadius: 20,
//     backgroundColor: colors.primary,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   avatarText: {
//     color: colors.white,
//     fontSize: 16,
//     fontWeight: '600',
//   },
//   userInfo: {
//     flex: 1,
//   },
//   userHeader: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginBottom: 8,
//   },
//   userHeaderRTL: {
//     flexDirection: 'row-reverse',
//   },
//   userName: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     marginRight: 8,
//     textAlign: 'left',
//   },
//   userNameRTL: {
//     marginRight: 0,
//     marginLeft: 8,
//     textAlign: 'right',
//   },
//   roleBadge: {
//     paddingHorizontal: 8,
//     paddingVertical: 2,
//     borderRadius: 4,
//   },
//   roleText: {
//     fontSize: 12,
//     fontWeight: '500',
//   },
//   userDetails: {

//   },
//   userDetailItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginBottom: 4,
//   },
//   userDetailItemRTL: {
//     flexDirection: 'row-reverse',
//   },
//   userDetailIcon: {
//     marginRight: 6,
//   },
//   userDetailIconRTL: {
//     marginRight: 0,
//     marginLeft: 6,
//   },
//   userDetailText: {
//     fontSize: 14,
//     color: colors.gray[600],
//     textAlign: 'left',
//   },
//   userDetailTextRTL: {
//     textAlign: 'right',
//   },
//   userAction: {
//     position: 'relative',
//     padding: 8,
//   },
//   actionMenu: {
//     position: 'absolute',
//     top: '100%',
//     right: 0,
//     backgroundColor: colors.white,
//     borderRadius: 8,
//     shadowColor: colors.gray[900],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 3,
//     zIndex: 10,
//     width: 120,
//   },
//   actionMenuRTL: {
//     right: 'auto',
//     left: 0,
//   },
//   actionMenuItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     padding: 12,
//   },
//   actionMenuItemRTL: {
//     flexDirection: 'row-reverse',
//   },
//   actionMenuItemIcon: {
//     marginRight: 8,
//   },
//   actionMenuItemIconRTL: {
//     marginRight: 0,
//     marginLeft: 8,
//   },
//   actionMenuItemText: {
//     fontSize: 14,
//     textAlign: 'left',
//   },
//   actionMenuItemTextRTL: {
//     textAlign: 'right',
//   },
//   emptyContainer: {
//     alignItems: 'center',
//     justifyContent: 'center',
//     padding: 40,
//   },
//   emptyText: {
//     fontSize: 16,
//     color: colors.gray[500],
//     marginTop: 12,
//   },
// });