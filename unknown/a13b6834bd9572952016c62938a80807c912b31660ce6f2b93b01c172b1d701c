import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  SafeAreaView,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import DatePicker from '@/components/DatePicker';
import ImagePicker from '@/components/ImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { useLanguage } from '@/i18n/translations/LanguageProvider';
import { TransactionCategory } from '@/types';
import { DollarSign, Tag, FileText, Calendar } from 'lucide-react-native';
import Toast from 'react-native-toast-message';
import DropdownPicker from '@/components/DropdownPicker';
import { useTranslation } from '@/i18n/useTranslation';

const CURRENCIES = [
  { label: 'USD ($)', value: 'USD' },
  { label: 'EUR (€)', value: 'EUR' },
  { label: 'GBP (£)', value: 'GBP' },
  { label: 'JPY (¥)', value: 'JPY' },
  { label: 'CNY (¥)', value: 'CNY' },
  { label: 'INR (₹)', value: 'INR' },
];

export default function AddCropTransactionScreen() {
  const { id, fieldId } = useLocalSearchParams();
  const cropIdString = Array.isArray(id) ? id[0] : id as string;
  const fieldIdString = Array.isArray(fieldId) ? fieldId[0] : fieldId as string;

  const { t, isRTL } = useTranslation();
  const { getCrop, getField, addCropTransaction, isLoading } = useFarmStore();

  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState('USD');
  const [date, setDate] = useState<Date>(new Date());
  const [description, setDescription] = useState('');
  const [receiptUri, setReceiptUri] = useState<string>('');
  const [crop, setCrop] = useState<any>(null);
  const [field, setField] = useState<any>(null);

  useEffect(() => {
    if (cropIdString) {
      const cropData =getCrop(cropIdString);
      // console.log({cropData},{id})
     
      setCrop(cropData);
    }
    
    if (fieldIdString) {
      const fieldData = getField(fieldIdString);
      setField(fieldData);
    }
  }, [cropIdString, fieldIdString, getCrop, getField]);

  const handleSubmit = async () => {
    if (!amount || !description || !date) {
      Alert.alert(t('error'), t('transaction.fillAllFields'));
      return;
    }

    try {
      // Upload receipt if selected
      let receiptUrl = '';
      if (receiptUri && !receiptUri.startsWith('http')) {
        receiptUrl = await uploadImageAsync(receiptUri, 'receipts');
      }

      const transactionData = {
        cropId: cropIdString,
        fieldId: crop?.fieldId,
        amount: parseFloat(amount),
        currency,
        date: date.toISOString(),
        description,
        receipt: receiptUrl || '',
      };

      await addCropTransaction(transactionData);

      Toast.show({
        type: 'overlay',
        text1: t('success'),
        text2: t('transaction.addSuccess'),
        autoHide: true,
        visibilityTime: 3000,
      });

      // Navigate back to the crop details page
      router.push(`/field/crop/${cropIdString}`);
    } catch (error: any) {
      Toast.show({
        type: 'overlay',
        text1: t('error'),
        text2: error.message || t('transaction.addError'),
        autoHide: true,
        visibilityTime: 3000,
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: t('transaction.addTitle') }} />

      <ScrollView style={styles.scrollView}>
        {crop && (
          <View style={styles.cropInfo}>
            <Text style={styles.cropInfoLabel}>{t('transaction.forCrop')}</Text>
            <Text style={styles.cropInfoValue}>{crop.cropType}</Text>
            {field && (
              <Text style={styles.fieldName}>
                {t('transaction.inField')}: {field.name}
              </Text>
            )}
          </View>
        )}

        <Input
          label={t('transaction.amount')}
          placeholder="0.00"
          value={amount}
          onChangeText={setAmount}
          keyboardType="numeric"
          leftIcon={<DollarSign size={20} color={colors.gray[500]} />}
          required
        />

        {/* <Text style={styles.label}>{t('transaction.currency')}</Text> */}
        <DropdownPicker
          label={t('transaction.currency')}
          options={CURRENCIES}
          onSelect={(val) => setCurrency(val as string)}
          selectedValue={currency}
          isMultiple={false}
        />

        <DatePicker
          label={t('transaction.date')}
          value={date}
          onChange={setDate}
          placeholder={t('transaction.selectDate')}
          required
        />

        <Input
          label={t('transaction.description')}
          placeholder={t('transaction.descriptionPlaceholder')}
          value={description}
          onChangeText={setDescription}
          multiline
          numberOfLines={4}
          leftIcon={<FileText size={20} color={colors.gray[500]} />}
          required
          helperText={t('transaction.descriptionHelper')}
        />

        <ImagePicker
          label={t('transaction.receipt')}
          onImageSelected={setReceiptUri}
          imageUri={receiptUri}
          placeholder={t('transaction.addReceipt')}
        />

        <Button
          title={t('common.save')}
          onPress={handleSubmit}
          loading={isLoading}
          style={styles.button}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    padding: 16,
  },
  cropInfo: {
    backgroundColor: colors.gray[100],
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  cropInfoLabel: {
    fontSize: 14,
    color: colors.gray[500],
    marginBottom: 4,
  },
  cropInfoValue: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  fieldName: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 4,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: colors.text,
  },
  button: {
    marginTop: 24,
    marginBottom: 40,
  },
});
