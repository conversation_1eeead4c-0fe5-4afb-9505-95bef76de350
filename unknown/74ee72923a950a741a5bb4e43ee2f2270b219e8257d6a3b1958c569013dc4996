import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  ActivityIndicator,
  ScrollView,
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { TransactionCategory } from '@/types';
import {
  DollarSign,
  Plus,
  X,
  ChevronDown,
  Calendar,
  Camera,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  ArrowLeft,
} from 'lucide-react-native';
// import DateTimePicker from '@/components/DateTimePicker';
import { analyzeTransactionWithAI, suggestTransactionCorrections } from '@/utils/transaction-analyzer';
// import { uploadImageAsync } from '@/utils/image-upload';
import * as ImagePicker from 'expo-image-picker';
import Button from '@/components/Button';
import DatePicker from '@/components/DatePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';

export default function FinanceScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { entityId, entityType } = useLocalSearchParams<{ entityId: string; entityType: string }>();
  const { user } = useAuthStore();
  const {
    currentFarm,
    addPlantTransaction,
    addGardenTransaction,
    getPlantTransactions,
    getGardenTransactions,
    isLoading
  } = useFarmStore();

  // States for transaction form
  const [activeTab, setActiveTab] = useState<'add' | 'view'>('add');
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState('USD');
  const [category, setCategory] = useState<TransactionCategory>('other');
  const [date, setDate] = useState(new Date());
  const [receiptImage, setReceiptImage] = useState<string>('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  // States for AI analysis
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [hasAppliedCorrections, setHasAppliedCorrections] = useState(false);

  // States for transactions list
  const [transactions, setTransactions] = useState<any[]>([]);
  const [isLoadingTransactions, setIsLoadingTransactions] = useState(false);

  // Dropdown states
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);

  useEffect(() => {
    loadTransactions();
  }, [entityId, entityType]);

  const loadTransactions = async () => {
    if (!entityId || !entityType) return;
    
    setIsLoadingTransactions(true);
    try {
      let result;
      if (entityType === 'plant') {
        result = await getPlantTransactions(entityId);
      } else {
        result = await getGardenTransactions(entityId);
      }
      setTransactions(result || []);
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setIsLoadingTransactions(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const handlePickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setReceiptImage(result.assets[0].uri);
    }
  };

  const handleAnalyze = () => {
    if (!description) {
      alert(t('finance.errorNoDescription'));
      return;
    }

    if (!amount || isNaN(Number(amount))) {
      alert(t('finance.errorInvalidAmount'));
      return;
    }

    setIsAnalyzing(true);

    // Simulate API delay
    setTimeout(() => {
      const numericAmount = parseFloat(amount);
      const analysis = analyzeTransactionWithAI(description, numericAmount);
      setAnalysisResult({
        ...analysis,
        description,
        amount: numericAmount
      });

      // Auto-set the category from analysis
      setCategory(analysis.category);

      setIsAnalyzing(false);
    }, 1000);
  };

  const handleApplyCorrections = () => {
    if (!analysisResult) return;

    const corrections = suggestTransactionCorrections(
      description,
      analysisResult.amount,
      analysisResult.category
    );

    if (corrections.correctedAmount !== undefined) {
      setAmount(corrections.correctedAmount.toString());
      setAnalysisResult({
        ...analysisResult,
        amount: corrections.correctedAmount,
        anomalyDetected: false,
        anomalyReason: undefined
      });
    }

    if (corrections.correctedCategory) {
      setCategory(corrections.correctedCategory);
      setAnalysisResult({
        ...analysisResult,
        category: corrections.correctedCategory,
        anomalyDetected: false,
        anomalyReason: undefined
      });
    }

    setHasAppliedCorrections(true);
  };

  const handleSubmit = async () => {
    if (!description || !amount || isNaN(Number(amount))) {
      alert(t('finance.errorInvalidInput'));
      return;
    }

    try {
      const numericAmount = parseFloat(amount);

      // Upload receipt image if exists
      let receiptUrl = '';
      if (receiptImage) {
        receiptUrl = await uploadImageAsync(receiptImage, 'receipts');
      }

      const transactionData = {
        description,
        amount: numericAmount,
        currency,
        category,
        date: date.toISOString(),
        receiptImage: receiptUrl,
        tags,
        farmId: currentFarm?.id || '',
        createdBy: user?.id || '',
        isExpense: category !== 'harvest_sale',
        linkedItemId: entityId,
        linkedItemType: entityType,
      };

      if (entityType === 'plant') {
        await addPlantTransaction(entityId, transactionData);
      } else {
        await addGardenTransaction(entityId, transactionData);
      }

      // Reset form
      setDescription('');
      setAmount('');
      setCategory('other');
      setDate(new Date());
      setReceiptImage('');
      setTags([]);
      setAnalysisResult(null);
      setHasAppliedCorrections(false);

      // Switch to view tab to see the new transaction
      setActiveTab('view');
      loadTransactions();

    } catch (error) {
      console.error('Error adding transaction:', error);
      alert(t('finance.errorAddingTransaction'));
    }
  };

  // Helper function to format category name
  const formatCategoryName = (category: string): string => {
    return category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const renderTransactionItem = ({ item }: { item: any }) => {
    return (
      <View style={styles.transactionItem}>
        <View style={styles.transactionHeader}>
          <View style={styles.categoryContainer}>
            {item.isExpense ? (
              <ArrowDownRight size={16} color={colors.danger} />
            ) : (
              <ArrowUpRight size={16} color={colors.success} />
            )}
            <Text style={[
              styles.categoryText,
              { color: item.isExpense ? colors.danger : colors.success }
            ]}>
              {formatCategoryName(item.category)}
            </Text>
          </View>

          <Text style={[
            styles.amount,
            { color: item.isExpense ? colors.danger : colors.success }
          ]}>
            {item.isExpense ? '-' : '+'}
            {item.currency} {item.amount.toFixed(2)}
          </Text>
        </View>

        <Text style={styles.description}>{item.description}</Text>

        <View style={styles.transactionFooter}>
          <Text style={styles.date}>
            {new Date(item.date).toLocaleDateString()}
          </Text>

          {item.tags && item.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {item.tags.map((tag: string, index: number) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  const CATEGORIES: { label: string; value: TransactionCategory }[] = [
    { label: t('finance.categories.seedCost'), value: 'seed_cost' },
    { label: t('finance.categories.irrigation'), value: 'irrigation' },
    { label: t('finance.categories.pesticide'), value: 'pesticide' },
    { label: t('finance.categories.fertilizer'), value: 'fertilizer' },
    { label: t('finance.categories.labor'), value: 'labor' },
    { label: t('finance.categories.equipment'), value: 'equipment' },
    { label: t('finance.categories.harvestSale'), value: 'harvest_sale' },
    { label: t('finance.categories.other'), value: 'other' },
  ];

  const CURRENCIES = ['USD', 'EUR', 'GBP', 'PKR', 'INR', 'AED'];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'add' && styles.activeTab]}
          onPress={() => setActiveTab('add')}
        >
          <Text style={[styles.tabText, activeTab === 'add' && styles.activeTabText]}>
            {t('finance.addTransaction')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'view' && styles.activeTab]}
          onPress={() => setActiveTab('view')}
        >
          <Text style={[styles.tabText, activeTab === 'view' && styles.activeTabText]}>
            {t('finance.viewTransactions')}
          </Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.keyboardAvoidingView}
      >
        {activeTab === 'add' ? (
          <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t('finance.description')}</Text>
                <TextInput
                  style={styles.input}
                  value={description}
                  onChangeText={setDescription}
                  placeholder={t('finance.descriptionPlaceholder')}
                  multiline
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t('finance.amount')}</Text>
                <View style={styles.amountContainer}>
                  <TouchableOpacity 
                    style={styles.currencySelector}
                    onPress={() => setShowCurrencyDropdown(!showCurrencyDropdown)}
                  >
                    <Text style={styles.currencyText}>{currency}</Text>
                    <ChevronDown size={16} color={colors.gray[600]} />
                  </TouchableOpacity>

                  <TextInput
                    style={styles.amountInput}
                    value={amount}
                    onChangeText={setAmount}
                    placeholder="0.00"
                    keyboardType="numeric"
                  />
                </View>

                {showCurrencyDropdown && (
                  <View style={styles.dropdown}>
                    {CURRENCIES.map((curr) => (
                      <TouchableOpacity
                        key={curr}
                        style={styles.dropdownItem}
                        onPress={() => {
                          setCurrency(curr);
                          setShowCurrencyDropdown(false);
                        }}
                      >
                        <Text style={styles.dropdownItemText}>{curr}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t('finance.category')}</Text>
                <TouchableOpacity 
                  style={styles.categorySelector}
                  onPress={() => setShowCategoryDropdown(!showCategoryDropdown)}
                >
                  <Text style={styles.categoryText}>
                    {formatCategoryName(category)}
                  </Text>
                  <ChevronDown size={16} color={colors.gray[600]} />
                </TouchableOpacity>

                {showCategoryDropdown && (
                  <View style={styles.dropdown}>
                    {CATEGORIES.map((cat) => (
                      <TouchableOpacity
                        key={cat.value}
                        style={styles.dropdownItem}
                        onPress={() => {
                          setCategory(cat.value);
                          setShowCategoryDropdown(false);
                        }}
                      >
                        <Text style={styles.dropdownItemText}>{cat.label}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t('finance.date')}</Text>
                <DatePicker
                  value={date}
                  onChange={setDate}
                  mode="date"
                  style={styles.datePicker}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t('finance.tags')}</Text>
                <View style={styles.tagsInputContainer}>
                  <TextInput
                    style={styles.tagInput}
                    value={tagInput}
                    onChangeText={setTagInput}
                    placeholder={t('finance.tagsPlaceholder')}
                    onSubmitEditing={handleAddTag}
                  />
                  <TouchableOpacity 
                    style={styles.addTagButton}
                    onPress={handleAddTag}
                  >
                    <Plus size={20} color={colors.white} />
                  </TouchableOpacity>
                </View>

                {tags.length > 0 && (
                  <View style={styles.tagsContainer}>
                    {tags.map((tag, index) => (
                      <View key={index} style={styles.tag}>
                        <Text style={styles.tagText}>{tag}</Text>
                        <TouchableOpacity
                          onPress={() => handleRemoveTag(tag)}
                          style={styles.removeTagButton}
                        >
                          <X size={12} color={colors.gray[600]} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>{t('finance.receiptImage')}</Text>
                <TouchableOpacity 
                  style={styles.imagePickerButton}
                  onPress={handlePickImage}
                >
                  <Camera size={20} color={colors.primary} />
                  <Text style={styles.imagePickerText}>
                    {receiptImage ? t('finance.changeImage') : t('finance.addImage')}
                  </Text>
                </TouchableOpacity>

                {receiptImage && (
                  <Image 
                    source={{ uri: receiptImage }} 
                    style={styles.receiptImage} 
                  />
                )}
              </View>

              <TouchableOpacity 
                style={styles.analyzeButton}
                onPress={handleAnalyze}
                disabled={isAnalyzing || !description || !amount}
              >
                {isAnalyzing ? (
                  <ActivityIndicator color={colors.white} />
                ) : (
                  <>
                    <RefreshCw size={20} color={colors.white} />
                    <Text style={styles.analyzeButtonText}>
                      {t('finance.analyzeTransaction')}
                    </Text>
                  </>
                )}
              </TouchableOpacity>

              {analysisResult && (
                <View style={styles.analysisContainer}>
                  <Text style={styles.analysisTitle}>{t('finance.aiAnalysis')}</Text>

                  <View style={styles.analysisRow}>
                    <Text style={styles.analysisLabel}>{t('finance.transactionType')}:</Text>
                    <Text style={styles.analysisValue}>
                      {analysisResult.isExpense ? t('finance.expense') : t('finance.income')}
                    </Text>
                  </View>

                  <View style={styles.analysisRow}>
                    <Text style={styles.analysisLabel}>{t('finance.category')}:</Text>
                    <Text style={styles.analysisValue}>
                      {formatCategoryName(analysisResult.category)}
                    </Text>
                  </View>

                  {analysisResult.anomalyDetected && (
                    <View style={styles.anomalyContainer}>
                      <Text style={styles.anomalyText}>
                        {analysisResult.anomalyReason}
                      </Text>
                      <TouchableOpacity
                        style={styles.applyCorrectionsButton}
                        onPress={handleApplyCorrections}
                        disabled={hasAppliedCorrections}
                      >
                        <Text style={styles.applyCorrectionsText}>
                          {t('finance.applyCorrections')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}

                  <View style={styles.analysisRow}>
                    <Text style={styles.analysisLabel}>{t('finance.estimatedProfit')}:</Text>
                    <Text style={styles.analysisValue}>
                      {analysisResult.estimatedProfit ?
                        `${analysisResult.currency} ${analysisResult.estimatedProfit.toFixed(2)}` :
                        t('finance.notAvailable')
                      }
                    </Text>
                  </View>
                </View>
              )}

              <Button
                title={t('finance.addTransaction')}
                onPress={handleSubmit}
                loading={isLoading}
                disabled={!description || !amount || isNaN(Number(amount))}
                style={styles.submitButton}
              />
            </View>
          </ScrollView>
        ) : (
          <View style={styles.transactionListContainer}>
            {isLoadingTransactions ? (
              <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
            ) : transactions.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>{t('finance.noTransactionsAdded')}</Text>
              </View>
            ) : (
              <FlatList
                data={transactions}
                keyExtractor={item => item.id}
                renderItem={renderTransactionItem}
                contentContainerStyle={styles.transactionList}
              />
            )}
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  formContainer: {
    padding: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
    color: colors.gray[700],
  },
  input: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    width: 100,
    marginRight: 8,
  },
  currencyText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  amountInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
  },
  categorySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
  },
  categoryText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  dropdown: {
    position: 'absolute',
    top: 74,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    zIndex: 10,
    elevation: 5,
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownItemText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  datePicker: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
  },
  tagsInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    marginRight: 8,
  },
  addTagButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[200],
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: colors.gray[800],
    marginRight: 4,
  },
  removeTagButton: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imagePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
  },
  imagePickerText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 8,
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginTop: 8,
    resizeMode: 'cover',
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.secondary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  analyzeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
    marginLeft: 8,
  },
  analysisContainer: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  analysisTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  analysisRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  analysisLabel: {
    fontSize: 14,
    color: colors.gray[600],
  },
  analysisValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  anomalyContainer: {
    backgroundColor: colors.danger + '20',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  anomalyText: {
    fontSize: 14,
    color: colors.danger,
    marginBottom: 8,
  },
  applyCorrectionsButton: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
  },
  applyCorrectionsText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  submitButton: {
    marginTop: 16,
  },
  transactionListContainer: {
    flex: 1,
  },
  transactionList: {
    padding: 16,
  },
  transactionItem: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
  },
  description: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  date: {
    fontSize: 12,
    color: colors.gray[500],
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
    textAlign: 'center',
  },
});