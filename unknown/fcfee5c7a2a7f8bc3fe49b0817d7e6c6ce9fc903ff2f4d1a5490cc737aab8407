import { Stack } from "expo-router";
import { colors } from "@/constants/colors";
import { useTranslation } from "@/i18n/useTranslation";

export default function AuthLayout() {
  const { isRTL } = useTranslation();
  
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.white,
        },
        headerTintColor: colors.gray[800],
        headerTitleStyle: {
          fontWeight: '600',
          textAlign: isRTL ? 'right' : 'left',
        },
        headerShadowVisible: false,
        // Apply RTL to header back button
        headerLeftContainerStyle: {
          paddingLeft: isRTL ? 0 : 16,
          paddingRight: isRTL ? 16 : 0,
        },
        headerRightContainerStyle: {
          paddingLeft: isRTL ? 16 : 0,
          paddingRight: isRTL ? 0 : 16,
        },
      }}
    >
      <Stack.Screen name="language-selection" options={{ headerShown: false }} />
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="register" options={{ headerShown: false }} />
      <Stack.Screen name="forgot-password" options={{ title: "Reset Password" }} />
    </Stack>
  );
}