import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Image,
  FlatList,
} from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import {
  Calendar,
  Clock,
  FileText,
  Download,
  Share2,
  Printer,
  CheckCircle,
  XCircle,
  Clock3,
  Users,
  Leaf,
  Droplets,
  Sun,
  CloudRain,
  Thermometer,
  Wind,
  ArrowRight,
  BarChart3,
  CheckSquare,
} from 'lucide-react-native';
import { normalizeDate } from '@/utils/dateUtils';
import { capitalizeFirstLetter } from '@/utils/util';

export default function DailyOperationsReport() {
  const { id } = useLocalSearchParams()
  const { currentFarm, tasks, getReportById } = useFarmStore();
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Mock tasks data
  const dailyTasks = [
    {
      id: '1',
      title: 'Irrigation Check',
      assignedTo: 'John Doe',
      status: 'completed',
      priority: 'high',
      category: 'maintenance',
      location: 'North Field',
      startTime: '08:00 AM',
      endTime: '09:30 AM',
      duration: 90,
      notes: 'All irrigation systems working properly. Replaced one broken sprinkler head.'
    },
    {
      id: '2',
      title: 'Harvest Tomatoes',
      assignedTo: 'Sarah Williams',
      status: 'completed',
      priority: 'medium',
      category: 'harvest',
      location: 'Vegetable Garden',
      startTime: '07:00 AM',
      endTime: '10:00 AM',
      duration: 180,
      notes: 'Harvested 45kg of tomatoes. Quality is excellent.'
    },
    {
      id: '3',
      title: 'Apply Fertilizer',
      assignedTo: 'Mike Johnson',
      status: 'in_progress',
      priority: 'medium',
      category: 'cultivation',
      location: 'South Field',
      startTime: '10:30 AM',
      endTime: '12:30 PM',
      duration: 120,
      notes: 'Applied organic fertilizer to corn crops.'
    },
    {
      id: '4',
      title: 'Tractor Maintenance',
      assignedTo: 'Robert Smith',
      status: 'pending',
      priority: 'low',
      category: 'equipment',
      location: 'Workshop',
      startTime: '02:00 PM',
      endTime: '04:00 PM',
      duration: 120,
      notes: 'Scheduled maintenance for the main tractor.'
    },
    {
      id: '5',
      title: 'Pest Inspection',
      assignedTo: 'Emily Davis',
      status: 'completed',
      priority: 'high',
      category: 'inspection',
      location: 'All Fields',
      startTime: '09:00 AM',
      endTime: '11:00 AM',
      duration: 120,
      notes: 'Found minor aphid infestation in the apple orchard. Applied organic pesticide.'
    },
  ];

  // Mock weather data
  const weatherData = {
    date: new Date().toLocaleDateString(),
    temperature: {
      morning: 18,
      afternoon: 24,
      evening: 20,
      unit: '°C'
    },
    conditions: 'Partly Cloudy',
    precipitation: {
      amount: 0,
      probability: 10,
      unit: 'mm'
    },
    humidity: 65,
    wind: {
      speed: 12,
      direction: 'NW',
      unit: 'km/h'
    },
    sunrise: '06:15 AM',
    sunset: '08:45 PM'
  };

  // Mock staff attendance
  const staffAttendance = [
    { id: '1', name: 'John Doe', role: 'Field Manager', status: 'present', hours: 8.5 },
    { id: '2', name: 'Sarah Williams', role: 'Harvester', status: 'present', hours: 9 },
    { id: '3', name: 'Mike Johnson', role: 'Cultivator', status: 'present', hours: 8 },
    { id: '4', name: 'Robert Smith', role: 'Mechanic', status: 'absent', hours: 0 },
    { id: '5', name: 'Emily Davis', role: 'Pest Control', status: 'present', hours: 8.5 },
  ];

  // Task summary
  const taskSummary = {
    total: dailyTasks.length,
    completed: dailyTasks.filter(task => task.status === 'completed').length,
    inProgress: dailyTasks.filter(task => task.status === 'in_progress').length,
    pending: dailyTasks.filter(task => task.status === 'pending').length,
    categories: {
      maintenance: dailyTasks.filter(task => task.category === 'maintenance').length,
      harvest: dailyTasks.filter(task => task.category === 'harvest').length,
      cultivation: dailyTasks.filter(task => task.category === 'cultivation').length,
      equipment: dailyTasks.filter(task => task.category === 'equipment').length,
      inspection: dailyTasks.filter(task => task.category === 'inspection').length,
    }
  };

  // Notes and observations
  const notesAndObservations = [
    { id: '1', text: 'Tomato yield is higher than expected. Consider increasing harvest frequency.', author: 'John Doe', timestamp: '10:30 AM' },
    { id: '2', text: 'South field irrigation system needs maintenance next week.', author: 'Mike Johnson', timestamp: '12:45 PM' },
    { id: '3', text: 'Weather forecast shows rain for the next three days. Adjust outdoor activities accordingly.', author: 'Sarah Williams', timestamp: '03:15 PM' },
  ];

  const [report, setReport] = useState({})
  const loadReport = async () => {
    // const response = await fetch('/api/report');
    const response = await getReportById(id)
    // console.log({ response })
    setReport(response)
  }
  useEffect(() => {
    // Fetch data from API or database
    // ...
    if (id) {
      loadReport()
    }
  }, []);
  useEffect(() => {
    // Simulate loading report data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return colors.success;
      case 'in_progress':
        return colors.warning;
      case 'pending':
        return colors.info;
      case 'overdue':
        return colors.danger;
      case 'present':
        return colors.success;
      case 'absent':
        return colors.danger;
      default:
        return colors.gray[500];
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return colors.danger;
      case 'medium':
        return colors.warning;
      case 'low':
        return colors.info;
      default:
        return colors.gray[500];
    }
  };

  const renderTaskItem = ({ item }: { item: any }) => (
    <View style={styles.taskItem}>
      <View style={styles.taskHeader}>
        <View style={styles.taskTitleContainer}>
          <View style={[styles.taskStatusDot, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.taskTitle}>{item.title}</Text>
        </View>
        <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) + '20' }]}>
          <Text style={[styles.priorityText, { color: getPriorityColor(item.priority) }]}>
            {item.priority}
          </Text>
        </View>
      </View>

      <View style={styles.taskDetails}>
        <View style={styles.taskDetailRow}>
          <Users size={14} color={colors.gray[600]} />
          <Text style={styles.taskDetailText}>{capitalizeFirstLetter(item.assignedTo)}</Text>
        </View>

        {/* <View style={styles.taskDetailRow}>
          <Leaf size={14} color={colors.gray[600]} />
          <Text style={styles.taskDetailText}>{item.location?.gardenid}</Text>
        </View> */}

        <View style={styles.taskDetailRow}>
          <Clock size={14} color={colors.gray[600]} />
          {/* {item.startTime} - */}
          <Text style={styles.taskDetailText}>{normalizeDate(item.dueDate)}</Text>
        </View>
      </View>

      {item.notes && (
        <View style={styles.taskNotes}>
          <Text style={styles.taskNotesLabel}>Notes:</Text>
          <Text style={styles.taskNotesText}>{item.notes}</Text>
        </View>
      )}
    </View>
  );

  const renderStaffItem = ({ item }: { item: any }) => (
    <View style={styles.staffItem}>
      <View style={styles.staffHeader}>
        <Text style={styles.staffName}>{item.name}</Text>
        <View style={[
          styles.staffStatusBadge,
          { backgroundColor: getStatusColor(item.status) + '20' }
        ]}>
          <Text style={[
            styles.staffStatusText,
            { color: getStatusColor(item.status) }
          ]}>
            {item.status}
          </Text>
        </View>
      </View>

      <Text style={styles.staffRole}>{item.role}</Text>

      {item.status === 'present' && (
        <View style={styles.staffHours}>
          <Clock3 size={14} color={colors.gray[600]} />
          <Text style={styles.staffHoursText}>{item.hours} hours</Text>
        </View>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Stack.Screen
          options={{
            title: 'Daily Operations Report',
            headerShown: true,
          }}
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Generating report...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Daily Operations Report',
          headerShown: true,
        }}
      />

      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.reportHeader}>
          <View style={styles.farmInfo}>
            <Text style={styles.farmName}>{currentFarm?.name || 'Farm Name'}</Text>
            <View style={styles.reportMetaRow}>
              <Calendar size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                {normalizeDate(report?.date)}
              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <Clock size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                Generated at {normalizeDate(report?.createdAt)}
              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <FileText size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>Daily Operations Report</Text>
            </View>
          </View>

          <View style={styles.reportActions}>
            <TouchableOpacity style={styles.reportAction}>
              <Download size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Share2 size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Printer size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>Task Summary</Text>

          <View style={styles.summaryCards}>
            <View style={styles.summaryCard}>
              <View style={[styles.summaryIconContainer, { backgroundColor: colors.primary + '20' }]}>
                <CheckSquare size={24} color={colors.primary} />
              </View>
              <Text style={styles.summaryValue}>{report?.taskSummary?.total || '--'}</Text>
              <Text style={styles.summaryLabel}>Total Tasks</Text>
            </View>

            <View style={styles.summaryCard}>
              <View style={[styles.summaryIconContainer, { backgroundColor: colors.success + '20' }]}>
                <CheckCircle size={24} color={colors.success} />
              </View>
              <Text style={styles.summaryValue}>{report?.taskSummary?.completed || '--'}</Text>
              <Text style={styles.summaryLabel}>Completed</Text>
            </View>

            <View style={styles.summaryCard}>
              <View style={[styles.summaryIconContainer, { backgroundColor: colors.warning + '20' }]}>
                <Clock3 size={24} color={colors.warning} />
              </View>
              <Text style={styles.summaryValue}>{report?.taskSummary?.inProgress || '--'}</Text>
              <Text style={styles.summaryLabel}>In Progress</Text>
            </View>
          </View>

          {/* <View style={styles.taskCategoriesContainer}>
            <Text style={styles.taskCategoriesTitle}>Task Categories</Text>
            <View style={styles.taskCategories}>
              {Object.entries(taskSummary.categories).map(([category, count]) => (
                <View key={category} style={styles.taskCategory}>
                  <Text style={styles.taskCategoryName}>{category}</Text>
                  <Text style={styles.taskCategoryCount}>{count}</Text>
                </View>
              ))}
            </View>
          </View> */}
        </View>

        {/* <View style={styles.weatherSection}>
          <Text style={styles.sectionTitle}>Weather Conditions</Text>

          <View style={styles.weatherCard}>
            <View style={styles.weatherHeader}>
              <View style={styles.weatherCondition}>
                <CloudRain size={32} color={colors.primary} />
                <Text style={styles.weatherConditionText}>{weatherData.conditions}</Text>
              </View>
              <Text style={styles.weatherDate}>{weatherData.date}</Text>
            </View>

            <View style={styles.weatherDetails}>
              <View style={styles.weatherDetail}>
                <Thermometer size={16} color={colors.gray[600]} />
                <Text style={styles.weatherDetailText}>
                  {weatherData.temperature.morning}°C / {weatherData.temperature.afternoon}°C
                </Text>
              </View>

              <View style={styles.weatherDetail}>
                <Droplets size={16} color={colors.gray[600]} />
                <Text style={styles.weatherDetailText}>
                  Humidity: {weatherData.humidity}%
                </Text>
              </View>

              <View style={styles.weatherDetail}>
                <Wind size={16} color={colors.gray[600]} />
                <Text style={styles.weatherDetailText}>
                  Wind: {weatherData.wind.speed} {weatherData.wind.unit} {weatherData.wind.direction}
                </Text>
              </View>

              <View style={styles.weatherDetail}>
                <Sun size={16} color={colors.gray[600]} />
                <Text style={styles.weatherDetailText}>
                  Sunrise: {weatherData.sunrise} | Sunset: {weatherData.sunset}
                </Text>
              </View>
            </View>
          </View>
        </View> */}

        <View style={styles.tasksSection}>
          <Text style={styles.sectionTitle}>Daily Tasks</Text>

          <View style={styles.tasksList}>
            {(report?.tasks || []).map(task => (
              <View key={task.id} style={styles.taskItemContainer}>
                {renderTaskItem({ item: task })}
              </View>
            ))}
          </View>
        </View>

        {/* <View style={styles.staffSection}>
          <Text style={styles.sectionTitle}>Staff Attendance</Text>

          <View style={styles.staffAttendanceSummary}>
            <View style={styles.staffAttendanceItem}>
              <Text style={styles.staffAttendanceValue}>
                {staffAttendance.filter(staff => staff.status === 'present').length}
              </Text>
              <Text style={styles.staffAttendanceLabel}>Present</Text>
            </View>

            <View style={styles.staffAttendanceItem}>
              <Text style={styles.staffAttendanceValue}>
                {staffAttendance.filter(staff => staff.status === 'absent').length}
              </Text>
              <Text style={styles.staffAttendanceLabel}>Absent</Text>
            </View>

            <View style={styles.staffAttendanceItem}>
              <Text style={styles.staffAttendanceValue}>
                {staffAttendance.reduce((total, staff) => total + staff.hours, 0)}
              </Text>
              <Text style={styles.staffAttendanceLabel}>Total Hours</Text>
            </View>
          </View>

          <View style={styles.staffList}>
            {staffAttendance.map(staff => (
              <View key={staff.id} style={styles.staffItemContainer}>
                {renderStaffItem({ item: staff })}
              </View>
            ))}
          </View>
        </View> */}

        <View style={styles.notesSection}>
          <Text style={styles.sectionTitle}>Notes & Observations</Text>

          {report?.notesAndObservations?.length ?
            <>{
              (report?.notesAndObservations || []).map(note => (
                <View key={note.id} style={styles.noteItem}>
                  <Text style={styles.noteText}>{note.text}</Text>
                  <View style={styles.noteMeta}>
                    <Text style={styles.noteAuthor}>{note.author}</Text>
                    <Text style={styles.noteTimestamp}>{note.timestamp}</Text>
                  </View>
                </View>
              ))}
            </>
            :
            <>
            <Text style={styles.noteAuthor}>Notes Not Available</Text>
            </>}

        </View>

        <View style={styles.signatureSection}>
          <Text style={styles.signatureLabel}>Report Generated By:</Text>
          <Text style={styles.signatureName}>{user?.createdBy || 'Farm Manager'}</Text>
          <Text style={styles.signatureDate}>{normalizeDate(report?.createdAt)}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[700],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmInfo: {
    flex: 1,
  },
  farmName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
  },
  reportMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  reportMetaText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 8,
  },
  reportActions: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  reportAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  summarySection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 12,
  },
  summaryCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: colors.gray[50],
    marginHorizontal: 4,
  },
  summaryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
  },
  summaryLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
    textAlign: 'center',
  },
  taskCategoriesContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  taskCategoriesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  taskCategories: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  taskCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  taskCategoryName: {
    fontSize: 12,
    color: colors.gray[700],
    textTransform: 'capitalize',
  },
  taskCategoryCount: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 4,
  },
  weatherSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  weatherCard: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  weatherHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  weatherCondition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  weatherConditionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginLeft: 8,
  },
  weatherDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  weatherDetails: {
    backgroundColor: colors.white,
    borderRadius: 6,
    padding: 10,
  },
  weatherDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  weatherDetailText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 8,
  },
  tasksSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tasksList: {
    marginTop: 8,
  },
  taskItemContainer: {
    marginBottom: 12,
  },
  taskItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  taskStatusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
  },
  taskDetails: {
    marginBottom: 8,
  },
  taskDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskDetailText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 8,
  },
  taskNotes: {
    backgroundColor: colors.white,
    borderRadius: 6,
    padding: 8,
  },
  taskNotesLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 4,
  },
  taskNotesText: {
    fontSize: 12,
    color: colors.gray[600],
    lineHeight: 18,
  },
  staffSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  staffAttendanceSummary: {
    flexDirection: 'row',
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  staffAttendanceItem: {
    flex: 1,
    alignItems: 'center',
  },
  staffAttendanceValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
  },
  staffAttendanceLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
  },
  staffList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  staffItemContainer: {
    width: '50%',
    padding: 4,
  },
  staffItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    // height: '100%',
  },
  staffHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  staffName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
  },
  staffStatusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  staffStatusText: {
    fontSize: 10,
    fontWeight: '500',
  },
  staffRole: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 8,
  },
  staffHours: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  staffHoursText: {
    fontSize: 12,
    color: colors.gray[700],
    marginLeft: 4,
  },
  notesSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  noteItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  noteText: {
    fontSize: 14,
    color: colors.gray[800],
    lineHeight: 20,
    marginBottom: 8,
  },
  noteMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  noteAuthor: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.gray[700],
  },
  noteTimestamp: {
    fontSize: 12,
    color: colors.gray[600],
  },
  signatureSection: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  signatureLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  signatureName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  signatureDate: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
  },
});