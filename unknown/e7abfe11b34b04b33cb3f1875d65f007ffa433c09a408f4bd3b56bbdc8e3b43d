import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useLanguage } from '@/i18n/translations/LanguageProvider';
import { CropTransaction, TransactionCategory } from '@/types';
import { 
  DollarSign, 
  Plus,
  ChevronRight,
  Calendar,
  Tag
} from 'lucide-react-native';
import Button from '@/components/Button';

// Helper function to get color for transaction category
const getCategoryColor = (category: TransactionCategory): string => {
  const colorMap: Record<TransactionCategory, string> = {
    seed_cost: '#4CAF50',
    irrigation: '#2196F3',
    pesticide: '#F44336',
    fertilizer: '#9C27B0',
    labor: '#FF9800',
    equipment: '#607D8B',
    harvest_sale: '#8BC34A',
    other: '#9E9E9E'
  };
  
  return colorMap[category] || colorMap.other;
};

// Helper function to get icon for transaction category
const getCategoryIcon = (category: TransactionCategory) => {
  switch (category) {
    case 'seed_cost':
      return <Text style={styles.categoryIcon}>🌱</Text>;
    case 'irrigation':
      return <Text style={styles.categoryIcon}>💧</Text>;
    case 'pesticide':
      return <Text style={styles.categoryIcon}>🐛</Text>;
    case 'fertilizer':
      return <Text style={styles.categoryIcon}>🧪</Text>;
    case 'labor':
      return <Text style={styles.categoryIcon}>👨‍🌾</Text>;
    case 'equipment':
      return <Text style={styles.categoryIcon}>🚜</Text>;
    case 'harvest_sale':
      return <Text style={styles.categoryIcon}>💰</Text>;
    default:
      return <Text style={styles.categoryIcon}>📋</Text>;
  }
};

// Helper function to format category name
const formatCategoryName = (category: string): string => {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const TransactionItem = ({ transaction, onPress }: { 
  transaction: CropTransaction; 
  onPress: () => void;
}) => {
  return (
    <TouchableOpacity style={styles.transactionItem} onPress={onPress}>
      <View style={styles.transactionHeader}>
        <View style={styles.categoryContainer}>
          {getCategoryIcon(transaction.category)}
          <View 
            style={[
              styles.categoryBadge, 
              { backgroundColor: getCategoryColor(transaction.category) }
            ]}
          >
            <Text style={styles.categoryText}>
              {formatCategoryName(transaction.category)}
            </Text>
          </View>
        </View>
        
        <Text style={[
          styles.amount,
          transaction.category === 'harvest_sale' ? styles.positiveAmount : styles.negativeAmount
        ]}>
          {transaction.category === 'harvest_sale' ? '+' : '-'}
          {transaction.currency} {transaction.amount.toFixed(2)}
        </Text>
      </View>
      
      <Text style={styles.description} numberOfLines={2}>
        {transaction.description}
      </Text>
      
      <View style={styles.transactionFooter}>
        <View style={styles.dateContainer}>
          <Calendar size={14} color={colors.gray[500]} />
          <Text style={styles.date}>
            {new Date(transaction.date).toLocaleDateString()}
          </Text>
        </View>
        
        {transaction.isHighCost && (
          <View style={styles.highCostBadge}>
            <Tag size={12} color={colors.white} />
            <Text style={styles.highCostText}>High Cost</Text>
          </View>
        )}
        
        <ChevronRight size={16} color={colors.gray[400]} />
      </View>
    </TouchableOpacity>
  );
};

export default function CropTransactionsScreen() {
  const { id } = useLocalSearchParams();
  const cropId = Array.isArray(id) ? id[0] : id as string;

  const { t, isRTL } = useLanguage();
  const { 
    getCrop, 
    getField, 
    fetchCropTransactions, 
    isLoading 
  } = useFarmStore();

  const [crop, setCrop] = useState<any>(null);
  const [field, setField] = useState<any>(null);
  const [transactions, setTransactions] = useState<CropTransaction[]>([]);

  useEffect(() => {
    loadData();
  }, [cropId]);

  const loadData = async () => {
    if (!cropId) return;
    
    // Get crop and field data
    const cropData = getCrop(cropId);
    setCrop(cropData);
    
    if (cropData) {
      const fieldData = getField(cropData.fieldId);
      setField(fieldData);
      
      // Fetch transactions
      const fetchedTransactions = await fetchCropTransactions(cropId);
      setTransactions(fetchedTransactions);
    }
  };

  const handleAddTransaction = () => {
    router.push({
      pathname: '/field/crop/transaction/add',
      params: { cropId, fieldId: crop?.fieldId }
    });
  };

  const handleTransactionPress = (transaction: CropTransaction) => {
    router.push(`/field/crop/transaction/${transaction.id}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: t('transaction.title') }} />
      
      {crop && (
        <View style={styles.cropHeader}>
          <Text style={styles.cropType}>{crop.cropType}</Text>
          {field && <Text style={styles.fieldName}>{field.name}</Text>}
        </View> 
      )}
      
      <Button
        title={t('transaction.add')}
        onPress={handleAddTransaction}
        style={styles.addButton}
        leftIcon={<Plus size={20} color={colors.white} />}
      />
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={transactions}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TransactionItem 
              transaction={item} 
              onPress={() => handleTransactionPress(item)}
            />
          )}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  cropHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  cropType: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  fieldName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[600],
  },
  addButton: {
    margin: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  transactionItem: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
  },
  positiveAmount: {
    color: colors.success,
  },
  negativeAmount: {
    color: colors.danger,
  },
  description: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    fontSize: 12,
    color: colors.gray[500],
    marginLeft: 4,
  },
  highCostBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warning,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  highCostText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
    marginLeft: 4,
  },
});