import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  Platform,
  Alert,
  Text,
  Modal
} from 'react-native';
// import { Text } from '@/components/Themed';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
// import { BarChart } from 'react-native-gifted-charts';
import { BarChart } from 'react-native-chart-kit';
// import { BarChart, StackedBarChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import {
  Plus,
  FileText,
  Calendar,
  DollarSign,
  ArrowDownCircle,
  ArrowUpCircle,
  Filter,
  Download,
  ChevronDown,
  X,
  ArrowUpRight,
  ArrowDownRight,
  BarChart2,
} from 'lucide-react-native';
import Button from '@/components/Button';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
// import { format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import DropdownPicker from '@/components/DropdownPicker';
import { Input } from '@/components/Input';
import { useLocalSearchParams } from 'expo-router';
import CustomBarChart from '@/components/CustomBarChart';
import { FinanceRecord } from '@/types';

// Screen width for charts
const screenWidth = Dimensions.get('window').width;

export default function FinanceScreen() {
  const { t, isRTL } = useTranslation();
  const { id, entity } = useLocalSearchParams();
  const {
    currentFarm,
    isLoading,
    error,
    financeRecords,
    financeSummary,
    monthlyFinanceData,
    addFinanceRecord,
    fetchFinanceRecords,
    getEntityName,
    exportFinanceRecordsToCSV,
    animals,
    fields,
    gardens,
    plants,
    equipment,
    crops,
    getAnimal
  } = useFarmStore();
  const { user } = useAuthStore();

  // View toggle state
  // const [activeView, setActiveView] = useState<'dashboard' | 'add' | 'view'>('dashboard');
  const [activeView, setActiveView] = useState<'dashboard' | 'add' | 'view'>(user?.role === 'owner' ? 'dashboard' : 'add');
  // console.log({ financeRecords }, { financeSummary }, {monthlyFinanceData})
  // Add Record Form State
  const [entityTypeOpen, setEntityTypeOpen] = useState(false);
  const [entityType, setEntityType] = useState<string | null>(entity as string ?? null);
  const [entityOptions, setEntityOptions] = useState<any[]>([
    { label: t('entity_types.animal'), value: 'animal' },//
    { label: t('entity_types.crop'), value: 'crop' },
    { label: t('entity_types.field'), value: 'field' },
    { label: t('entity_types.garden'), value: 'garden' },
    { label: t('entity_types.plant'), value: 'plant' },
    { label: t('entity_types.equipment'), value: 'equipment' },
  ]);

  const [entityIdOpen, setEntityIdOpen] = useState(false);
  const [entityId, setEntityId] = useState<string | null>(id as string ?? null);
  const [entityIdOptions, setEntityIdOptions] = useState<any[]>([]);

  const [transactionType, setTransactionType] = useState<'income' | 'expense'>('expense');

  const [categoryOpen, setCategoryOpen] = useState(false);
  const [category, setCategory] = useState<string | null>(null);
  const [categoryOptions, setCategoryOptions] = useState<any[]>([
    // 🔸 Animal (Expenses)
    { id: 'feed', label: t('categories.feed'), value: 'feed', parentId: 'animal', type: 'expense' },
    { id: 'treatment', label: t('categories.treatment'), value: 'treatment', parentId: 'animal', type: 'expense' },
    { id: 'medicine', label: t('categories.medicine'), value: 'medicine', parentId: 'animal', type: 'expense' },
    { id: 'vaccination', label: t('categories.vaccination'), value: 'vaccination', parentId: 'animal', type: 'expense' },
    { id: 'breeding', label: t('categories.breeding'), value: 'breeding', parentId: 'animal', type: 'expense' },
    { id: 'animal_transport', label: t('categories.animal_transport'), value: 'animal_transport', parentId: 'animal', type: 'expense' },

    // 🔸 Animal (Income)
    { id: 'animal_sales', label: t('categories.sales'), value: 'animal_sales', parentId: 'animal', type: 'income' },
    { id: 'animal_byproducts', label: t('categories.animal_byproducts'), value: 'animal_byproducts', parentId: 'animal', type: 'income' },

    // 🌾 Crop (Expenses)
    { id: 'seeds', label: t('categories.seeds'), value: 'seeds', parentId: 'crop', type: 'expense' },
    { id: 'fertilizer', label: t('categories.fertilizer'), value: 'fertilizer', parentId: 'crop', type: 'expense' },
    { id: 'pesticides', label: t('categories.pesticides'), value: 'pesticides', parentId: 'crop', type: 'expense' },
    { id: 'irrigation', label: t('categories.irrigation'), value: 'irrigation', parentId: 'crop', type: 'expense' },
    { id: 'crop_transport', label: t('categories.crop_transport'), value: 'crop_transport', parentId: 'crop', type: 'expense' },

    // 🌾 Crop (Income)
    { id: 'crop_sales', label: t('categories.sales'), value: 'crop_sales', parentId: 'crop', type: 'income' },

    // 🌱 Field (Expenses)
    { id: 'soil_testing', label: t('categories.soil_testing'), value: 'soil_testing', parentId: 'field', type: 'expense' },
    { id: 'field_preparation', label: t('categories.field_preparation'), value: 'field_preparation', parentId: 'field', type: 'expense' },
    { id: 'harvest', label: t('categories.harvest'), value: 'harvest', parentId: 'field', type: 'expense' },

    // 🌱 Garden (Expenses)
    { id: 'plant_care', label: t('categories.plant_care'), value: 'plant_care', parentId: 'garden', type: 'expense' },
    { id: 'gardening_tools', label: t('categories.gardening_tools'), value: 'gardening_tools', parentId: 'garden', type: 'expense' },

    // 🌿 Plant (Expenses)
    { id: 'plant_seeds', label: t('categories.plant_seeds'), value: 'plant_seeds', parentId: 'plant', type: 'expense' },
    { id: 'plant_nutrition', label: t('categories.plant_nutrition'), value: 'plant_nutrition', parentId: 'plant', type: 'expense' },

    // ⚙️ Equipment (Expenses)
    { id: 'equipment_maintenance', label: t('categories.equipment_maintenance'), value: 'equipment_maintenance', parentId: 'equipment', type: 'expense' },
    { id: 'fuel', label: t('categories.fuel'), value: 'fuel', parentId: 'equipment', type: 'expense' },
    { id: 'equipment_repair', label: t('categories.equipment_repair'), value: 'equipment_repair', parentId: 'equipment', type: 'expense' },

    // ⚙️ Equipment (Income)
    { id: 'equipment_rental_income', label: t('categories.equipment_rental_income'), value: 'equipment_rental_income', parentId: 'equipment', type: 'income' },
    { id: 'equipment_sales', label: t('categories.sales'), value: 'equipment_sales', parentId: 'equipment', type: 'income' },

    // 👷 General (can apply to any entity)
    { id: 'labor', label: t('categories.labor'), value: 'labor', parentId: null, type: 'expense' },
    { id: 'transport', label: t('categories.transport'), value: 'transport', parentId: null, type: 'expense' },
    { id: 'consulting', label: t('categories.consulting'), value: 'consulting', parentId: null, type: 'expense' },

    // ❔ Other
    { id: 'other', label: t('categories.other'), value: 'other', parentId: null, type: 'expense' }
  ]);

  const [amount, setAmount] = useState('');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [currencyOpen, setCurrencyOpen] = useState(false);
  const [currency, setCurrency] = useState('PKR');
  const [currencyOptions, setCurrencyOptions] = useState([
    { label: 'PKR (₨)', value: 'PKR' },
    { label: 'USD ($)', value: 'USD' },
  ]);

  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);

  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter State
  const [filterEntityTypeOpen, setFilterEntityTypeOpen] = useState(false);
  const [filterEntityType, setFilterEntityType] = useState<string | null>(null);

  const [filterEntityIdOpen, setFilterEntityIdOpen] = useState(false);
  const [filterEntityId, setFilterEntityId] = useState<string | null>(null);
  const [filterEntityIdOptions, setFilterEntityIdOptions] = useState<any[]>([]);

  const [filterTransactionType, setFilterTransactionType] = useState<'all' | 'income' | 'expense'>('all');

  const [filterCurrencyOpen, setFilterCurrencyOpen] = useState(false);
  const [filterCurrency, setFilterCurrency] = useState<string | null>(null);

  const [filterCategoryOpen, setFilterCategoryOpen] = useState(false);
  const [filterCategory, setFilterCategory] = useState<string | null>(null);

  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [filterDate, setFilterDate] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Chart State
  const [chartPeriod, setChartPeriod] = useState<'monthly' | 'weekly' | 'yearly'>('monthly');
  const [chartData, setChartData] = useState<any>({
    labels: [],
    datasets: [{ data: [] }, { data: [] }],
    legend: [],
    // data: [],
    barColors: [],
  });
  // Initialize entity types and categories
  useEffect(() => {
    // Entity types
    setEntityOptions([
      { label: t('entity_types.animal'), value: 'animal' },
      { label: t('entity_types.crop'), value: 'crop' },
      { label: t('entity_types.field'), value: 'field' },
      { label: t('entity_types.garden'), value: 'garden' },
      { label: t('entity_types.plant'), value: 'plant' },
      { label: t('entity_types.equipment'), value: 'equipment' },
    ]);

    // Categories
    // setCategoryOptions([
    //   { label: t('categories.feed'), value: 'feed' },
    //   { label: t('categories.treatment'), value: 'treatment' },
    //   { label: t('categories.equipment_maintenance'), value: 'equipment_maintenance' },
    //   { label: t('categories.medicine'), value: 'medicine' },
    //   { label: t('categories.transport'), value: 'transport' },
    //   { label: t('categories.seeds'), value: 'seeds' },
    //   { label: t('categories.fertilizer'), value: 'fertilizer' },
    //   { label: t('categories.labor'), value: 'labor' },
    //   { label: t('categories.harvest'), value: 'harvest' },
    //   { label: t('categories.sales'), value: 'sales' },
    //   { label: t('categories.other'), value: 'other' },
    // ]);

    // Load finance records when view is active
    if (activeView === 'view' || activeView === 'dashboard') {
      loadFinanceRecords();
    }
  }, [activeView, currentFarm?.id, t]);

  // Load entities based on selected entity type
  useEffect(() => {
    if (entityType) {
      loadEntitiesByType(entityType);
    }
  }, [entityType, animals, fields, gardens, plants, equipment, crops]);

  // Load filter entities based on selected filter entity type
  useEffect(() => {
    if (filterEntityType) {
      loadEntitiesByType(filterEntityType, true);
    }
  }, [filterEntityType, animals, fields, gardens, plants, equipment, crops]);
  // 

  // Apply filters when they change
  useEffect(() => {
    if (activeView === 'view') {
      loadFinanceRecords();
    }
    if (activeView === 'dashboard') {
      // prepareChartData();
      const res = generateChartData(financeRecords, chartPeriod)
      // console.log({ res })
      setChartData(res)
    }
  }, [filterEntityType, chartPeriod, monthlyFinanceData, filterEntityId, filterTransactionType, filterCurrency, filterCategory, startDate, endDate]);
  const generateChartData = (
    records: FinanceRecord[],
    period: 'weekly' | 'monthly' | 'yearly'
  ) => {
    if (!records || records.length === 0) {
      return {
        labels: [],
        income: [],
        expense: [],
        barColors: ['#4ade80', '#f87171'], // Green for income, red for expense
      };
    }

    const incomeMap: Record<string, number> = {};
    const expenseMap: Record<string, number> = {};
    const labelSet = new Set<string>();

    records.forEach((record) => {
      const date = record.date.toDate();
      let label = '';

      if (period === 'weekly') {
        const startOfWeek = new Date(date);
        startOfWeek.setDate(date.getDate() - date.getDay()); // Sunday
        label = `${startOfWeek.getMonth() + 1}/${startOfWeek.getDate()}`;
      }

      else if (period === 'monthly') {
        label = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
      }

      else if (period === 'yearly') {
        label = `${date.getFullYear()}`;
      }

      labelSet.add(label);

      if (!(label in incomeMap)) incomeMap[label] = 0;
      if (!(label in expenseMap)) expenseMap[label] = 0;

      if (record.transactionType === 'income') {
        incomeMap[label] += record.amount;
      } else {
        expenseMap[label] += record.amount;
      }
    });

    // Sort labels chronologically
    const sortedLabels = Array.from(labelSet).sort((a, b) => {
      const parse = (label: string) => {
        if (period === 'weekly') {
          const [m, d] = label.split('/').map(Number);
          return new Date(new Date().getFullYear(), m - 1, d).getTime();
        } else if (period === 'monthly') {
          const [mon, year] = label.split(' ');
          return new Date(+year, new Date(`${mon} 1`).getMonth(), 1).getTime();
        } else {
          return new Date(+label, 0, 1).getTime();
        }
      };
      return parse(a) - parse(b);
    });

    return {
      labels: sortedLabels,
      income: sortedLabels.map(label => incomeMap[label]),
      expense: sortedLabels.map(label => expenseMap[label]),
      barColors: ['#4ade80', '#f87171'],
    };
  };

  const processRecordsForChart = (records: any[], period: 'week' | 'year') => {
    if (!records || records.length === 0) return null;

    const incomeMap: { [key: string]: number } = {};
    const expenseMap: { [key: string]: number } = {};
    let labels: string[] = [];

    if (period === 'week') {
      // Get the last 8 weeks (including current)
      const today = new Date();
      const weeks: string[] = [];

      for (let i = 7; i >= 0; i--) {
        const d = new Date(today);
        d.setDate(today.getDate() - i * 7);
        const weekStart = new Date(d);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay()); // Sunday
        const label = `Week ${8 - i}`;
        weeks.push(label);

        incomeMap[label] = 0;
        expenseMap[label] = 0;
      }

      // Assign each record to a week label
      records.forEach((record) => {
        const recordDate = new Date(record.date.toDate());
        const diffWeeks = Math.floor(
          (today.getTime() - recordDate.getTime()) / (7 * 24 * 60 * 60 * 1000)
        );
        const weekIndex = 7 - diffWeeks;

        if (weekIndex >= 0 && weekIndex < 8) {
          const label = `Week ${weekIndex + 1}`;
          if (record.transactionType === 'income') {
            incomeMap[label] += record.amount;
          } else {
            expenseMap[label] += record.amount;
          }
        }
      });

      labels = weeks;
    }

    else if (period === 'year') {
      const currentYear = new Date().getFullYear();
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

      labels = [...monthNames];
      labels.forEach((m) => {
        incomeMap[m] = 0;
        expenseMap[m] = 0;
      });

      records.forEach((record) => {
        const recordDate = new Date(record.date.toDate());
        if (recordDate.getFullYear() === currentYear) {
          const month = monthNames[recordDate.getMonth()];
          if (record.transactionType === 'income') {
            incomeMap[month] += record.amount;
          } else {
            expenseMap[month] += record.amount;
          }
        }
      });
    }

    // Construct ordered arrays
    const income = labels.map((label) => incomeMap[label] || 0);
    const expense = labels.map((label) => expenseMap[label] || 0);

    return { labels, income, expense };
  };


  const loadEntitiesByType = (type: string, isFilter = false) => {
    if (!currentFarm?.id) return;

    let entities: { label: string; value: string }[] = [];

    switch (type) {
      case 'animal':
        entities = animals.map(animal => ({
          label: animal.name || animal.id,
          value: animal.id
        }));
        break;
      case 'crop':
        entities = crops.map(crop => ({
          label: crop.name || crop.id,
          value: crop.id
        }));
        break;
      case 'field':
        entities = fields.map(field => ({
          label: field.name || field.id,
          value: field.id
        }));
        break;
      case 'garden':
        entities = gardens.map(garden => ({
          label: garden.name || garden.id,
          value: garden.id
        }));
        break;
      case 'plant':
        entities = plants.map(plant => ({
          label: plant.name || plant.id,
          value: plant.id
        }));
        break;
      case 'equipment':
        entities = equipment.map(equip => ({
          label: equip.name || equip.id,
          value: equip.id
        }));
        break;
      default:
        entities = [];
    }

    if (isFilter) {
      setFilterEntityIdOptions([{ label: t('all'), value: 'all' }, ...entities]);
    } else {
      setEntityIdOptions(entities);
    }
  };

  const loadFinanceRecords = async () => {
    try {
      await fetchFinanceRecords({
        entityType: filterEntityType || undefined,
        entityId: filterEntityId !== 'all' ? filterEntityId || undefined : undefined,
        transactionType: filterTransactionType !== 'all' ? filterTransactionType as 'income' | 'expense' : undefined,
        category: filterCategory || undefined,
        currency: filterCurrency || undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined
      });
    } catch (error) {
      console.error('Error loading finance records:', error);
    }
  };

  const handleAddRecord = async () => {
    if (!currentFarm?.id || !user?.id) {
      Alert.alert(t('error'), t('errors.farm_not_selected'));
      return;
    }

    if (!entityType || !entityId || !category || !amount || isNaN(parseFloat(amount))) {
      Alert.alert(t('error'), t('errors.all_fields_required'));
      return;
    }

    setIsSubmitting(true);

    try {
      await addFinanceRecord({
        entityType,
        entityId,
        transactionType,
        category,
        amount: parseFloat(amount),
        currency,
        date: Timestamp.fromDate(date),
        notes: notes.trim()
      });

      // Reset form
      setEntityType(null);
      setEntityId(null);
      setCategory(null);
      setAmount('');
      setNotes('');
      setDate(new Date());

      Alert.alert(
        t('success'),
        t('finance.record_added_successfully'),
        [{ text: t('ok'), onPress: () => setActiveView('view') }]
      );
    } catch (error: any) {
      console.error('Error adding finance record:', error);
      Alert.alert(t('error'), error.message || t('errors.failed_to_add_record'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleExportCSV = async () => {
    if (financeRecords.length === 0) {
      Alert.alert(t('error'), t('errors.no_records_to_export'));
      return;
    }

    try {
      const csvContent = await exportFinanceRecordsToCSV();

      // Write to file
      const fileName = `finance_export_${Date.now()}.csv`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8
      });

      // Share file
      await Sharing.shareAsync(fileUri);
    } catch (error: any) {
      console.error('Error exporting to CSV:', error);
      Alert.alert(t('error'), error.message || t('errors.export_failed'));
    }
  };

  const handleExportPDF = async () => {
    // This would require additional libraries like react-native-html-to-pdf
    Alert.alert(
      t('info'),
      t('finance.pdf_export_not_implemented'),
      [{ text: t('ok') }]
    );
  };

  const renderFinanceRecord = ({ item }: { item: any }) => {
    const date = item.date.toDate();
    const formattedDate = new Date(date).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }); //format(date, 'MMM dd, yyyy');
// console.log({item})
    return (
      <View style={[styles.recordCard, isRTL && styles.rtlCard]}>
        <View style={[styles.recordHeader, isRTL && styles.rtlRow]}>
          <Text style={[styles.entityType, isRTL && styles.rtlText]}>
            {t(`entity_types.${item.entityType}`)} - {getAnimal(item.entityId)?.name || '--'}
            {/* //{item.name || item.entityId} */}
          </Text>
          <View style={[
            styles.transactionTypeBadge,
            { backgroundColor: item.transactionType === 'income' ? colors.success : colors.danger }
          ]}>
            <Text style={styles.transactionTypeText}>
              {t(item.transactionType)}
            </Text>
          </View>
        </View>

        <View style={[styles.recordBody, isRTL && styles.rtlRow]}>
          <View style={isRTL ? { alignItems: 'flex-end' } : undefined}>
            <Text style={[
              styles.amount,
              { color: item.transactionType === 'income' ? colors.success : colors.danger },
              isRTL && styles.rtlText
            ]}>
              {item.currency === 'PKR' ? '₨' : '$'} {item.amount.toFixed(2)}
            </Text>
            <Text style={[styles.category, isRTL && styles.rtlText]}>
              {t(`categories.${item.category}`)}
            </Text>
          </View>
          <Text style={[styles.date, isRTL && styles.rtlText]}>
            {formattedDate}
          </Text>
        </View>

        {item.notes ? (
          <Text style={[styles.notes, isRTL && styles.rtlText]}>
            {item.notes}
          </Text>
        ) : null}
      </View>
    );
  };


  const handleFilter = () => {
    // You can implement filter logic here if needed, or leave it empty if filters are applied automatically.
  };
  // Helper function to format category name
  const formatCategoryName = (category: string): string => {
    return category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  const renderTransactionItem = ({ item }: { item: any }) => {
    return (
      <View style={styles.transactionItem}>
        <View style={styles.transactionHeader}>
          <View style={styles.categoryContainer}>
            {item.isExpense ? (
              <ArrowDownRight size={16} color={colors.danger} />
            ) : (
              <ArrowUpRight size={16} color={colors.success} />
            )}
            <Text style={[
              styles.categoryText,
              { color: item.isExpense ? colors.danger : colors.success }
            ]}>
              {formatCategoryName(item.category)}
            </Text>
          </View>

          <Text style={[
            styles.amount,
            { color: item.isExpense ? colors.danger : colors.success }
          ]}>
            {item.isExpense ? '-' : '+'}
            {item.currency} {item.amount.toFixed(2)}
          </Text>
        </View>

        <Text style={styles.description}>{item.description}</Text>

        <View style={styles.transactionFooter}>
          <Text style={styles.date}>
            {new Date(item.date).toLocaleDateString()}
          </Text>

          {item.tags && item.tags?.length > 0 && (
            <View style={styles.tagsContainer}>
              {item.tags.map((tag: string, index: number) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.tabs}>
        {user?.role === 'owner' && (
          <TouchableOpacity
            style={[styles.tab, activeView === 'dashboard' && styles.activeTab]}
            onPress={() => setActiveView('dashboard')}
          >
            <BarChart2 size={16} color={activeView !== 'dashboard' ? colors.secondary : colors.primary} />
            <Text style={[styles.tabText, activeView === 'dashboard' && styles.activeTabText]}>
              {t('finance.dashboard')}
            </Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[styles.tab, activeView === 'add' && styles.activeTab]}
          onPress={() => setActiveView('add')}
        >
          <Plus size={16} color={activeView !== 'add' ? colors.secondary : colors.primary} />
          <Text style={[styles.tabText, activeView === 'add' && styles.activeTabText]}>
            {t('finance.add')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeView === 'view' && styles.activeTab]}
          onPress={() => setActiveView('view')}
        >
          <FileText size={16} color={activeView !== 'view' ? colors.secondary : colors.primary} />
          <Text style={[styles.tabText, activeView === 'view' && styles.activeTabText]}>
            {t('finance.records')}
          </Text>
        </TouchableOpacity>
      </View>
      {user?.role === 'owner' && activeView === 'dashboard' && (
        <ScrollView style={styles.dashboardContainer}>
          <View style={styles.summaryContainer}>
            <View style={styles.summaryCard}>
              <Text style={styles.summaryLabel}>{t('finance.total_income')}</Text>
              <Text style={[styles.summaryValue, { color: colors.success }]}>
                {financeSummary?.currency === 'PKR' ? '₨' : '$'} {financeSummary?.totalIncome?.toFixed(2) ?? '0.00'}
              </Text>
            </View>
            <View style={styles.summaryCard}>
              <Text style={styles.summaryLabel}>{t('finance.total_expense')}</Text>
              <Text style={[styles.summaryValue, { color: colors.danger }]}>
                {financeSummary?.currency === 'PKR' ? '₨' : '$'} {financeSummary?.totalExpense?.toFixed(2) ?? '0.00'}
              </Text>
            </View>
            <View style={styles.summaryCard}>
              <Text style={styles.summaryLabel}>{t('finance.net_profit')}</Text>
              <Text style={[styles.summaryValue, { color: (financeSummary?.netProfit ?? 0) >= 0 ? colors.primary : colors.danger }]}>
                {financeSummary?.currency === 'PKR' ? '₨' : '$'} {financeSummary?.netProfit?.toFixed(2) ?? '0.00'}
              </Text>
            </View>
          </View>

          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>{t('finance.financial_overview')}</Text>
            <View style={styles.periodSelector}>
              <TouchableOpacity style={[styles.periodButton, chartPeriod === 'weekly' && styles.activePeriodButton]} onPress={() => setChartPeriod('weekly')}>
                <Text style={[styles.periodButtonText, chartPeriod === 'weekly' && styles.activePeriodButtonText]}>{t('finance.weekly')}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.periodButton, chartPeriod === 'monthly' && styles.activePeriodButton]} onPress={() => setChartPeriod('monthly')}>
                <Text style={[styles.periodButtonText, chartPeriod === 'monthly' && styles.activePeriodButtonText]}>{t('finance.monthly')}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.periodButton, chartPeriod === 'yearly' && styles.activePeriodButton]} onPress={() => setChartPeriod('yearly')}>
                <Text style={[styles.periodButtonText, chartPeriod === 'yearly' && styles.activePeriodButtonText]}>{t('finance.yearly')}</Text>
              </TouchableOpacity>
            </View>
            {isLoading ? (
              <ActivityIndicator size="large" color={colors.primary} />
            ) : chartData?.labels?.length > 0 ? (
              <CustomBarChart
                labels={chartData?.labels}
                income={chartData?.income}
                expense={chartData?.expense}
                barColors={chartData?.barColors} />
            ) : (
              <View style={styles.emptyChart}>
                <Text style={styles.emptyText}>{t('finance.no_data_for_chart')}</Text>
              </View>
            )}
          </View>

          <View style={styles.recentTransactionsContainer}>
            <Text style={styles.recentTransactionsTitle}>{t('finance.recent_transactions')}</Text>
            {financeRecords.slice(0, 3).map(item => renderFinanceRecord({ item }))}
            {financeRecords?.length === 0 && <Text style={styles.emptyText}>{t('finance.noTransactionsAdded')}</Text>}
          </View>
        </ScrollView>
      )}

      {activeView === 'add' && (
        <ScrollView
          style={styles.formContainer}
          contentContainerStyle={styles.formContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Entity Type */}
          <View style={styles.formGroup}>
            {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.entity_type')}
            </Text> */}
            <DropdownPicker
              label={t('finance.entity_type')}
              options={entityOptions || []}
              onSelect={(val) => setEntityType(val as any)}
              selectedValue={entityType}
              isMultiple={false}
              disabled={entity ? true : false}
            />
          </View>

          {/* Entity ID */}
          <View style={styles.formGroup}>
            {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.entity_Name')}
            </Text> */}
            <DropdownPicker
              label={t('finance.entity_Name')}
              options={entityIdOptions || []}
              onSelect={(val) => setEntityId(val as any)}
              selectedValue={entityId}
              isMultiple={false}
              disabled={id ? true : false}
            />
          </View>

          {/* Transaction Type */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.transaction_type')}
            </Text>
            <View style={[styles.toggleContainer, isRTL && styles.rtlRow]}>
              <TouchableOpacity
                style={[
                  styles.transactionToggle,
                  transactionType === 'expense' && styles.activeTransactionToggle,
                  { borderTopLeftRadius: 8, borderBottomLeftRadius: 8 },
                  isRTL && { borderTopLeftRadius: 0, borderBottomLeftRadius: 0, borderTopRightRadius: 8, borderBottomRightRadius: 8 }
                ]}
                onPress={() => setTransactionType('expense')}
              >
                <ArrowDownCircle size={16} color={transactionType === 'expense' ? colors.white : colors.danger} />
                <Text style={[
                  styles.transactionToggleText,
                  transactionType === 'expense' && styles.activeTransactionToggleText,
                  isRTL && { marginRight: 8, marginLeft: 0 }
                ]}>
                  {t('finance.expense')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.transactionToggle,
                  transactionType === 'income' && styles.activeIncomeToggle,
                  { borderTopRightRadius: 8, borderBottomRightRadius: 8 },
                  isRTL && { borderTopRightRadius: 0, borderBottomRightRadius: 0, borderTopLeftRadius: 8, borderBottomLeftRadius: 8 }
                ]}
                onPress={() => setTransactionType('income')}
              >
                <ArrowUpCircle size={16} color={transactionType === 'income' ? colors.white : colors.danger} />
                <Text style={[
                  styles.transactionToggleText,
                  transactionType === 'income' && styles.activeIncomeToggleText,
                  isRTL && { marginRight: 8, marginLeft: 0 }
                ]}>
                  {t('finance.income')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Category */}
          <View style={styles.formGroup}>
            {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.category')}
            </Text> */}
            <DropdownPicker
              label={t('finance.category')}
              options={categoryOptions?.filter(cat => (cat.parentId === entityType || cat.parentId === null) && cat.type === transactionType) || []}
              onSelect={(val) => setCategory(val as any)}
              selectedValue={category}
              isMultiple={false}
            />

          </View>

          {/* Amount */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.amount')}
            </Text>
            <View style={styles.inputWithIcon}>

              <Input
                // style={[styles.input, isRTL && styles.rtlInput]}
                value={amount}
                onChangeText={setAmount}
                placeholder="0.00"
                keyboardType="numeric"
                leftIcon={<DollarSign size={20} color={colors.gray[500]} />}
              />
            </View>
          </View>

          {/* Currency */}
          <View style={styles.formGroup}>
            {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.currency')}
            </Text> */}
            <DropdownPicker
              label={t('finance.currency')}
              options={currencyOptions || []}
              onSelect={(val) => setCurrency(val as any)}
              selectedValue={currency}
              isMultiple={false}
            />

          </View>

          {/* Date */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.date')}
            </Text>
            <View style={styles.inputWithIcon}>

              <Input
                // style={[styles.input, isRTL && styles.rtlInput]}
                value={new Date(date).toLocaleDateString()}
                onChangeText={setDate}
                placeholder="DD/MM/YYYY"
                keyboardType="numeric"
                leftIcon={<Calendar size={20} color={colors.gray[500]} />}
              />
            </View>
          </View>

          {/* Notes */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isRTL && styles.rtlText]}>
              {t('finance.notes')}
            </Text>
            <TextInput
              style={[styles.input, styles.textArea, isRTL && styles.rtlInput]}
              value={notes}
              onChangeText={setNotes}
              placeholder={t('finance.placeholders.enter_notes')}
              multiline
              numberOfLines={4}
            />
          </View>

          <Button
            title={t('common.save')}
            onPress={handleAddRecord}
            loading={isSubmitting}
            style={styles.button}
          />
        </ScrollView>
      )}

      {/* View Records View */}
      {activeView === 'view' && (
        <>
          <View style={styles.transactionListContainer}>
            {isLoading ? (
              <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
            ) : financeRecords.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>{t('finance.noTransactionsAdded')}</Text>
              </View>
            ) : (
              <FlatList
                data={financeRecords}
                keyExtractor={(item) => item.id}
                renderItem={renderFinanceRecord}
                contentContainerStyle={styles.transactionList}
              />
            )}
          </View>
          {/* FLOATING BUTTON */}
          <TouchableOpacity
            style={styles.fab}
            onPress={() => setFilterModalVisible(true)}
          >
            <Filter color="#fff" size={24} />
          </TouchableOpacity>
        </>
      )}

      {/* FILTER MODAL */}
      <Modal visible={filterModalVisible} animationType="slide">
        <View style={styles.modalContainer}>
          <ScrollView
            style={styles.formContainer}
            contentContainerStyle={styles.formContent}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.filterContainer}>
              {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
                {t('finance.filter_by_entity_type')}
              </Text> */}
              <DropdownPicker
                label={t('finance.filter_by_entity_type')}
                options={entityOptions || []}
                onSelect={(val) => setFilterEntityType(val as any)}
                selectedValue={filterEntityType}
                isMultiple={false}
              />

            </View>

            <View style={styles.filterContainer}>
              {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
                {t('finance.filter_by_entity')}
              </Text> */}
              <DropdownPicker
                label={t('finance.filter_by_entity')}
                options={filterEntityIdOptions || []}
                onSelect={(val) => setFilterEntityId(val as any)}
                selectedValue={filterEntityId}
                isMultiple={false}
              />

            </View>

            {/*  <View style={styles.filterContainer}>
              <Text style={[styles.label, isRTL && styles.rtlText]}>
                {t('filter_by_transaction_type')}
              </Text>
              <DropdownPicker
              label={t('entity_type')}
              options={transactionTypeOptions || []}
              onSelect={(val) => setFilterTransactionType(val as any)}
              selectedValue={filterTransactionType}
              isMultiple={false}
            />

            </View> */}

            <View style={styles.filterContainer}>
              {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
                {t('finance.filter_by_category')}
              </Text> */}
              <DropdownPicker
                label={t('finance.filter_by_category')}
                options={categoryOptions || []}
                onSelect={(val) => setFilterCategory(val as any)}
                selectedValue={filterCategory}
                isMultiple={false}
              />

            </View>

            <View style={styles.filterContainer}>
              {/* <Text style={[styles.label, isRTL && styles.rtlText]}>
                {t('finance.filter_by_currency')}
              </Text> */}
              <DropdownPicker
                label={t('finance.filter_by_currency')}
                options={currencyOptions || []}
                onSelect={(val) => setFilterCurrency(val as any)}
                selectedValue={filterCurrency}
                isMultiple={false}
              />

            </View>

            <View style={styles.filterContainer}>
              <Text style={[styles.label, isRTL && styles.rtlText]}>
                {t('finance.filter_by_date')}
              </Text>
              <View style={styles.inputWithIcon}>

                <Input
                  // style={[styles.input, isRTL && styles.rtlInput]}
                  value={new Date(filterDate).toLocaleDateString()}
                  onChangeText={setFilterDate}
                  placeholder="DD/MM/YYYY"
                  keyboardType="numeric"
                  leftIcon={<Calendar size={20} color={colors.gray[500]} />}
                />
              </View>
            </View>

            <Button
              title={t('finance.common.filter')}
              onPress={handleFilter}
              style={styles.button}
            />

            <Button title="Close" onPress={() => setFilterModalVisible(false)} variant="ghost" />
          </ScrollView>
        </View>
      </Modal>

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    marginLeft: 8,
    color: colors.gray[600],
  },
  loader: {
    marginTop: 32,
    alignSelf: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
    textAlign: 'center',
    marginTop: 16,
  },
  date: {
    fontSize: 13,
    color: colors.gray[600],
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  activeTabText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  tag: {
    backgroundColor: colors.gray[200],
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: colors.gray[700],
  },
  description: {
    fontSize: 13,
    color: colors.gray[700],
    marginTop: 4,
    marginBottom: 4,
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    right: 24,
    bottom: 32,
    backgroundColor: colors.primary,
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    zIndex: 100,
  },
  transactionListContainer: {
    flex: 1,
  },
  transactionList: {
    padding: 16,
  },
  transactionItem: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white,
    padding: 16,
    justifyContent: 'center',
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  recordBody: {
    flex: 1,
  },
  transactionToggle: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    backgroundColor: colors.secondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTransactionToggle: {
    backgroundColor: colors.primary,
  },
  transactionToggleText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  activeTransactionToggleText: {
    color: colors.white,
  },
  activeIncomeToggle: {
    backgroundColor: colors.success,
  },
  activeIncomeToggleText: {
    color: colors.white,
  },
  activeExpenseToggle: {
    backgroundColor: colors.danger,
  },
  activeExpenseToggleText: {
    color: colors.white,
  },
  recordContainer: {
    padding: 16,
  },
  recordHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },

  recordCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  // recordHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 12,
  //   borderBottomWidth: 1,
  //   borderBottomColor: colors.gray[200],
  //   paddingBottom: 8,
  // },
  recordHeaderLeft: {
    flex: 1,
    marginRight: 8,
  },
  recordHeaderRight: {
    alignItems: 'flex-end',
  },
  recordCategory: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  recordEntity: {
    fontSize: 13,
    color: colors.gray[500],
    marginTop: 2,
  },
  recordAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  entityType: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[700],
  },
  transactionTypeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  transactionTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  recordNotes: {
    marginTop: 8,
    fontSize: 14,
    color: colors.gray[600],
  },
  recordDetails: {
    marginTop: 12,
  },
  recordDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  recordDetailLabel: {
    fontSize: 14,
    color: colors.gray[700],
  },
  recordDetailValue: {
    fontSize: 14,
    color: colors.gray[700],
  },
  recordOptions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 12,
  },
  recordOption: {
    marginLeft: 8,
  },
  recordOptionIcon: {
    marginRight: 4,
  },
  recordOptionText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  rtlRecordOption: {
    marginLeft: 8,
    marginRight: 0,
  },
  rtlRecordOptionIcon: {
    transform: [{ rotate: '180deg' }],
  },
  rtlRecordOptionText: {
    textAlign: 'right',
  },
  rtlRecordOptions: {
    flexDirection: 'row-reverse',
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  activeToggle: {
    backgroundColor: colors.primary,
  },
  toggleText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  activeToggleText: {
    color: colors.white,
  },
  rtlRow: {
    flexDirection: 'row-reverse',
  },
  rtlText: {
    textAlign: 'right',
  },
  rtlInput: {
    textAlign: 'right',
  },
  rtlButton: {
    transform: [{ rotate: '180deg' }],
  },
  rtlIcon: {
    transform: [{ rotate: '180deg' }],
  },
  rtlCard: {
    flexDirection: 'row-reverse',
  },
  rtlRecordAmount: {
    textAlign: 'right',
  },
  rtlRecordNotes: {
    textAlign: 'right',
  },
  rtlRecordDate: {
    textAlign: 'right',
  },
  rtlRecordTransactionType: {
    textAlign: 'right',
  },
  rtlRecordCategory: {
    textAlign: 'right',
  },
  rtlRecordCurrency: {
    textAlign: 'right',
  },
  rtlRecordEntityType: {
    textAlign: 'right',
  },
  rtlRecordEntity: {
    textAlign: 'right',
  },
  rtlRecordCreatedBy: {
    textAlign: 'right',
  },
  rtlRecordCreatedAt: {
    textAlign: 'right',
  },
  rtlRecordOptions: {
    flexDirection: 'row-reverse',
  },
  rtlRecordOption: {
    marginLeft: 8,
    marginRight: 0,
  },
  rtlRecordOptionIcon: {
    transform: [{ rotate: '180deg' }],
  },
  rtlRecordOptionText: {
    textAlign: 'right',
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  viewToggle: {
    flexDirection: 'row',
  },
  viewToggleItem: {
    padding: 8,
  },
  activeViewToggleItem: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  viewToggleText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  activeViewToggleText: {
    color: colors.primary,
    fontWeight: '600',
  },
  formContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  formContent: {
    padding: 16,
  },
  filterContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    marginTop: 16,
  },
  dropdown: {
    borderColor: colors.gray[300],
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.white,
  },
  dropdownText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  dropdownItemContainer: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  formGroup: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
  },
  // rtlInput: {
  //   textAlign: 'right',
  // },
  // rtlText: {
  //   textAlign: 'right',
  // },
  // rtlCard: {
  //   flexDirection: 'row-reverse',
  // },
  // rtlRecordAmount: {
  //   textAlign: 'right',
  // },
  // rtlRecordNotes: {
  //   textAlign: 'right',
  // },
  // rtlRecordDate: {
  //   textAlign: 'right',
  // },
  // rtlRecordTransactionType: {
  //   textAlign: 'right',
  // },
  // rtlRecordCategory: {
  //   textAlign: 'right',
  // },
  // rtlRecordCurrency: {
  //   textAlign: 'right',
  // },
  // rtlRecordEntityType: {
  //   textAlign: 'right',
  // },
  // rtlRecordEntity: {
  //   textAlign: 'right',
  // },
  // rtlRecordCreatedBy: {
  //   textAlign: 'right',
  // },
  // rtlRecordCreatedAt: {
  //   textAlign: 'right',
  // },
  // rtlRecordOptions: {
  //   flexDirection: 'row-reverse',
  // },
  // rtlRecordOption: {
  //   marginLeft: 8,
  //   marginRight: 0,
  // },
  // rtlRecordOptionIcon: {
  //   transform: [{ rotate: '180deg' }],
  // },
  // rtlRecordOptionText: {
  //   textAlign: 'right',
  // },
  // rtlRecordOptionIcon: {
  //   transform: [{ rotate: '180deg' }],
  // },
  // rtlRecordOptionText: {
  //   textAlign: 'right',
  // },
  // rtlRecordOptionIcon: {
  //   transform: [{ rotate: '180deg' }],
  // },
  // rtlRecordOptionText: {
  //   textAlign: 'right',
  // },
  //   rtlRecordOptionIcon: {
  //   transform: [{ rotate: '180deg' }],
  // },
  // rtlRecordOptionText: {
  //   textAlign: 'right',
  // },
  dashboardContainer: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  summaryLabel: {
    fontSize: 13,
    color: colors.gray[600],
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  chartContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 6,
  },
  activePeriodButton: {
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  periodButtonText: {
    textAlign: 'center',
    fontSize: 14,
    color: colors.gray[600],
  },
  activePeriodButtonText: {
    color: colors.primary,
    fontWeight: '600',
  },
  emptyChart: {
    height: 250,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recentTransactionsContainer: {},
  recentTransactionsTitle: { fontSize: 18, fontWeight: '600', color: colors.gray[800], marginBottom: 12 },
});
