import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Image,
  FlatList,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { 
  Calendar, 
  Clock, 
  FileText, 
  Download, 
  Share2, 
  Printer, 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Leaf, 
  Wheat,
  Droplets,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock3,
  DollarSign,
  ArrowRight,
  ArrowUpRight,
  ArrowDownRight,
} from 'lucide-react-native';

export default function WeeklySummaryReport() {
  const { currentFarm } = useFarmStore();
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  
  // Mock date range
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 7);
  const endDate = new Date();
  
  // Mock task completion data
  const taskCompletionData = {
    total: 42,
    completed: 35,
    inProgress: 5,
    pending: 2,
    completionRate: 83,
    previousWeekCompletionRate: 78,
    dailyCompletion: [
      { day: 'Mon', completed: 6, total: 8 },
      { day: 'Tue', completed: 7, total: 7 },
      { day: 'Wed', completed: 5, total: 7 },
      { day: 'Thu', completed: 4, total: 6 },
      { day: 'Fri', completed: 6, total: 7 },
      { day: 'Sat', completed: 4, total: 4 },
      { day: 'Sun', completed: 3, total: 3 },
    ]
  };
  
  // Mock crop health data
  const cropHealthData = [
    { id: '1', name: 'Wheat', health: 'excellent', trend: 'stable', area: '12 hectares', notes: 'Growing well, on track for harvest in 4 weeks' },
    { id: '2', name: 'Corn', health: 'good', trend: 'improving', area: '8 hectares', notes: 'Recovering from minor pest issue, applied organic treatment' },
    { id: '3', name: 'Tomatoes', health: 'fair', trend: 'declining', area: '2 hectares', notes: 'Showing signs of blight, increased monitoring required' },
    { id: '4', name: 'Lettuce', health: 'excellent', trend: 'stable', area: '1.5 hectares', notes: 'Ready for harvest next week' },
    { id: '5', name: 'Soybeans', health: 'good', trend: 'stable', area: '10 hectares', notes: 'Normal growth pattern, no issues detected' },
  ];
  
  // Mock resource usage data
  const resourceUsageData = {
    water: {
      current: 45000,
      previous: 48000,
      unit: 'liters',
      trend: 'down',
      percentage: 6.25
    },
    fertilizer: {
      current: 850,
      previous: 800,
      unit: 'kg',
      trend: 'up',
      percentage: 6.25
    },
    fuel: {
      current: 320,
      previous: 350,
      unit: 'liters',
      trend: 'down',
      percentage: 8.57
    },
    electricity: {
      current: 1200,
      previous: 1150,
      unit: 'kWh',
      trend: 'up',
      percentage: 4.35
    }
  };
  
  // Mock financial summary
  const financialSummary = {
    revenue: 12500,
    expenses: 7800,
    profit: 4700,
    previousWeekProfit: 4200,
    profitTrend: 'up',
    profitPercentage: 11.9,
    topExpenses: [
      { category: 'Labor', amount: 3200 },
      { category: 'Supplies', amount: 1800 },
      { category: 'Fuel', amount: 950 },
      { category: 'Maintenance', amount: 850 },
      { category: 'Utilities', amount: 600 },
    ]
  };
  
  // Mock issues and alerts
  const issuesAndAlerts = [
    { id: '1', severity: 'high', title: 'Irrigation System Malfunction', description: 'North field irrigation pump needs repair', status: 'in_progress', assignedTo: 'Mike Johnson' },
    { id: '2', severity: 'medium', title: 'Pest Detection', description: 'Aphids detected in tomato greenhouse', status: 'resolved', assignedTo: 'Sarah Williams' },
    { id: '3', severity: 'low', title: 'Equipment Maintenance Due', description: 'Tractor scheduled maintenance in 3 days', status: 'pending', assignedTo: 'Robert Smith' },
  ];
  
  // Mock upcoming tasks
  const upcomingTasks = [
    { id: '1', title: 'Harvest Wheat Field 3', dueDate: 'Mon, Jun 12', priority: 'high', assignedTo: 'Field Team' },
    { id: '2', title: 'Apply Organic Pesticide', dueDate: 'Tue, Jun 13', priority: 'medium', assignedTo: 'Sarah Williams' },
    { id: '3', title: 'Irrigation System Repair', dueDate: 'Wed, Jun 14', priority: 'high', assignedTo: 'Mike Johnson' },
    { id: '4', title: 'Tractor Maintenance', dueDate: 'Fri, Jun 16', priority: 'medium', assignedTo: 'Robert Smith' },
  ];
  
  useEffect(() => {
    // Simulate loading report data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);
  
  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent':
        return colors.success;
      case 'good':
        return colors.primary;
      case 'fair':
        return colors.warning;
      case 'poor':
        return colors.danger;
      default:
        return colors.gray[500];
    }
  };
  
  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'improving':
      case 'up':
        return colors.success;
      case 'stable':
        return colors.primary;
      case 'declining':
      case 'down':
        return colors.danger;
      default:
        return colors.gray[500];
    }
  };
  
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
      case 'up':
        return <ArrowUpRight size={14} color={getTrendColor(trend)} />;
      case 'declining':
      case 'down':
        return <ArrowDownRight size={14} color={getTrendColor(trend)} />;
      default:
        return <ArrowRight size={14} color={getTrendColor(trend)} />;
    }
  };
  
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return colors.danger;
      case 'medium':
        return colors.warning;
      case 'low':
        return colors.info;
      default:
        return colors.gray[500];
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved':
        return colors.success;
      case 'in_progress':
        return colors.warning;
      case 'pending':
        return colors.info;
      default:
        return colors.gray[500];
    }
  };
  
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return colors.danger;
      case 'medium':
        return colors.warning;
      case 'low':
        return colors.info;
      default:
        return colors.gray[500];
    }
  };
  
  const renderCropHealthItem = ({ item }: { item: any }) => (
    <View style={styles.cropHealthItem}>
      <View style={styles.cropHealthHeader}>
        <Text style={styles.cropName}>{item.name}</Text>
        <View style={[
          styles.healthBadge, 
          { backgroundColor: getHealthColor(item.health) + '20' }
        ]}>
          <Text style={[
            styles.healthBadgeText,
            { color: getHealthColor(item.health) }
          ]}>
            {item.health}
          </Text>
        </View>
      </View>
      
      <View style={styles.cropHealthDetails}>
        <View style={styles.cropHealthDetail}>
          <Text style={styles.cropHealthDetailLabel}>Area:</Text>
          <Text style={styles.cropHealthDetailValue}>{item.area}</Text>
        </View>
        
        <View style={styles.cropHealthDetail}>
          <Text style={styles.cropHealthDetailLabel}>Trend:</Text>
          <View style={styles.trendContainer}>
            {getTrendIcon(item.trend)}
            <Text style={[
              styles.trendText,
              { color: getTrendColor(item.trend) }
            ]}>
              {item.trend}
            </Text>
          </View>
        </View>
      </View>
      
      {item.notes && (
        <Text style={styles.cropHealthNotes}>{item.notes}</Text>
      )}
    </View>
  );
  
  const renderResourceItem = ({ item }: { item: any }) => {
    const [key, data] = item;
    
    return (
      <View style={styles.resourceItem}>
        <View style={styles.resourceHeader}>
          <Text style={styles.resourceName}>{key}</Text>
          <View style={styles.resourceTrend}>
            {getTrendIcon(data.trend)}
            <Text style={[
              styles.resourceTrendText,
              { color: getTrendColor(data.trend) }
            ]}>
              {data.percentage}%
            </Text>
          </View>
        </View>
        
        <View style={styles.resourceValues}>
          <Text style={styles.resourceCurrentValue}>
            {data.current.toLocaleString()} {data.unit}
          </Text>
          <Text style={styles.resourcePreviousValue}>
            Previous: {data.previous.toLocaleString()} {data.unit}
          </Text>
        </View>
      </View>
    );
  };
  
  const renderIssueItem = ({ item }: { item: any }) => (
    <View style={styles.issueItem}>
      <View style={styles.issueHeader}>
        <View style={[
          styles.issueSeverity,
          { backgroundColor: getSeverityColor(item.severity) }
        ]} />
        <Text style={styles.issueTitle}>{item.title}</Text>
      </View>
      
      <Text style={styles.issueDescription}>{item.description}</Text>
      
      <View style={styles.issueFooter}>
        <View style={[
          styles.issueStatusBadge,
          { backgroundColor: getStatusColor(item.status) + '20' }
        ]}>
          <Text style={[
            styles.issueStatusText,
            { color: getStatusColor(item.status) }
          ]}>
            {item.status.replace('_', ' ')}
          </Text>
        </View>
        
        <Text style={styles.issueAssignee}>{item.assignedTo}</Text>
      </View>
    </View>
  );
  
  const renderUpcomingTaskItem = ({ item }: { item: any }) => (
    <View style={styles.upcomingTaskItem}>
      <View style={styles.upcomingTaskHeader}>
        <Text style={styles.upcomingTaskTitle}>{item.title}</Text>
        <View style={[
          styles.priorityBadge,
          { backgroundColor: getPriorityColor(item.priority) + '20' }
        ]}>
          <Text style={[
            styles.priorityText,
            { color: getPriorityColor(item.priority) }
          ]}>
            {item.priority}
          </Text>
        </View>
      </View>
      
      <View style={styles.upcomingTaskDetails}>
        <Text style={styles.upcomingTaskDate}>{item.dueDate}</Text>
        <Text style={styles.upcomingTaskAssignee}>{item.assignedTo}</Text>
      </View>
    </View>
  );
  
  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Stack.Screen 
          options={{
            title: 'Weekly Summary Report',
            headerShown: true,
          }}
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Generating report...</Text>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Weekly Summary Report',
          headerShown: true,
        }}
      />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.reportHeader}>
          <View style={styles.farmInfo}>
            <Text style={styles.farmName}>{currentFarm?.name || 'Farm Name'}</Text>
            <View style={styles.reportMetaRow}>
              <Calendar size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <Clock size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                Generated at {new Date().toLocaleTimeString()}
              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <FileText size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>Weekly Summary Report</Text>
            </View>
          </View>
          
          <View style={styles.reportActions}>
            <TouchableOpacity style={styles.reportAction}>
              <Download size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Share2 size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Printer size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.taskCompletionSection}>
          <Text style={styles.sectionTitle}>Task Completion</Text>
          
          <View style={styles.taskCompletionOverview}>
            <View style={styles.taskCompletionChart}>
              <View style={styles.taskCompletionRateContainer}>
                <Text style={styles.taskCompletionRateValue}>{taskCompletionData.completionRate}%</Text>
                <Text style={styles.taskCompletionRateLabel}>Completion Rate</Text>
                
                <View style={styles.taskCompletionTrend}>
                  <TrendingUp size={14} color={colors.success} />
                  <Text style={styles.taskCompletionTrendText}>
                    +{taskCompletionData.completionRate - taskCompletionData.previousWeekCompletionRate}% from last week
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.taskCompletionStats}>
              <View style={styles.taskCompletionStat}>
                <View style={[styles.taskCompletionStatIcon, { backgroundColor: colors.success + '20' }]}>
                  <CheckCircle size={20} color={colors.success} />
                </View>
                <View style={styles.taskCompletionStatContent}>
                  <Text style={styles.taskCompletionStatValue}>{taskCompletionData.completed}</Text>
                  <Text style={styles.taskCompletionStatLabel}>Completed</Text>
                </View>
              </View>
              
              <View style={styles.taskCompletionStat}>
                <View style={[styles.taskCompletionStatIcon, { backgroundColor: colors.warning + '20' }]}>
                  <Clock3 size={20} color={colors.warning} />
                </View>
                <View style={styles.taskCompletionStatContent}>
                  <Text style={styles.taskCompletionStatValue}>{taskCompletionData.inProgress}</Text>
                  <Text style={styles.taskCompletionStatLabel}>In Progress</Text>
                </View>
              </View>
              
              <View style={styles.taskCompletionStat}>
                <View style={[styles.taskCompletionStatIcon, { backgroundColor: colors.danger + '20' }]}>
                  <XCircle size={20} color={colors.danger} />
                </View>
                <View style={styles.taskCompletionStatContent}>
                  <Text style={styles.taskCompletionStatValue}>{taskCompletionData.pending}</Text>
                  <Text style={styles.taskCompletionStatLabel}>Pending</Text>
                </View>
              </View>
            </View>
          </View>
          
          <View style={styles.dailyCompletionContainer}>
            <Text style={styles.dailyCompletionTitle}>Daily Completion</Text>
            <View style={styles.dailyCompletionChart}>
              {taskCompletionData.dailyCompletion.map((day, index) => (
                <View key={day.day} style={styles.dailyCompletionBar}>
                  <View style={styles.dailyCompletionBarContainer}>
                    <View 
                      style={[
                        styles.dailyCompletionBarFill, 
                        { 
                          height: `${(day.completed / day.total) * 100}%`,
                          backgroundColor: day.completed === day.total ? colors.success : colors.primary
                        }
                      ]} 
                    />
                  </View>
                  <Text style={styles.dailyCompletionDay}>{day.day}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
        
        <View style={styles.cropHealthSection}>
          <Text style={styles.sectionTitle}>Crop Health Status</Text>
          
          <View style={styles.cropHealthList}>
            {cropHealthData.map(crop => (
              <View key={crop.id} style={styles.cropHealthItemContainer}>
                {renderCropHealthItem({ item: crop })}
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.resourceUsageSection}>
          <Text style={styles.sectionTitle}>Resource Usage</Text>
          
          <View style={styles.resourceList}>
            {Object.entries(resourceUsageData).map((item, index) => (
              <View key={item[0]} style={styles.resourceItemContainer}>
                {renderResourceItem({ item })}
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.financialSection}>
          <Text style={styles.sectionTitle}>Financial Summary</Text>
          
          <View style={styles.financialOverview}>
            <View style={styles.financialSummary}>
              <View style={styles.financialSummaryItem}>
                <Text style={styles.financialSummaryLabel}>Revenue</Text>
                <Text style={styles.financialSummaryValue}>${financialSummary.revenue.toLocaleString()}</Text>
              </View>
              
              <View style={styles.financialSummaryItem}>
                <Text style={styles.financialSummaryLabel}>Expenses</Text>
                <Text style={styles.financialSummaryValue}>${financialSummary.expenses.toLocaleString()}</Text>
              </View>
              
              <View style={styles.financialSummaryItem}>
                <Text style={styles.financialSummaryLabel}>Profit</Text>
                <Text style={[styles.financialSummaryValue, { color: colors.success }]}>
                  ${financialSummary.profit.toLocaleString()}
                </Text>
                <View style={styles.financialTrend}>
                  {getTrendIcon(financialSummary.profitTrend)}
                  <Text style={[
                    styles.financialTrendText,
                    { color: getTrendColor(financialSummary.profitTrend) }
                  ]}>
                    {financialSummary.profitPercentage}%
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.topExpensesContainer}>
              <Text style={styles.topExpensesTitle}>Top Expenses</Text>
              
              {financialSummary.topExpenses.map((expense, index) => (
                <View key={expense.category} style={styles.expenseItem}>
                  <Text style={styles.expenseCategory}>{expense.category}</Text>
                  <Text style={styles.expenseAmount}>${expense.amount.toLocaleString()}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
        
        <View style={styles.issuesSection}>
          <Text style={styles.sectionTitle}>Issues & Alerts</Text>
          
          <View style={styles.issuesList}>
            {issuesAndAlerts.map(issue => (
              <View key={issue.id} style={styles.issueItemContainer}>
                {renderIssueItem({ item: issue })}
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.upcomingTasksSection}>
          <Text style={styles.sectionTitle}>Upcoming Tasks</Text>
          
          <View style={styles.upcomingTasksList}>
            {upcomingTasks.map(task => (
              <View key={task.id} style={styles.upcomingTaskItemContainer}>
                {renderUpcomingTaskItem({ item: task })}
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.signatureSection}>
          <Text style={styles.signatureLabel}>Report Generated By:</Text>
          <Text style={styles.signatureName}>{user?.displayName || 'Farm Manager'}</Text>
          <Text style={styles.signatureDate}>{new Date().toLocaleDateString()}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[700],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmInfo: {
    flex: 1,
  },
  farmName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
  },
  reportMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  reportMetaText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 8,
  },
  reportActions: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  reportAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 12,
  },
  taskCompletionSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  taskCompletionOverview: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  taskCompletionChart: {
    flex: 1,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 16,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  taskCompletionRateContainer: {
    alignItems: 'center',
  },
  taskCompletionRateValue: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.primary,
  },
  taskCompletionRateLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
  },
  taskCompletionTrend: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    backgroundColor: colors.success + '10',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  taskCompletionTrendText: {
    fontSize: 10,
    color: colors.success,
    marginLeft: 4,
  },
  taskCompletionStats: {
    flex: 1,
  },
  taskCompletionStat: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  taskCompletionStatIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  taskCompletionStatContent: {
    flex: 1,
  },
  taskCompletionStatValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  taskCompletionStatLabel: {
    fontSize: 12,
    color: colors.gray[600],
  },
  dailyCompletionContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  dailyCompletionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  dailyCompletionChart: {
    flexDirection: 'row',
    height: 120,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  dailyCompletionBar: {
    flex: 1,
    alignItems: 'center',
  },
  dailyCompletionBarContainer: {
    width: 12,
    height: 100,
    backgroundColor: colors.gray[200],
    borderRadius: 6,
    overflow: 'hidden',
    justifyContent: 'flex-end',
  },
  dailyCompletionBarFill: {
    width: '100%',
    borderRadius: 6,
  },
  dailyCompletionDay: {
    fontSize: 10,
    color: colors.gray[600],
    marginTop: 4,
  },
  cropHealthSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cropHealthList: {
    marginTop: 8,
  },
  cropHealthItemContainer: {
    marginBottom: 12,
  },
  cropHealthItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  cropHealthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cropName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  healthBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  healthBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  cropHealthDetails: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  cropHealthDetail: {
    flex: 1,
  },
  cropHealthDetailLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 2,
  },
  cropHealthDetailValue: {
    fontSize: 14,
    color: colors.gray[800],
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 14,
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  cropHealthNotes: {
    fontSize: 12,
    color: colors.gray[600],
    backgroundColor: colors.white,
    borderRadius: 4,
    padding: 8,
  },
  resourceUsageSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  resourceList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  resourceItemContainer: {
    width: '50%',
    padding: 4,
  },
  resourceItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    // height: '100%',
  },
  resourceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resourceName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    textTransform: 'capitalize',
  },
  resourceTrend: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resourceTrendText: {
    fontSize: 12,
    marginLeft: 4,
  },
  resourceValues: {
    marginTop: 4,
  },
  resourceCurrentValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 2,
  },
  resourcePreviousValue: {
    fontSize: 12,
    color: colors.gray[600],
  },
  financialSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  financialOverview: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  financialSummary: {
    flexDirection: 'row',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    paddingBottom: 16,
  },
  financialSummaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  financialSummaryLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  financialSummaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  financialTrend: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  financialTrendText: {
    fontSize: 12,
    marginLeft: 4,
  },
  topExpensesContainer: {
    backgroundColor: colors.white,
    borderRadius: 6,
    padding: 10,
  },
  topExpensesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  expenseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 6,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  expenseCategory: {
    fontSize: 14,
    color: colors.gray[700],
  },
  expenseAmount: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  issuesSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  issuesList: {
    marginTop: 8,
  },
  issueItemContainer: {
    marginBottom: 12,
  },
  issueItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  issueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  issueSeverity: {
    width: 4,
    height: 16,
    borderRadius: 2,
    marginRight: 8,
  },
  issueTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
  },
  issueDescription: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 8,
  },
  issueFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  issueStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  issueStatusText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  issueAssignee: {
    fontSize: 12,
    color: colors.gray[600],
  },
  upcomingTasksSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  upcomingTasksList: {
    marginTop: 8,
  },
  upcomingTaskItemContainer: {
    marginBottom: 12,
  },
  upcomingTaskItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  upcomingTaskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  upcomingTaskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
  },
  upcomingTaskDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  upcomingTaskDate: {
    fontSize: 14,
    color: colors.gray[700],
  },
  upcomingTaskAssignee: {
    fontSize: 14,
    color: colors.gray[600],
  },
  signatureSection: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  signatureLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  signatureName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  signatureDate: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
  },
});