// import React, { useState } from 'react';
// import { View, FlatList, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
// import { Video } from 'expo-av';
// import * as FileSystem from 'expo-file-system';
// import { MaterialIcons } from '@expo/vector-icons';

// const helpVideos = [
//   {
//     id: '1',
//     title: 'How to Record a Harvest',
//     url: 'https://www.w3schools.com/html/mov_bbb.mp4',
//   },
//   {
//     id: '2',
//     title: 'Daily Animal Checklist Guide',
//     url: 'https://www.w3schools.com/html/movie.mp4',
//   },
// ];

// const HelpVideosScreen = () => {
//   const [downloaded, setDownloaded] = useState<string[]>([]);

//   const handleDownload = async (videoUrl: string, title: string) => {
//     try {
//       const filename = videoUrl.split('/').pop();
//       const path = FileSystem.documentDirectory + filename;

//       const fileInfo = await FileSystem.getInfoAsync(path);
//       if (fileInfo.exists) {
//         Alert.alert('Already Downloaded', `${title} is already downloaded.`);
//         return;
//       }

//       const download = FileSystem.createDownloadResumable(videoUrl, path);
//       const { uri } = await download.downloadAsync();
//       setDownloaded([...downloaded, uri]);
//       Alert.alert('Downloaded', `${title} saved locally.`);
//     } catch (error) {
//       console.error('Download failed', error);
//       Alert.alert('Download failed', 'Please try again.');
//     }
//   };

//   const renderItem = ({ item }) => (
//     <View style={styles.card}>
//       <Text style={styles.title}>{item.title}</Text>
//       <Video
//         source={{ uri: item.url }}
//         style={styles.video}
//         useNativeControls
//         resizeMode="contain"
//         isLooping
//       />
//       <TouchableOpacity style={styles.downloadButton} onPress={() => handleDownload(item.url, item.title)}>
//         <MaterialIcons name="file-download" size={24} color="#fff" />
//         <Text style={styles.downloadText}>Download</Text>
//       </TouchableOpacity>
//     </View>
//   );

//   return (
//     <FlatList
//       data={helpVideos}
//       keyExtractor={(item) => item.id}
//       renderItem={renderItem}
//       contentContainerStyle={styles.container}
//     />
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     padding: 12,
//   },
//   card: {
//     marginBottom: 24,
//     padding: 12,
//     backgroundColor: '#fff',
//     borderRadius: 12,
//     elevation: 2,
//   },
//   title: {
//     fontWeight: 'bold',
//     fontSize: 16,
//     marginBottom: 8,
//   },
//   video: {
//     width: '100%',
//     height: 200,
//     borderRadius: 8,
//     backgroundColor: '#000',
//   },
//   downloadButton: {
//     marginTop: 12,
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: '#007aff',
//     padding: 10,
//     borderRadius: 8,
//     justifyContent: 'center',
//   },
//   downloadText: {
//     color: '#fff',
//     marginLeft: 8,
//   },
// });

// export default HelpVideosScreen;

// import React, { useState } from 'react';
// import { View, Text, FlatList, TouchableOpacity, StyleSheet, Image } from 'react-native';
// import { Video } from 'expo-av';
// import { Ionicons } from '@expo/vector-icons';

// const helpVideos = [
//   {
//     id: '1',
//     title: 'How to Record a Harvest',
//     thumbnail: 'https://i3.ytimg.com/vi/aqz-KE-bpKQ/hqdefault.jpg', // Replace with actual
//     uri: 'https://www.w3schools.com/html/mov_bbb.mp4',
//   },
//   {
//     id: '2',
//     title: 'Daily Animal Checklist Guide',
//     thumbnail: 'https://i3.ytimg.com/vi/2Vv-BfVoq4g/hqdefault.jpg',
//     uri: 'https://www.w3schools.com/html/movie.mp4',
//   },
// ];

// export default function HelpVideosScreen() {
//   const [selectedVideo, setSelectedVideo] = useState(helpVideos[0]);

//   return (
//     <View style={styles.container}>
//       <Video
//         source={{ uri: selectedVideo.uri }}
//         rate={1.0}
//         volume={1.0}
//         isMuted={false}
//         resizeMode="contain"
//         useNativeControls
//         style={styles.video}
//       />
//       <FlatList
//         data={helpVideos}
//         keyExtractor={(item) => item.id}
//         renderItem={({ item }) => (
//           <TouchableOpacity
//             style={styles.card}
//             onPress={() => setSelectedVideo(item)}
//           >
//             <Text style={styles.title}>{item.title}</Text>
//             <Image source={{ uri: item.thumbnail }} style={styles.thumbnail} />
//             <TouchableOpacity style={styles.downloadButton}>
//               <Ionicons name="download-outline" size={20} color="white" />
//               <Text style={styles.downloadText}>Download</Text>
//             </TouchableOpacity>
//           </TouchableOpacity>
//         )}
//         contentContainerStyle={{ paddingBottom: 20 }}
//       />
//     </View>
//   );
// }

// const styles = StyleSheet.create({
//   container: { flex: 1, backgroundColor: '#fff' },
//   video: {
//     width: '100%',
//     height: 200,
//     backgroundColor: '#000',
//   },
//   card: {
//     margin: 10,
//     borderRadius: 10,
//     backgroundColor: '#f9f9f9',
//     padding: 10,
//     shadowColor: '#ccc',
//     shadowOpacity: 0.3,
//     shadowRadius: 5,
//   },
//   title: { fontSize: 16, fontWeight: 'bold', marginBottom: 5 },
//   thumbnail: {
//     width: '100%',
//     height: 150,
//     borderRadius: 8,
//     marginBottom: 10,
//   },
//   downloadButton: {
//     flexDirection: 'row',
//     backgroundColor: '#007bff',
//     borderRadius: 6,
//     padding: 8,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   downloadText: {
//     color: 'white',
//     marginLeft: 5,
//   },
// });

// export default 
import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, Image, ActivityIndicator } from 'react-native';
import { Video } from 'expo-av';
import { Feather } from '@expo/vector-icons';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { firestore } from '@/firebase/config';

interface HelpVideo {
  id: string;
  title: string;
  thumbnail: string;
  url: string;
}

export default function HelpVideosScreen() {
  const [videos, setVideos] = useState<HelpVideo[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<HelpVideo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVideos = async () => {
      setLoading(true);
      setError(null);
      try {
        const videosCollectionRef = collection(firestore, 'videos');
        const q = query(videosCollectionRef, orderBy('title'));
        const querySnapshot = await getDocs(q);
        const fetchedVideos: HelpVideo[] = querySnapshot.docs.map(doc => ({
          id: doc.id,
          title: doc.data().title,
          thumbnail: doc.data().thumbnailUrl,
          url: doc.data().videoUrl,
        }));

        // console.log({fetchedVideos})
        setVideos(fetchedVideos);
        if (fetchedVideos.length > 0) {
          setSelectedVideo(fetchedVideos[0]);
        }
      } catch (err) {
        console.error("Error fetching help videos:", err);
        setError("Failed to load videos. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);

  if (loading) {
    return (
      <View style={styles.centeredContainer}>
        <ActivityIndicator size="large" color="#007bff" />
        <Text style={styles.infoText}>Loading Videos...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centeredContainer}>
        <Text style={styles.infoText}>{error}</Text>
      </View>
    );
  }

  if (videos.length === 0) {
    return (
      <View style={styles.centeredContainer}>
        <Text style={styles.infoText}>No help videos available.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {selectedVideo && (
        <Video
          source={{ uri: selectedVideo.url }}
          useNativeControls
          resizeMode="contain"
          style={styles.video}
        />
      )}
      <FlatList
        data={videos}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => {
          const isSelected = selectedVideo?.id === item.id;
          return (
            <TouchableOpacity
              style={[styles.listItem, isSelected && styles.selectedItem]}
              onPress={() => setSelectedVideo(item)}
            >
              <Image source={{ uri: item.thumbnail }} style={styles.thumbnail} />
              <View style={styles.videoInfo}>
                <Text style={[styles.title, isSelected && styles.selectedText]}>
                  {item.title}
                </Text>
                <TouchableOpacity style={styles.downloadBtn}>
                  <Feather name="download" size={16} color="#007bff" />
                  <Text style={styles.downloadText}>Download</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          );
        }}

        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  video: { width: '100%', height: 220, backgroundColor: '#000' },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  listContainer: { paddingVertical: 10 },
  listItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
    borderColor: '#ccc',
    alignItems: 'center',
  },
  thumbnail: {
    width: 100,
    height: 60,
    borderRadius: 6,
    marginRight: 12,
  },
  selectedItem: {
    backgroundColor: '#e6f0ff', // light blue background
    borderLeftWidth: 4,
    borderLeftColor: '#007bff',
    borderRadius: 4,
  },

  selectedText: {
    color: '#007bff',
  },
  videoInfo: { flex: 1, justifyContent: 'center' },
  title: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 6,
    color: '#333',
  },
  downloadBtn: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  downloadText: {
    color: '#007bff',
    marginLeft: 5,
    fontSize: 14,
  },
});
