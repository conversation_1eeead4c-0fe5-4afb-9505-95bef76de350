import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  SafeAreaView,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useTranslation } from '@/i18n/useTranslation';
import { Crop } from '@/types';
import {
  Calendar,
  Edit,
  Trash2,
  ChevronRight,
  Leaf,
  MapPin,
  Clock,
  DollarSign,
  FileText,
  PieChart,
  Plus
} from 'lucide-react-native';
import Button from '@/components/Button';
export default function CropDetailsScreen() {
  const { id } = useLocalSearchParams();
  const cropId = Array.isArray(id) ? id[0] : id as string;

  const { t, isRTL } = useTranslation();
  const { crops, fields, getCrop, updateCrop, markCropAsHarvested, isLoading } = useFarmStore();

  const [crop, setCrop] = useState<Crop | null>(null);
  const [field, setField] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'details' | 'tasks' | 'gallery' | 'financials'>('details');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    loadCropDetails();
    // console.log({ cropId })
  }, [cropId]);

  const loadCropDetails = async () => {
    try {
      const cropData = getCrop(cropId);
      setCrop(cropData ?? null);

      // Find the field this crop belongs to
      if (cropData) {
        const fieldData = fields.find(f => f.id === cropData.fieldId);
        setField(fieldData || null);
      }
    } catch (error) {
      console.error('Error loading crop details:', error);
      Alert.alert('Error', 'Failed to load crop details');
    }
  };

  const handleMarkHarvested = () => {
    Alert.alert(
      'Mark as Harvested',
      'Are you sure you want to mark this crop as harvested?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Yes, Harvest',
          onPress: () => router.push(`/field/harvest/${cropId}`)
        }
      ]
    );
  };

  // Add these new functions for financial management
  const handleAddTransaction = () => {
    router.push({
      pathname: '/field/crop/transaction/add',
      params: { cropId, fieldId: crop?.fieldId }
    });
  };

  const handleViewFinancials = () => {
    router.push(`/field/crop/financials?id=${cropId}`);
  };

  if (!crop) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Loading...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: crop.cropType,
          headerRight: () => (
            <TouchableOpacity onPress={() => router.push(`/field/crop/edit/${cropId}`)}>
              <Edit size={24} color={colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />

      <View style={[styles.tabsContainer, { flexDirection: isRTL ? "row-reverse" : 'row' }]}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'details' && styles.activeTab]}
          onPress={() => setActiveTab('details')}
        >
          <Text style={[styles.tabText, activeTab === 'details' && styles.activeTabText]}>
            {t('crop.details')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'tasks' && styles.activeTab]}
          onPress={() => setActiveTab('tasks')}
        >
          <Text style={[styles.tabText, activeTab === 'tasks' && styles.activeTabText]}>
            {t('crop.tasks')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'gallery' && styles.activeTab]}
          onPress={() => setActiveTab('gallery')}
        >
          <Text style={[styles.tabText, activeTab === 'gallery' && styles.activeTabText]}>
            {t('crop.gallery')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'financials' && styles.activeTab]}
          onPress={() => setActiveTab('financials')}
        >
          <Text style={[styles.tabText, activeTab === 'financials' && styles.activeTabText]}>
            {t('crop.financials')}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {activeTab === 'details' && (
          <View style={styles.detailsContainer}>
            <View style={styles.infoCard}>
              <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Leaf size={20} color={colors.primary} />
                <Text style={styles.infoLabel}>{t('crop.type')}:</Text>
                <Text style={[styles.infoValue, { textAlign: isRTL ? 'right' : 'left' }]}>{crop.cropType}</Text>
              </View>

              <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <MapPin size={20} color={colors.primary} />
                <Text style={styles.infoLabel}>{t('crop.field')}:</Text>
                <Text style={[styles.infoValue, { textAlign: isRTL ? 'right' : 'left' }]}>{field?.name || 'Unknown'}</Text>
              </View>

              <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.infoLabel}>{t('crop.plantedDate')}:</Text>
                <Text style={[styles.infoValue, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {new Date(crop.plantedDate).toLocaleDateString()}
                </Text>
              </View>

              {crop.expectedHarvestDate && (
                <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.infoLabel}>{t('crop.expectedHarvest')}:</Text>
                  <Text style={[styles.infoValue, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {new Date(crop.expectedHarvestDate).toLocaleDateString()}
                  </Text>
                </View>
              )}

              {crop.soilType && (
                <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <Text style={styles.infoLabel}>{t('entity.garden.soilType')}:</Text>
                  <Text style={[styles.infoValue, { textAlign: isRTL ? 'right' : 'left' }]}>{crop.soilType}</Text>
                </View>
              )}

              {crop.notes && (
                <View style={[styles.notesContainer, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                  <Text style={styles.notesLabel}>{t('crop.notes')}:</Text>
                  <Text style={[styles.notesText, { textAlign: isRTL ? 'right' : 'left' }]}>{crop.notes}</Text>
                </View>
              )}
            </View>

            <View style={styles.actionsContainer}>
              <Button
                title={t('crop.markHarvested')}
                onPress={handleMarkHarvested}
                style={styles.harvestButton}
              />
            </View>
          </View>
        )}

        {activeTab === 'tasks' && (
          <View style={styles.tasksContainer}>
            <Text style={styles.sectionTitle}>{t('crop.tasks')}</Text>
            <Button
              title={t('crop.addTask')}
              onPress={() => router.push({
                pathname: '/task/create',
                params: { entityType: 'crop', entityId: cropId }
              })}
              style={styles.addButton}
            />
            <Text style={styles.emptyText}>{t('crop.noTasks')}</Text>
          </View>
        )}

        {activeTab === 'gallery' && (
          <View style={styles.galleryContainer}>
            <Text style={styles.sectionTitle}>{t('crop.gallery')}</Text>
            {crop.images && crop.images.length > 0 ? (
              <View style={styles.imagesGrid}>
                {crop.images.map((image, index) => (
                  <Image
                    key={index}
                    source={{ uri: image }}
                    style={styles.galleryImage}
                  />
                ))}
              </View>
            ) : (
              <Text style={styles.emptyText}>{t('crop.noImages')}</Text>
            )}
            <Button
              title={t('crop.addImage')}
              onPress={() => {/* Add image logic */ }}
              style={styles.addButton}
            />
          </View>
        )}

        {activeTab === 'financials' && (
          <View style={styles.financialsContainer}>
            <Text style={[styles.sectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>{t('crop.financials')}</Text>

            <View style={[styles.financialCards]}>
              <TouchableOpacity
                style={[styles.financialCard,{ flexDirection: isRTL ? 'row-reverse' : "row" }]}
                onPress={handleViewFinancials}
              >
                <PieChart size={24} color={colors.primary}  />
                <Text style={[styles.financialCardTitle, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{t('crop.viewFinancials')}</Text>
                {/* <View style> */}

                  <Text style={[styles.financialCardDescription, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 32 : 0 }]}>
                    {t('crop.viewFinancialsDescription')}
                  </Text>
                  {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
                {/* </View> */}

              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.financialCard, { flexDirection: isRTL ? 'row-reverse' : "row" }]}
                onPress={handleAddTransaction}
              >
                <DollarSign size={24} color={colors.primary} />
                <Text style={[styles.financialCardTitle, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{t('crop.addTransaction')}</Text>
                <Text style={[styles.financialCardDescription,, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 32 : 0 }]}>
                  {t('crop.addTransactionDescription')}
                </Text>
                {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
              </TouchableOpacity>
            </View>

            <Button
              title={t('crop.viewAllTransactions')}
              onPress={() => router.push(`/field/crop/transactions?id=${cropId}`)}
              style={styles.viewAllButton}
              variant="outline"
              leftIcon={<FileText size={20} color={colors.primary} />}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    backgroundColor: colors.white,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    color: colors.gray[600],
    fontSize: 14,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  detailsContainer: {
    flex: 1,
  },
  infoCard: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[700],
    marginLeft: 8,
    marginRight: 4,
  },
  infoValue: {
    fontSize: 16,
    color: colors.gray[900],
    flex: 1,
  },
  notesContainer: {
    marginTop: 8,
  },
  notesLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 4,
  },
  notesText: {
    fontSize: 16,
    color: colors.gray[800],
    lineHeight: 22,
  },
  actionsContainer: {
    marginTop: 8,
  },
  harvestButton: {
    backgroundColor: colors.success,
  },
  tasksContainer: {
    flex: 1,
  },
  galleryContainer: {
    flex: 1,
  },
  financialsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
    textAlign: 'center',
    marginTop: 24,
  },
  addButton: {
    marginTop: 16,
  },
  imagesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  galleryImage: {
    width: '48%',
    aspectRatio: 1,
    margin: '1%',
    borderRadius: 8,
  },
  financialCards: {
    marginBottom: 16,
  },
  financialCard: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  financialCardTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[800],
    marginLeft: 12,
    flex: 1,
  },
  financialCardDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 4,
    marginLeft: 36,
    width: '85%',
  },
  viewAllButton: {
    marginTop: 8,
  },
});

