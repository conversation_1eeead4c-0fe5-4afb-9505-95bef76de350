import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useLanguage } from '@/i18n/translations/LanguageProvider';
import { CropTransaction, CropFinancials, TransactionCategory } from '@/types';
import { 
  DollarSign, 
  TrendingUp, 
  AlertTriangle, 
  Plus, 
  ArrowUpRight, 
  ArrowDownRight,
  PieChart,
  Tag,
  Calendar,
  ChevronRight
} from 'lucide-react-native';
import { <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON> as R<PERSON>ie<PERSON>hart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import { useTranslation } from '@/i18n/useTranslation';

const screenWidth = Dimensions.get('window').width;

// Helper function to get color for transaction category
const getCategoryColor = (category: TransactionCategory): string => {
  const colorMap: Record<TransactionCategory, string> = {
    seed_cost: '#4CAF50',
    irrigation: '#2196F3',
    pesticide: '#F44336',
    fertilizer: '#9C27B0',
    labor: '#FF9800',
    equipment: '#607D8B',
    harvest_sale: '#8BC34A',
    other: '#9E9E9E'
  };
  
  return colorMap[category] || colorMap.other;
};

// Helper function to get icon for transaction category
const getCategoryIcon = (category: TransactionCategory) => {
  switch (category) {
    case 'seed_cost':
      return <Text style={styles.categoryIcon}>🌱</Text>;
    case 'irrigation':
      return <Text style={styles.categoryIcon}>💧</Text>;
    case 'pesticide':
      return <Text style={styles.categoryIcon}>🐛</Text>;
    case 'fertilizer':
      return <Text style={styles.categoryIcon}>🧪</Text>;
    case 'labor':
      return <Text style={styles.categoryIcon}>👨‍🌾</Text>;
    case 'equipment':
      return <Text style={styles.categoryIcon}>🚜</Text>;
    case 'harvest_sale':
      return <Text style={styles.categoryIcon}>💰</Text>;
    default:
      return <Text style={styles.categoryIcon}>📋</Text>;
  }
};

// Helper function to format category name
const formatCategoryName = (category: string): string => {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export default function CropFinancialsScreen() {
  const { id } = useLocalSearchParams();
  const cropId = Array.isArray(id) ? id[0] : id as string;

  const { t, isRTL } = useTranslation();
  const { 
    getCrop, 
    getField, 
    fetchCropTransactions, 
    calculateCropFinancials,
    cropTransactions,
    cropFinancials,
    isLoading 
  } = useFarmStore();

  const [crop, setCrop] = useState<any>(null);
  const [field, setField] = useState<any>(null);
  const [transactions, setTransactions] = useState<CropTransaction[]>([]);
  const [financials, setFinancials] = useState<CropFinancials | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'analysis'>('overview');

  useEffect(() => {
    loadData();
  }, [cropId]);

  const loadData = async () => {
    if (!cropId) return;
    
    // Get crop and field data
    const cropData = getCrop(cropId);
    setCrop(cropData);
    
    if (cropData) {
      const fieldData = getField(cropData.fieldId);
      setField(fieldData);
      
      // Fetch transactions
      const fetchedTransactions = await fetchCropTransactions(cropId);
      setTransactions(fetchedTransactions);
      
      // Calculate financials
      const calculatedFinancials = await calculateCropFinancials(cropId);
      setFinancials(calculatedFinancials);
    }
  };

  // Prepare data for pie chart
  const getPieChartData = () => {
    if (!financials) return [];
    
    return Object.entries(financials.costBreakdown)
      .filter(([category, amount]) => category !== 'harvest_sale' && amount > 0)
      .map(([category, amount]) => ({
        name: formatCategoryName(category),
        value: amount,
        color: getCategoryColor(category as TransactionCategory),
        legendFontColor: colors.text,
        legendFontSize: 12
      }));
  };
  const TransactionItem = ({ transaction, onPress }: { 
  transaction: CropTransaction; 
  onPress: () => void;
}) => {
  return (
    <TouchableOpacity style={styles.transactionItem} onPress={onPress}>
      <View style={styles.transactionHeader}>
        <View style={styles.categoryContainer}>
          {getCategoryIcon(transaction.category)}
          <View 
            style={[
              styles.categoryBadge, 
              { backgroundColor: getCategoryColor(transaction.category) }
            ]}
          >
            <Text style={styles.categoryText}>
              {formatCategoryName(transaction.category)}
            </Text>
          </View>
        </View>
        
        <Text style={[
          styles.amount,
          transaction.category === 'harvest_sale' ? styles.positiveAmount : styles.negativeAmount
        ]}>
          {transaction.category === 'harvest_sale' ? '+' : '-'}
          {transaction.currency} {transaction.amount.toFixed(2)}
        </Text>
      </View>
      
      <Text style={styles.description} numberOfLines={2}>
        {transaction.description}
      </Text>
      
      <View style={styles.transactionFooter}>
        <View style={styles.dateContainer}>
          <Calendar size={14} color={colors.gray[500]} />
          <Text style={styles.date}>
            {new Date(transaction.date).toLocaleDateString()}
          </Text>
        </View>
        
        {transaction.isHighCost && (
          <View style={styles.highCostBadge}>
            <Tag size={12} color={colors.white} />
            <Text style={styles.highCostText}>High Cost</Text>
          </View>
        )}
        
        <ChevronRight size={16} color={colors.gray[400]} />
      </View>
    </TouchableOpacity>
  );
};

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: t('financials.title') }} />

      {crop && (
        <View style={styles.cropHeader}>
          <Text style={styles.cropType}>{crop.cropType}</Text>
          {field && <Text style={styles.fieldName}>{field.name}</Text>}
        </View>
      )}

      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>
            {t('financials.tabs.overview')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'transactions' && styles.activeTab]}
          onPress={() => setActiveTab('transactions')}
        >
          <Text style={[styles.tabText, activeTab === 'transactions' && styles.activeTabText]}>
            {t('financials.tabs.transactions')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'analysis' && styles.activeTab]}
          onPress={() => setActiveTab('analysis')}
        >
          <Text style={[styles.tabText, activeTab === 'analysis' && styles.activeTabText]}>
            {t('financials.tabs.analysis')}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {activeTab === 'overview' && financials && (
          <View style={styles.overviewContainer}>
            <View style={styles.metricsContainer}>
              <View style={styles.metricCard}>
                <Text style={styles.metricLabel}>{t('financials.totalCosts')}</Text>
                <Text style={styles.metricValue}>
                  ${financials.totalCosts.toFixed(2)}
                </Text>
              </View>
              
              <View style={styles.metricCard}>
                <Text style={styles.metricLabel}>{t('financials.totalRevenue')}</Text>
                <Text style={styles.metricValue}>
                  ${financials.totalRevenue.toFixed(2)}
                </Text>
              </View>
              
              <View style={styles.metricCard}>
                <Text style={styles.metricLabel}>{t('financials.profit')}</Text>
                <Text style={[
                  styles.metricValue,
                  { color: financials.totalRevenue - financials.totalCosts >= 0 ? colors.success : colors.danger }
                ]}>
                  ${(financials.totalRevenue - financials.totalCosts).toFixed(2)}
                </Text>
              </View>
            </View>
            
            <View style={styles.roiContainer}>
              <Text style={styles.sectionTitle}>{t('financials.roi')}</Text>
              <View style={styles.roiCard}>
                <Text style={styles.roiValue}>
                  {financials.roi.toFixed(2)}%
                </Text>
                {financials.roi >= 0 ? (
                  <ArrowUpRight size={24} color={colors.success} />
                ) : (
                  <ArrowDownRight size={24} color={colors.danger} />
                )}
              </View>
              <Text style={styles.roiDescription}>
                {financials.roi >= 0 
                  ? t('financials.positiveRoi', { value: financials.roi.toFixed(2) })
                  : t('financials.negativeRoi', { value: Math.abs(financials.roi).toFixed(2) })}
              </Text>
            </View>
            
            <View style={styles.chartContainer}>
              <Text style={styles.sectionTitle}>{t('financials.costBreakdown')}</Text>
              {getPieChartData().length > 0 ? (
                <RNPieChart
                  data={getPieChartData()}
                  width={screenWidth - 32}
                  height={220}
                  chartConfig={{
                    backgroundColor: colors.white,
                    backgroundGradientFrom: colors.white,
                    backgroundGradientTo: colors.white,
                    decimalPlaces: 2,
                    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                  }}
                  accessor="value"
                  backgroundColor="transparent"
                  paddingLeft="15"
                  absolute
                />
              ) : (
                <Text style={styles.noDataText}>{t('financials.noCostData')}</Text>
              )}
            </View>
            
            {financials.inconsistencies && financials.inconsistencies.length > 0 && (
              <View style={styles.inconsistenciesContainer}>
                <Text style={styles.sectionTitle}>{t('financials.inconsistencies')}</Text>
                {financials.inconsistencies.map((inconsistency, index) => (
                  <View key={index} style={[
                    styles.inconsistencyCard,
                    { 
                      borderLeftColor: 
                        inconsistency.severity === 'high' ? colors.danger :
                        inconsistency.severity === 'medium' ? colors.warning :
                        colors.info
                    }
                  ]}>
                    <AlertTriangle 
                      size={20} 
                      color={
                        inconsistency.severity === 'high' ? colors.danger :
                        inconsistency.severity === 'medium' ? colors.warning :
                        colors.info
                      } 
                    />
                    <View style={styles.inconsistencyContent}>
                      <Text style={styles.inconsistencyType}>{inconsistency.type}</Text>
                      <Text style={styles.inconsistencyDescription}>{inconsistency.description}</Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
        
        {activeTab === 'transactions' && (
          <View style={styles.transactionsContainer}>
            <Text style={styles.sectionTitle}>{t('financials.transactions')}</Text>
            {transactions.length > 0 ? (
              <FlatList
                data={transactions}
                keyExtractor={item => item.id}
                renderItem={({ item }) => (
                  <TransactionItem transaction={item} />
                )}
              />
            ) : (
              <Text style={styles.noDataText}>{t('financials.noTransactions')}</Text>
            )}
          </View>
        )}
        
        {activeTab === 'analysis' && (
          <View style={styles.analysisContainer}>
            <Text style={styles.sectionTitle}>{t('financials.analysis')}</Text>
            <Text style={styles.analysisText}>
              {t('financials.analysisText')}
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 13,
    color: colors.white,
    fontWeight: '500',
  },
  transactionItem: {
    backgroundColor: colors.white,
    borderRadius: 10,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  transactionFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cropHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.white,
  },
  cropType: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  fieldName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[600],
  },
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  overviewContainer: {
    flex: 1,
  },
  metricsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  metricCard: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexBasis: '48%',
  },
  metricLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  roiContainer: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  roiCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  roiValue: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
  },
  roiDescription: {
    fontSize: 14,
    color: colors.gray[600],
  },
  chartContainer: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  inconsistenciesContainer: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 16,
  },
  inconsistencyCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inconsistencyContent: {
    marginLeft: 8,
  },
  inconsistencyType: {
    fontSize: 14,
    color: colors.text,
    fontWeight: '600',
    marginBottom: 4,
  },
  inconsistencyDescription: {
    fontSize: 12,
    color: colors.gray[600],
  },
  transactionsContainer: {
    flex: 1,
  },
  analysisContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  analysisText: {
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 22,
  },
  noDataText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
  },
  description: {
    fontSize: 13,
    color: colors.gray[700],
    marginBottom: 8,
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  positiveAmount: {
    color: colors.success,
  },
  negativeAmount: {
    color: colors.danger,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  highCostBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.danger,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
  },
  highCostText: {
    color: colors.white,
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '500',
  },
});