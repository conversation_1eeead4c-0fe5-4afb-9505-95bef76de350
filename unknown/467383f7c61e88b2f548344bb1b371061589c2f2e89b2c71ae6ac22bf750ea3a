import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView
} from 'react-native';
import { router, Stack } from 'expo-router';
import { useAuthStore } from '@/store/auth-store';
import { colors } from '@/constants/colors';
import Input from '@/components/Input';
import Button from '@/components/Button';
import ImagePicker from '@/components/ImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { 
  User, 
  Mail, 
  Lock, 
  Phone, 
  ChevronRight, 
  ChevronLeft, 
  Check,
  Home,
  Briefcase,
  UserCog
} from 'lucide-react-native';

export default function RegisterScreen() {
  const { register, isLoading } = useAuthStore();
  
  // Form state
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [profileImage, setProfileImage] = useState<string | null>(null);
  
  // Step state
  const [currentStep, setCurrentStep] = useState(1);
  const [passwordStrength, setPasswordStrength] = useState<'weak' | 'medium' | 'strong'>('weak');
  
  // Validation state
  const [errors, setErrors] = useState<{
    fullName?: string;
    email?: string;
    phone?: string;
    password?: string;
    confirmPassword?: string;
  }>({});
  
  // Password strength checker
  const checkPasswordStrength = (password: string) => {
    let strength = 0;
    
    // Length check
    if (password.length >= 8) strength += 1;
    
    // Contains number
    if (/\d/.test(password)) strength += 1;
    
    // Contains special character
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;
    
    // Contains uppercase and lowercase
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 1;
    
    if (strength <= 1) {
      setPasswordStrength('weak');
    } else if (strength <= 3) {
      setPasswordStrength('medium');
    } else {
      setPasswordStrength('strong');
    }
  };
  
  // Validate step 1
  const validateStep1 = () => {
    const newErrors: any = {};
    
    if (!fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }
    
    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[0-9]{10,15}$/.test(phone)) {
      newErrors.phone = 'Phone number is invalid';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Validate step 2
  const validateStep2 = () => {
    const newErrors: any = {};
    
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }
    
    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle next step
  const handleNextStep = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    } else if (currentStep === 2 && validateStep2()) {
      setCurrentStep(3);
    }
  };
  
  // Handle previous step
  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  // Handle registration
  const handleRegister = async () => {
    try {
      let profileImageUrl = '';
      
      if (profileImage) {
        profileImageUrl = await uploadImageAsync(profileImage, 'profile_images');
      }
      
      const result = await register({
        email,
        password,
        name: fullName,
        photoURL: profileImageUrl,
        phone,
        role: 'owner', // Only owners can register directly
      });
      
      // Show verification message
      Alert.alert('Email Verification Required',
        'A verification email has been sent to your email address. Please verify your email before logging in.',
        [
          { text: 'OK', onPress: () => router.replace('/') }
        ]
      );
    } catch (error: any) {
      Alert.alert('Registration Failed', error.message || 'An error occurred during registration');
    }
  };
  
  // Render step indicators
  const renderStepIndicators = () => {
    return (
      <View style={styles.stepIndicatorContainer}>
        {[1, 2, 3].map((step) => (
          <View key={step} style={styles.stepIndicatorWrapper}>
            <View 
              style={[
                styles.stepIndicator, 
                currentStep === step ? styles.activeStepIndicator : null,
                currentStep > step ? styles.completedStepIndicator : null
              ]}
            >
              {currentStep > step ? (
                <Check size={16} color={colors.white} />
              ) : (
                <Text style={[
                  styles.stepIndicatorText,
                  currentStep === step ? styles.activeStepIndicatorText : null
                ]}>
                  {step}
                </Text>
              )}
            </View>
            {step < 3 && <View style={[
              styles.stepConnector,
              currentStep > step ? styles.completedStepConnector : null
            ]} />}
          </View>
        ))}
      </View>
    );
  };
  
  // Render step title
  const renderStepTitle = () => {
    switch (currentStep) {
      case 1:
        return 'Personal Information';
      case 2:
        return 'Create Password';
      case 3:
        return 'Complete Profile';
      default:
        return '';
    }
  };
  
  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <>
            <Input
              label="Full Name"
              placeholder="Enter your full name"
              value={fullName}
              onChangeText={setFullName}
              leftIcon={<User size={20} color={colors.gray[500]} />}
              error={errors.fullName}
              containerStyle={styles.inputContainer}
            />
            
            <Input
              label="Email"
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon={<Mail size={20} color={colors.gray[500]} />}
              error={errors.email}
              containerStyle={styles.inputContainer}
            />
            
            <Input
              label="Phone Number"
              placeholder="Enter your phone number"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              leftIcon={<Phone size={20} color={colors.gray[500]} />}
              error={errors.phone}
              containerStyle={styles.inputContainer}
            />
          </>
        );
      
      case 2:
        return (
          <>
            <Input
              label="Password"
              placeholder="Create a password"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                checkPasswordStrength(text);
              }}
              secureTextEntry
              leftIcon={<Lock size={20} color={colors.gray[500]} />}
              error={errors.password}
              containerStyle={styles.inputContainer}
            />
            
            <View style={styles.passwordStrengthContainer}>
              <Text style={styles.passwordStrengthLabel}>Password Strength:</Text>
              <View style={styles.passwordStrengthIndicator}>
                <View 
                  style={[
                    styles.passwordStrengthBar,
                    styles.passwordStrengthBarWeak,
                    passwordStrength === 'weak' ? styles.passwordStrengthBarActive : null,
                    passwordStrength === 'medium' || passwordStrength === 'strong' ? styles.passwordStrengthBarCompleted : null
                  ]} 
                />
                <View 
                  style={[
                    styles.passwordStrengthBar,
                    styles.passwordStrengthBarMedium,
                    passwordStrength === 'medium' ? styles.passwordStrengthBarActive : null,
                    passwordStrength === 'strong' ? styles.passwordStrengthBarCompleted : null
                  ]} 
                />
                <View 
                  style={[
                    styles.passwordStrengthBar,
                    styles.passwordStrengthBarStrong,
                    passwordStrength === 'strong' ? styles.passwordStrengthBarActive : null
                  ]} 
                />
              </View>
              <Text style={[
                styles.passwordStrengthText,
                passwordStrength === 'weak' ? styles.passwordStrengthTextWeak : null,
                passwordStrength === 'medium' ? styles.passwordStrengthTextMedium : null,
                passwordStrength === 'strong' ? styles.passwordStrengthTextStrong : null
              ]}>
                {passwordStrength.charAt(0).toUpperCase() + passwordStrength.slice(1)}
              </Text>
            </View>
            
            <Input
              label="Confirm Password"
              placeholder="Confirm your password"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
              leftIcon={<Lock size={20} color={colors.gray[500]} />}
              error={errors.confirmPassword}
              containerStyle={styles.inputContainer}
            />
            
            <View style={styles.passwordRequirements}>
              <Text style={styles.passwordRequirementsTitle}>Password Requirements:</Text>
              <View style={styles.passwordRequirementItem}>
                <View style={[
                  styles.passwordRequirementDot,
                  password.length >= 8 ? styles.passwordRequirementDotCompleted : null
                ]} />
                <Text style={styles.passwordRequirementText}>At least 8 characters</Text>
              </View>
              <View style={styles.passwordRequirementItem}>
                <View style={[
                  styles.passwordRequirementDot,
                  /\d/.test(password) ? styles.passwordRequirementDotCompleted : null
                ]} />
                <Text style={styles.passwordRequirementText}>Contains a number</Text>
              </View>
              <View style={styles.passwordRequirementItem}>
                <View style={[
                  styles.passwordRequirementDot,
                  /[!@#$%^&*(),.?":{}|<>]/.test(password) ? styles.passwordRequirementDotCompleted : null
                ]} />
                <Text style={styles.passwordRequirementText}>Contains a special character</Text>
              </View>
              <View style={styles.passwordRequirementItem}>
                <View style={[
                  styles.passwordRequirementDot,
                  /[a-z]/.test(password) && /[A-Z]/.test(password) ? styles.passwordRequirementDotCompleted : null
                ]} />
                <Text style={styles.passwordRequirementText}>Contains uppercase and lowercase letters</Text>
              </View>
            </View>
          </>
        );
      
      case 3:
        return (
          <>
            <View style={styles.profileCompletionContainer}>
              <Text style={styles.profileCompletionTitle}>Almost there!</Text>
              <Text style={styles.profileCompletionSubtitle}>Add a profile picture to complete your registration</Text>
              
              <ImagePicker
                image={profileImage}
                onImageSelected={setProfileImage}
                placeholder="Add Profile Picture"
                shape="circle"
                size={150}
                style={styles.profileImagePicker}
              />
              
              <View style={styles.profileSummary}>
                <Text style={styles.profileSummaryTitle}>Registration Summary</Text>
                
                <View style={styles.profileSummaryItem}>
                  <Text style={styles.profileSummaryLabel}>Name:</Text>
                  <Text style={styles.profileSummaryValue}>{fullName}</Text>
                </View>
                
                <View style={styles.profileSummaryItem}>
                  <Text style={styles.profileSummaryLabel}>Email:</Text>
                  <Text style={styles.profileSummaryValue}>{email}</Text>
                </View>
                
                <View style={styles.profileSummaryItem}>
                  <Text style={styles.profileSummaryLabel}>Phone:</Text>
                  <Text style={styles.profileSummaryValue}>{phone}</Text>
                </View>
                
                <View style={styles.profileSummaryItem}>
                  <Text style={styles.profileSummaryLabel}>Role:</Text>
                  <Text style={styles.profileSummaryValue}>Farm Owner</Text>
                </View>
                
                <View style={styles.roleDescription}>
                  <Text style={styles.roleDescriptionText}>
                    As a Farm Owner, you will be able to create and manage multiple farms, invite managers and caretakers, and have full access to all features.
                  </Text>
                </View>
              </View>
            </View>
          </>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen 
        options={{
          title: 'Create Account',
          headerShown: true,
        }}
      />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {renderStepIndicators()}
          
          <Text style={styles.stepTitle}>{renderStepTitle()}</Text>
          
          <View style={styles.formContainer}>
            {renderStepContent()}
          </View>
          
          <View style={styles.buttonContainer}>
            {currentStep > 1 && (
              <Button
                title="Back"
                onPress={handlePrevStep}
                variant="outline"
                style={styles.backButton}
                leftIcon={<ChevronLeft size={20} color={colors.primary} />}
              />
            )}
            
            {currentStep < 3 ? (
              <Button
                title="Next"
                onPress={handleNextStep}
                style={styles.nextButton}
                rightIcon={<ChevronRight size={20} color={colors.white} />}
              />
            ) : (
              <Button
                title={isLoading ? "Creating Account..." : "Create Account"}
                onPress={handleRegister}
                style={styles.registerButton}
                disabled={isLoading}
                leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
              />
            )}
          </View>
          
          <View style={styles.loginLinkContainer}>
            <Text style={styles.loginLinkText}>Already have an account?</Text>
            <TouchableOpacity onPress={() => router.replace('/')}>
              <Text style={styles.loginLink}>Login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  stepIndicatorWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepIndicator: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.gray[200],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.gray[200],
  },
  activeStepIndicator: {
    backgroundColor: colors.white,
    borderColor: colors.primary,
  },
  completedStepIndicator: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  stepIndicatorText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[600],
  },
  activeStepIndicatorText: {
    color: colors.primary,
  },
  stepConnector: {
    width: 30,
    height: 2,
    backgroundColor: colors.gray[200],
    marginHorizontal: 4,
  },
  completedStepConnector: {
    backgroundColor: colors.primary,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 24,
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  backButton: {
    flex: 1,
    marginRight: 8,
  },
  nextButton: {
    flex: 1,
    marginLeft: 8,
  },
  registerButton: {
    flex: 1,
  },
  loginLinkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginLinkText: {
    fontSize: 14,
    color: colors.gray[600],
    marginRight: 4,
  },
  loginLink: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
  passwordStrengthContainer: {
    marginBottom: 16,
  },
  passwordStrengthLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  passwordStrengthIndicator: {
    flexDirection: 'row',
    height: 6,
    borderRadius: 3,
    marginBottom: 8,
  },
  passwordStrengthBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.gray[200],
    marginRight: 4,
  },
  passwordStrengthBarWeak: {
    marginRight: 4,
  },
  passwordStrengthBarMedium: {
    marginRight: 4,
  },
  passwordStrengthBarStrong: {
    marginRight: 0,
  },
  passwordStrengthBarActive: {
    backgroundColor: colors.primary,
  },
  passwordStrengthBarCompleted: {
    backgroundColor: colors.primary,
  },
  passwordStrengthText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[600],
  },
  passwordStrengthTextWeak: {
    color: colors.danger,
  },
  passwordStrengthTextMedium: {
    color: colors.warning,
  },
  passwordStrengthTextStrong: {
    color: colors.success,
  },
  passwordRequirements: {
    marginBottom: 16,
    backgroundColor: colors.gray[50],
    padding: 16,
    borderRadius: 8,
  },
  passwordRequirementsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  passwordRequirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  passwordRequirementDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.gray[300],
    marginRight: 8,
  },
  passwordRequirementDotCompleted: {
    backgroundColor: colors.success,
  },
  passwordRequirementText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  profileCompletionContainer: {
    alignItems: 'center',
  },
  profileCompletionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 8,
  },
  profileCompletionSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 24,
    textAlign: 'center',
  },
  profileImagePicker: {
    marginBottom: 24,
  },
  profileSummary: {
    width: '100%',
    backgroundColor: colors.gray[50],
    padding: 16,
    borderRadius: 12,
  },
  profileSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  profileSummaryItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  profileSummaryLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    width: 80,
  },
  profileSummaryValue: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  roleDescription: {
    backgroundColor: colors.primary + '10',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  roleDescriptionText: {
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
  },
});

