import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Image,
  FlatList,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { 
  Calendar, 
  Clock, 
  FileText, 
  Download, 
  Share2, 
  Printer, 
  Users, 
  User, 
  Star, 
  CheckCircle, 
  Clock3,
  Award,
  TrendingUp,
  TrendingDown,
  BarChart3,
  AlertTriangle,
  ThumbsUp,
  Search,
  Filter,
} from 'lucide-react-native';

export default function StaffPerformanceReport() {
  const { currentFarm } = useFarmStore();
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  
  // Mock date range
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);
  const endDate = new Date();
  
  // Mock staff data
  const staffMembers = [
    { 
      id: '1', 
      name: 'John Doe', 
      role: 'Field Manager',
      department: 'operations',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8MTJ8fHBlb3BsZXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      performance: {
        rating: 4.8,
        tasksCompleted: 42,
        tasksAssigned: 45,
        completionRate: 93,
        attendanceRate: 98,
        trend: 'up',
        previousRating: 4.6,
      },
      strengths: ['Leadership', 'Problem Solving', 'Technical Knowledge'],
      areasForImprovement: ['Documentation'],
      feedback: [
        { id: '1', text: 'Excellent leadership during the harvest season', from: 'Farm Owner', date: '2023-05-15' },
        { id: '2', text: 'Needs to improve documentation of field activities', from: 'Operations Director', date: '2023-05-10' },
      ]
    },
    { 
      id: '2', 
      name: 'Sarah Williams', 
      role: 'Harvester',
      department: 'production',
      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NXx8cGVvcGxlfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      performance: {
        rating: 4.5,
        tasksCompleted: 38,
        tasksAssigned: 40,
        completionRate: 95,
        attendanceRate: 100,
        trend: 'stable',
        previousRating: 4.5,
      },
      strengths: ['Efficiency', 'Attention to Detail', 'Teamwork'],
      areasForImprovement: ['Initiative'],
      feedback: [
        { id: '3', text: 'Consistently meets harvest quotas with high quality', from: 'Field Manager', date: '2023-05-18' },
      ]
    },
    { 
      id: '3', 
      name: 'Mike Johnson', 
      role: 'Equipment Operator',
      department: 'operations',
      image: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Nnx8cGVvcGxlfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      performance: {
        rating: 4.2,
        tasksCompleted: 32,
        tasksAssigned: 35,
        completionRate: 91,
        attendanceRate: 95,
        trend: 'up',
        previousRating: 3.9,
      },
      strengths: ['Equipment Maintenance', 'Safety Awareness'],
      areasForImprovement: ['Time Management', 'Communication'],
      feedback: [
        { id: '4', text: 'Has shown significant improvement in equipment handling', from: 'Field Manager', date: '2023-05-12' },
        { id: '5', text: 'Sometimes late in reporting equipment issues', from: 'Maintenance Supervisor', date: '2023-05-05' },
      ]
    },
    { 
      id: '4', 
      name: 'Emily Davis', 
      role: 'Crop Specialist',
      department: 'agronomy',
      image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8MTV8fHBlb3BsZXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      performance: {
        rating: 4.9,
        tasksCompleted: 28,
        tasksAssigned: 28,
        completionRate: 100,
        attendanceRate: 97,
        trend: 'up',
        previousRating: 4.7,
      },
      strengths: ['Crop Knowledge', 'Problem Diagnosis', 'Research Skills'],
      areasForImprovement: ['Training Others'],
      feedback: [
        { id: '6', text: 'Exceptional knowledge of crop diseases and treatments', from: 'Farm Owner', date: '2023-05-20' },
      ]
    },
    { 
      id: '5', 
      name: 'Robert Smith', 
      role: 'Maintenance Technician',
      department: 'maintenance',
      image: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Nnx8cGVyc29ufGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      performance: {
        rating: 3.8,
        tasksCompleted: 25,
        tasksAssigned: 30,
        completionRate: 83,
        attendanceRate: 90,
        trend: 'down',
        previousRating: 4.1,
      },
      strengths: ['Technical Skills', 'Problem Solving'],
      areasForImprovement: ['Punctuality', 'Task Completion', 'Communication'],
      feedback: [
        { id: '7', text: 'Needs to improve on meeting maintenance schedules', from: 'Operations Director', date: '2023-05-08' },
        { id: '8', text: 'Good at troubleshooting complex equipment issues', from: 'Field Manager', date: '2023-05-16' },
      ]
    },
  ];
  
  // Department performance summary
  const departmentSummary = {
    operations: {
      avgRating: 4.5,
      completionRate: 92,
      attendanceRate: 96.5,
      trend: 'up',
    },
    production: {
      avgRating: 4.4,
      completionRate: 94,
      attendanceRate: 98,
      trend: 'stable',
    },
    agronomy: {
      avgRating: 4.8,
      completionRate: 98,
      attendanceRate: 97,
      trend: 'up',
    },
    maintenance: {
      avgRating: 3.9,
      completionRate: 85,
      attendanceRate: 92,
      trend: 'down',
    },
  };
  
  // Top performers
  const topPerformers = staffMembers
    .sort((a, b) => b.performance.rating - a.performance.rating)
    .slice(0, 3);
  
  // Staff needing improvement
  const needsImprovement = staffMembers
    .filter(staff => staff.performance.rating < 4.0 || staff.performance.trend === 'down')
    .sort((a, b) => a.performance.rating - b.performance.rating);
  
  useEffect(() => {
    // Simulate loading report data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);
  
  const getFilteredStaff = () => {
    switch (activeTab) {
      case 'operations':
        return staffMembers.filter(staff => staff.department === 'operations');
      case 'production':
        return staffMembers.filter(staff => staff.department === 'production');
      case 'agronomy':
        return staffMembers.filter(staff => staff.department === 'agronomy');
      case 'maintenance':
        return staffMembers.filter(staff => staff.department === 'maintenance');
      case 'top':
        return topPerformers;
      case 'improvement':
        return needsImprovement;
      default:
        return staffMembers;
    }
  };
  
  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return colors.success;
    if (rating >= 4.0) return colors.primary;
    if (rating >= 3.5) return colors.warning;
    return colors.danger;
  };
  
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={14} color={colors.success} />;
      case 'down':
        return <TrendingDown size={14} color={colors.danger} />;
      default:
        return null;
    }
  };
  
  const renderStaffCard = ({ item }: { item: any }) => (
    <View style={styles.staffCard}>
      <View style={styles.staffCardHeader}>
        <View style={styles.staffInfo}>
          {item.image ? (
            <Image source={{ uri: item.image }} style={styles.staffImage} />
          ) : (
            <View style={styles.staffImagePlaceholder}>
              <User size={20} color={colors.white} />
            </View>
          )}
          <View style={styles.staffDetails}>
            <Text style={styles.staffName}>{item.name}</Text>
            <Text style={styles.staffRole}>{item.role}</Text>
          </View>
        </View>
        <View style={styles.ratingContainer}>
          <Text style={[styles.ratingValue, { color: getRatingColor(item.performance.rating) }]}>
            {item.performance.rating.toFixed(1)}
          </Text>
          {getTrendIcon(item.performance.trend)}
        </View>
      </View>
      
      <View style={styles.performanceMetrics}>
        <View style={styles.performanceMetric}>
          <Text style={styles.metricValue}>{item.performance.completionRate}%</Text>
          <Text style={styles.metricLabel}>Task Completion</Text>
        </View>
        
        <View style={styles.performanceMetric}>
          <Text style={styles.metricValue}>{item.performance.tasksCompleted}</Text>
          <Text style={styles.metricLabel}>Tasks Completed</Text>
        </View>
        
        <View style={styles.performanceMetric}>
          <Text style={styles.metricValue}>{item.performance.attendanceRate}%</Text>
          <Text style={styles.metricLabel}>Attendance</Text>
        </View>
      </View>
      
      <View style={styles.strengthsContainer}>
        <Text style={styles.strengthsTitle}>Strengths:</Text>
        <View style={styles.strengthsList}>
          {item.strengths.map((strength: string, index: number) => (
            <View key={index} style={styles.strengthBadge}>
              <Text style={styles.strengthText}>{strength}</Text>
            </View>
          ))}
        </View>
      </View>
      
      {item.areasForImprovement.length > 0 && (
        <View style={styles.improvementContainer}>
          <Text style={styles.improvementTitle}>Areas for Improvement:</Text>
          <View style={styles.improvementList}>
            {item.areasForImprovement.map((area: string, index: number) => (
              <View key={index} style={styles.improvementBadge}>
                <Text style={styles.improvementText}>{area}</Text>
              </View>
            ))}
          </View>
        </View>
      )}
      
      {item.feedback.length > 0 && (
        <View style={styles.feedbackContainer}>
          <Text style={styles.feedbackTitle}>Recent Feedback:</Text>
          {item.feedback.slice(0, 1).map((feedback: any) => (
            <View key={feedback.id} style={styles.feedbackItem}>
              <Text style={styles.feedbackText}>"{feedback.text}"</Text>
              <View style={styles.feedbackMeta}>
                <Text style={styles.feedbackFrom}>- {feedback.from}</Text>
                <Text style={styles.feedbackDate}>{feedback.date}</Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );
  
  const renderDepartmentSummaryItem = ({ item }: { item: [string, any] }) => {
    const [department, data] = item;
    
    return (
      <View style={styles.departmentItem}>
        <View style={styles.departmentHeader}>
          <Text style={styles.departmentName}>{department}</Text>
          <View style={styles.departmentRating}>
            <Text style={[styles.departmentRatingValue, { color: getRatingColor(data.avgRating) }]}>
              {data.avgRating.toFixed(1)}
            </Text>
            {getTrendIcon(data.trend)}
          </View>
        </View>
        
        <View style={styles.departmentMetrics}>
          <View style={styles.departmentMetric}>
            <Text style={styles.departmentMetricValue}>{data.completionRate}%</Text>
            <Text style={styles.departmentMetricLabel}>Completion</Text>
          </View>
          
          <View style={styles.departmentMetric}>
            <Text style={styles.departmentMetricValue}>{data.attendanceRate}%</Text>
            <Text style={styles.departmentMetricLabel}>Attendance</Text>
          </View>
        </View>
      </View>
    );
  };
  
  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Stack.Screen 
          options={{
            title: 'Staff Performance Report',
            headerShown: true,
          }}
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Generating report...</Text>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Staff Performance Report',
          headerShown: true,
        }}
      />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.reportHeader}>
          <View style={styles.farmInfo}>
            <Text style={styles.farmName}>{currentFarm?.name || 'Farm Name'}</Text>
            <View style={styles.reportMetaRow}>
              <Calendar size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <Clock size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                Generated at {new Date().toLocaleTimeString()}
              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <FileText size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>Staff Performance Report</Text>
            </View>
          </View>
          
          <View style={styles.reportActions}>
            <TouchableOpacity style={styles.reportAction}>
              <Download size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Share2 size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Printer size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.overviewSection}>
          <Text style={styles.sectionTitle}>Performance Overview</Text>
          
          <View style={styles.overviewCards}>
            <View style={styles.overviewCard}>
              <View style={[styles.overviewIconContainer, { backgroundColor: colors.primary + '20' }]}>
                <Users size={24} color={colors.primary} />
              </View>
              <Text style={styles.overviewValue}>{staffMembers.length}</Text>
              <Text style={styles.overviewLabel}>Total Staff</Text>
            </View>
            
            <View style={styles.overviewCard}>
              <View style={[styles.overviewIconContainer, { backgroundColor: colors.success + '20' }]}>
                <Star size={24} color={colors.success} />
              </View>
              <Text style={styles.overviewValue}>
                {(staffMembers.reduce((sum, staff) => sum + staff.performance.rating, 0) / staffMembers.length).toFixed(1)}
              </Text>
              <Text style={styles.overviewLabel}>Avg. Rating</Text>
            </View>
            
            <View style={styles.overviewCard}>
              <View style={[styles.overviewIconContainer, { backgroundColor: colors.info + '20' }]}>
                <CheckCircle size={24} color={colors.info} />
              </View>
              <Text style={styles.overviewValue}>
                {Math.round(staffMembers.reduce((sum, staff) => sum + staff.performance.completionRate, 0) / staffMembers.length)}%
              </Text>
              <Text style={styles.overviewLabel}>Task Completion</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.departmentSection}>
          <Text style={styles.sectionTitle}>Department Performance</Text>
          
          <View style={styles.departmentList}>
            {Object.entries(departmentSummary).map((item, index) => (
              <View key={item[0]} style={styles.departmentItemContainer}>
                {renderDepartmentSummaryItem({ item })}
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.topPerformersSection}>
          <Text style={styles.sectionTitle}>Top Performers</Text>
          
          <View style={styles.topPerformersList}>
            {topPerformers.map((performer, index) => (
              <View key={performer.id} style={styles.topPerformerItem}>
                <View style={styles.topPerformerRank}>
                  <Text style={styles.topPerformerRankText}>{index + 1}</Text>
                </View>
                
                <View style={styles.topPerformerInfo}>
                  {performer.image ? (
                    <Image source={{ uri: performer.image }} style={styles.topPerformerImage} />
                  ) : (
                    <View style={styles.topPerformerImagePlaceholder}>
                      <User size={16} color={colors.white} />
                    </View>
                  )}
                  
                  <View style={styles.topPerformerDetails}>
                    <Text style={styles.topPerformerName}>{performer.name}</Text>
                    <Text style={styles.topPerformerRole}>{performer.role}</Text>
                  </View>
                </View>
                
                <View style={styles.topPerformerRating}>
                  <Star size={16} color={getRatingColor(performer.performance.rating)} />
                  <Text style={[styles.topPerformerRatingValue, { color: getRatingColor(performer.performance.rating) }]}>
                    {performer.performance.rating.toFixed(1)}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
        
        {needsImprovement.length > 0 && (
          <View style={styles.needsImprovementSection}>
            <Text style={styles.sectionTitle}>Needs Improvement</Text>
            
            <View style={styles.needsImprovementList}>
              {needsImprovement.map((staff) => (
                <View key={staff.id} style={styles.needsImprovementItem}>
                  <View style={styles.needsImprovementInfo}>
                    {staff.image ? (
                      <Image source={{ uri: staff.image }} style={styles.needsImprovementImage} />
                    ) : (
                      <View style={styles.needsImprovementImagePlaceholder}>
                        <User size={16} color={colors.white} />
                      </View>
                    )}
                    
                    <View style={styles.needsImprovementDetails}>
                      <Text style={styles.needsImprovementName}>{staff.name}</Text>
                      <Text style={styles.needsImprovementRole}>{staff.role}</Text>
                    </View>
                  </View>
                  
                  <View style={styles.needsImprovementRating}>
                    <AlertTriangle size={16} color={getRatingColor(staff.performance.rating)} />
                    <Text style={[styles.needsImprovementRatingValue, { color: getRatingColor(staff.performance.rating) }]}>
                      {staff.performance.rating.toFixed(1)}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
        
        <View style={styles.staffListSection}>
          <View style={styles.staffListHeader}>
            <Text style={styles.sectionTitle}>Staff Performance Details</Text>
            <View style={styles.staffListActions}>
              <TouchableOpacity style={styles.staffListAction}>
                <Search size={20} color={colors.gray[600]} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.staffListAction}>
                <Filter size={20} color={colors.gray[600]} />
              </TouchableOpacity>
            </View>
          </View>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tabsContainer}
          >
            <TouchableOpacity 
              style={[
                styles.tab,
                activeTab === 'all' && styles.activeTab
              ]}
              onPress={() => setActiveTab('all')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'all' && styles.activeTabText
              ]}>All</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.tab,
                activeTab === 'operations' && styles.activeTab
              ]}
              onPress={() => setActiveTab('operations')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'operations' && styles.activeTabText
              ]}>Operations</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.tab,
                activeTab === 'production' && styles.activeTab
              ]}
              onPress={() => setActiveTab('production')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'production' && styles.activeTabText
              ]}>Production</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.tab,
                activeTab === 'agronomy' && styles.activeTab
              ]}
              onPress={() => setActiveTab('agronomy')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'agronomy' && styles.activeTabText
              ]}>Agronomy</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.tab,
                activeTab === 'maintenance' && styles.activeTab
              ]}
              onPress={() => setActiveTab('maintenance')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'maintenance' && styles.activeTabText
              ]}>Maintenance</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.tab,
                activeTab === 'top' && styles.activeTab
              ]}
              onPress={() => setActiveTab('top')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'top' && styles.activeTabText
              ]}>Top Performers</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.tab,
                activeTab === 'improvement' && styles.activeTab
              ]}
              onPress={() => setActiveTab('improvement')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'improvement' && styles.activeTabText
              ]}>Needs Improvement</Text>
            </TouchableOpacity>
          </ScrollView>
          
          <View style={styles.staffCardsList}>
            {getFilteredStaff().map(staff => (
              <View key={staff.id} style={styles.staffCardContainer}>
                {renderStaffCard({ item: staff })}
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.recommendationsSection}>
          <Text style={styles.sectionTitle}>Recommendations</Text>
          
          <View style={styles.recommendationItem}>
            <View style={[styles.recommendationPriority, { backgroundColor: colors.primary }]} />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>Training Opportunities</Text>
              <Text style={styles.recommendationText}>
                Schedule training sessions for maintenance staff to improve task completion rates.
              </Text>
            </View>
          </View>
          
          <View style={styles.recommendationItem}>
            <View style={[styles.recommendationPriority, { backgroundColor: colors.success }]} />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>Recognition Program</Text>
              <Text style={styles.recommendationText}>
                Implement a monthly recognition program to acknowledge top performers like Emily Davis and John Doe.
              </Text>
            </View>
          </View>
          
          <View style={styles.recommendationItem}>
            <View style={[styles.recommendationPriority, { backgroundColor: colors.warning }]} />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>Performance Improvement Plan</Text>
              <Text style={styles.recommendationText}>
                Develop a performance improvement plan for Robert Smith focusing on punctuality and task completion.
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.signatureSection}>
          <Text style={styles.signatureLabel}>Report Generated By:</Text>
          <Text style={styles.signatureName}>{user?.displayName || 'Farm Manager'}</Text>
          <Text style={styles.signatureDate}>{new Date().toLocaleDateString()}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[700],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmInfo: {
    flex: 1,
  },
  farmName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
  },
  reportMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  reportMetaText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 8,
  },
  reportActions: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  reportAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 12,
  },
  overviewSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  overviewCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  overviewCard: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: colors.gray[50],
    marginHorizontal: 4,
  },
  overviewIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  overviewValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
  },
  overviewLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
    textAlign: 'center',
  },
  departmentSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  departmentList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  departmentItemContainer: {
    width: '50%',
    padding: 4,
  },
  departmentItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    // height: '100%',
  },
  departmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  departmentName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    textTransform: 'capitalize',
  },
  departmentRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  departmentRatingValue: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 4,
  },
  departmentMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  departmentMetric: {
    alignItems: 'center',
  },
  departmentMetricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  departmentMetricLabel: {
    fontSize: 12,
    color: colors.gray[600],
  },
  topPerformersSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  topPerformersList: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  topPerformerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  topPerformerRank: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  topPerformerRankText: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.white,
  },
  topPerformerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  topPerformerImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  topPerformerImagePlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.gray[400],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  topPerformerDetails: {
    flex: 1,
  },
  topPerformerName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  topPerformerRole: {
    fontSize: 12,
    color: colors.gray[600],
  },
  topPerformerRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  topPerformerRatingValue: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  needsImprovementSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  needsImprovementList: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  needsImprovementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  needsImprovementInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  needsImprovementImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  needsImprovementImagePlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.gray[400],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  needsImprovementDetails: {
    flex: 1,
  },
  needsImprovementName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  needsImprovementRole: {
    fontSize: 12,
    color: colors.gray[600],
  },
  needsImprovementRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  needsImprovementRatingValue: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  staffListSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  staffListHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  staffListActions: {
    flexDirection: 'row',
  },
  staffListAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  tabsContainer: {
    paddingBottom: 12,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    marginRight: 8,
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  activeTabText: {
    color: colors.white,
    fontWeight: '500',
  },
  staffCardsList: {
    marginTop: 8,
  },
  staffCardContainer: {
    marginBottom: 16,
  },
  staffCard: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 16,
  },
  staffCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  staffInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  staffImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  staffImagePlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.gray[400],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  staffDetails: {
    flex: 1,
  },
  staffName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 2,
  },
  staffRole: {
    fontSize: 14,
    color: colors.gray[600],
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingValue: {
    fontSize: 20,
    fontWeight: '700',
    marginRight: 4,
  },
  performanceMetrics: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 6,
    padding: 10,
    marginBottom: 12,
  },
  performanceMetric: {
    flex: 1,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  metricLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 2,
  },
  strengthsContainer: {
    marginBottom: 12,
  },
  strengthsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  strengthsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  strengthBadge: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  strengthText: {
    fontSize: 12,
    color: colors.primary,
  },
  improvementContainer: {
    marginBottom: 12,
  },
  improvementTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  improvementList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  improvementBadge: {
    backgroundColor: colors.warning + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  improvementText: {
    fontSize: 12,
    color: colors.warning,
  },
  feedbackContainer: {
    marginTop: 4,
  },
  feedbackTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  feedbackItem: {
    backgroundColor: colors.white,
    borderRadius: 6,
    padding: 10,
  },
  feedbackText: {
    fontSize: 14,
    color: colors.gray[800],
    fontStyle: 'italic',
    marginBottom: 4,
  },
  feedbackMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  feedbackFrom: {
    fontSize: 12,
    color: colors.gray[700],
  },
  feedbackDate: {
    fontSize: 12,
    color: colors.gray[600],
  },
  recommendationsSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recommendationItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  recommendationPriority: {
    width: 4,
    borderRadius: 2,
    marginRight: 12,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  recommendationText: {
    fontSize: 12,
    color: colors.gray[600],
    lineHeight: 18,
  },
  signatureSection: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  signatureLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  signatureName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  signatureDate: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
  },
});