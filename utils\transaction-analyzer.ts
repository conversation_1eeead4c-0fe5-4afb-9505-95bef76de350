import { TransactionCategory } from '@/types';

interface TransactionAnalysis {
  category: TransactionCategory;
  isExpense: boolean;
  suggestedPlantId?: string;
  suggestedGardenId?: string;
  anomalyDetected?: boolean;
  anomalyReason?: string;
  estimatedProfit?: number;
}

export function analyzeTransactionWithAI(description: string, amount: number = 0): TransactionAnalysis {
  // This is a simplified version - in production, you might use an actual AI service
  description = description.toLowerCase();
  
  // Determine if it's an expense or income
  const isExpense = !description.includes('sale') && 
                    !description.includes('revenue') && 
                    !description.includes('income') &&
                    !description.includes('harvest');
  
  // Determine category based on keywords
  let category: TransactionCategory = 'other';
  
  if (description.includes('seed') || description.includes('seedling') || description.includes('sapling')) {
    category = 'seed_cost';
  } else if (description.includes('water') || description.includes('irrigation') || description.includes('sprinkler')) {
    category = 'irrigation';
  } else if (description.includes('pesticide') || description.includes('insecticide') || description.includes('fungicide') || description.includes('pest control')) {
    category = 'pesticide';
  } else if (description.includes('fertilizer') || description.includes('manure') || description.includes('compost') || description.includes('nutrient')) {
    category = 'fertilizer';
  } else if (description.includes('worker') || description.includes('labor') || description.includes('staff') || description.includes('wage')) {
    category = 'labor';
  } else if (description.includes('equipment') || description.includes('tool') || description.includes('machine') || description.includes('tractor')) {
    category = 'equipment';
  } else if (description.includes('sale') || description.includes('revenue') || description.includes('income') || description.includes('harvest')) {
    category = 'harvest_sale';
  }
  
  // Check for anomalies
  let anomalyDetected = false;
  let anomalyReason = '';
  
  // Anomaly: Category is harvest_sale but amount is negative
  if (category === 'harvest_sale' && amount < 0) {
    anomalyDetected = true;
    anomalyReason = 'Sale amount should be positive';
  }
  
  // Anomaly: Category is an expense but amount is positive (assuming expenses are stored as negative values)
  if (category !== 'harvest_sale' && amount > 0 && isExpense) {
    anomalyDetected = true;
    anomalyReason = 'Expense amount should be negative';
  }
  
  // Anomaly: Unusually high amount for seed costs
  if (category === 'seed_cost' && Math.abs(amount) > 5000) {
    anomalyDetected = true;
    anomalyReason = 'Unusually high amount for seed costs';
  }
  
  // Try to extract plant or garden references
  let suggestedPlantId: string | undefined;
  let suggestedGardenId: string | undefined;
  
  // Simple regex to find IDs in format PLT123 or GRD123
  const plantMatch = description.match(/PLT[0-9]+/i);
  if (plantMatch) {
    suggestedPlantId = plantMatch[0];
  }
  
  const gardenMatch = description.match(/GRD[0-9]+/i);
  if (gardenMatch) {
    suggestedGardenId = gardenMatch[0];
  }
  
  // Estimate profit for harvest sales (simplified)
  let estimatedProfit: number | undefined;
  if (category === 'harvest_sale' && amount > 0) {
    // Assume 60% profit margin for simplicity
    estimatedProfit = amount * 0.6;
  }
  
  return {
    category,
    isExpense,
    suggestedPlantId,
    suggestedGardenId,
    anomalyDetected,
    anomalyReason: anomalyDetected ? anomalyReason : undefined,
    estimatedProfit
  };
}

// Function to suggest corrections for anomalies
export function suggestTransactionCorrections(
  description: string, 
  amount: number, 
  category: TransactionCategory
): { correctedAmount?: number; correctedCategory?: TransactionCategory } {
  const result: { correctedAmount?: number; correctedCategory?: TransactionCategory } = {};
  
  // If it's a sale but amount is negative, suggest positive amount
  if (category === 'harvest_sale' && amount < 0) {
    result.correctedAmount = Math.abs(amount);
  }
  
  // If it's an expense but amount is positive, suggest negative amount
  if (category !== 'harvest_sale' && amount > 0) {
    result.correctedAmount = -Math.abs(amount);
  }
  
  // Suggest category correction based on description
  const analysis = analyzeTransactionWithAI(description);
  if (analysis.category !== category) {
    result.correctedCategory = analysis.category;
  }
  
  return result;
}
