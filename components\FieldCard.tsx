import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { colors } from '@/constants/colors';
import { Field } from '@/types';
import { useTranslation } from '@/i18n/useTranslation';

interface FieldCardProps {
  field: Field;
  onPress?: () => void;
  style?: any; // Add this to support custom styles
}

export const FieldCard: React.FC<FieldCardProps> = ({ field, onPress, style }) => {

  const {t}=useTranslation()
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return colors.success;
      case 'inactive':
        return colors.gray[400];
      case 'irrigated':
        return colors.info;
      default:
        return colors.gray[500];
    }
  };
  
  const getStatusLabel = (status: string) => {

    // return t(`entity.field.status${status}`)
    return status.charAt(0).toUpperCase() + status.slice(1);
  };
  
  return (
    <TouchableOpacity 
      style={[styles.card, style]} 
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.imageContainer}>
        {field.image ? (
          <Image 
            source={{ uri: field.image }} 
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.placeholderImage} />
        )}
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>{field.name}</Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(field.status) + '20' }
          ]}>
            <Text style={[
              styles.statusText,
              { color: getStatusColor(field.status) }
            ]}>
              {getStatusLabel(field.status)}
            </Text>
          </View>
        </View>
        
        <View style={styles.details}>
          <Text style={styles.detailText}>
            {field.size?.toFixed(2)} {t(`common.areaUnit.${field?.sizeUnit}`)}
          </Text>
          {field.cropType && (
            <Text style={styles.cropType}>
              {field.cropType}
            </Text>
          )}
        </View>
        
        {field.health !== undefined && (
          <View style={styles.healthContainer}>
            <Text style={styles.healthLabel}>Health</Text>
            <View style={styles.healthBarContainer}>
              <View 
                style={[
                  styles.healthBar, 
                  { 
                    width: `${field.health}%`,
                    backgroundColor: field.health > 70 
                      ? colors.success 
                      : field.health > 40 
                        ? colors.warning 
                        : colors.danger
                  }
                ]} 
              />
            </View>
            <Text style={styles.healthPercentage}>{field.health}%</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    width: 280, // Fixed width for consistency
    marginRight: 16, // Add margin between cards
  },
  imageContainer: {
    width: '100%',
    height: 120,
    backgroundColor: colors.gray[200], // Background for placeholder
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.gray[200],
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1, // Allow text to wrap
    marginRight: 8, // Space between title and badge
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  cropType: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  healthContainer: {
    marginTop: 8,
  },
  healthLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  healthBarContainer: {
    height: 6,
    backgroundColor: colors.gray[200],
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  healthBar: {
    height: '100%',
    borderRadius: 3,
  },
  healthPercentage: {
    fontSize: 12,
    color: colors.gray[600],
    textAlign: 'right',
  },
});

export default FieldCard;
