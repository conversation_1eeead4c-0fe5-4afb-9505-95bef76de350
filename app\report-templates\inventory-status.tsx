import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Image,
  FlatList,
} from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import {
  Calendar,
  Clock,
  FileText,
  Download,
  Share2,
  Printer,
  Package,
  Tractor,
  Wheat,
  Droplets,
  Sprout,
  Shovel,
  AlertTriangle,
  ArrowRight,
  Search,
  Filter,
} from 'lucide-react-native';
import { normalizeDate } from '@/utils/dateUtils';

export default function InventoryStatusReport() {
  const { id } = useLocalSearchParams()
  const { currentFarm, inventory, equipment, getReportById } = useFarmStore();
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [report, setReport] = useState({})
  const loadReport = async () => {
    const response = await getReportById(id)
    setReport(response)
  }
  useEffect(() => {
    if (id) loadReport()
  }, [id]);

  // useEffect(() => {
  //   console.log({ report })
  // }, [report])

  // // Mock inventory data
  // const inventoryItems = [
  //   { id: '1', name: 'Wheat Seeds', category: 'seeds', quantity: 250, unit: 'kg', status: 'sufficient', lastUpdated: '2023-05-15' },
  //   { id: '2', name: 'Fertilizer', category: 'fertilizer', quantity: 500, unit: 'kg', status: 'sufficient', lastUpdated: '2023-05-10' },
  //   { id: '3', name: 'Pesticide', category: 'pesticide', quantity: 20, unit: 'liters', status: 'low', lastUpdated: '2023-05-12' },
  //   { id: '4', name: 'Irrigation Pipes', category: 'equipment', quantity: 15, unit: 'pieces', status: 'sufficient', lastUpdated: '2023-04-28' },
  //   { id: '5', name: 'Tomato Seeds', category: 'seeds', quantity: 5, unit: 'kg', status: 'low', lastUpdated: '2023-05-14' },
  //   { id: '6', name: 'Corn Seeds', category: 'seeds', quantity: 100, unit: 'kg', status: 'sufficient', lastUpdated: '2023-05-08' },
  //   { id: '7', name: 'Herbicide', category: 'pesticide', quantity: 10, unit: 'liters', status: 'depleted', lastUpdated: '2023-05-05' },
  //   { id: '8', name: 'Shovels', category: 'tools', quantity: 8, unit: 'pieces', status: 'sufficient', lastUpdated: '2023-04-20' },
  // ];

  // // Mock equipment data
  // const equipmentItems = [
  //   { id: '1', name: 'Tractor', type: 'vehicle', status: 'operational', lastMaintenance: '2023-04-15', nextMaintenance: '2023-07-15' },
  //   { id: '2', name: 'Harvester', type: 'vehicle', status: 'maintenance', lastMaintenance: '2023-05-10', nextMaintenance: '2023-08-10' },
  //   { id: '3', name: 'Irrigation System', type: 'fixed', status: 'operational', lastMaintenance: '2023-03-20', nextMaintenance: '2023-06-20' },
  //   { id: '4', name: 'Plow', type: 'attachment', status: 'repair', lastMaintenance: '2023-02-05', nextMaintenance: '2023-05-05' },
  // ];

  // // Inventory summary
  // const inventorySummary = {
  //   totalItems: inventoryItems.length,
  //   sufficientItems: inventoryItems.filter(item => item.status === 'sufficient').length,
  //   lowItems: inventoryItems.filter(item => item.status === 'low').length,
  //   depletedItems: inventoryItems.filter(item => item.status === 'depleted').length,
  //   categories: {
  //     seeds: inventoryItems.filter(item => item.category === 'seeds').length,
  //     fertilizer: inventoryItems.filter(item => item.category === 'fertilizer').length,
  //     pesticide: inventoryItems.filter(item => item.category === 'pesticide').length,
  //     equipment: inventoryItems.filter(item => item.category === 'equipment').length,
  //     tools: inventoryItems.filter(item => item.category === 'tools').length,
  //   },
  // };

  // // Equipment summary
  // const equipmentSummary = {
  //   totalItems: equipmentItems.length,
  //   operationalItems: equipmentItems.filter(item => item.status === 'operational').length,
  //   maintenanceItems: equipmentItems.filter(item => item.status === 'maintenance').length,
  //   repairItems: equipmentItems.filter(item => item.status === 'repair').length,
  // };

  // // Recent inventory activities
  // const recentActivities = [
  //   { id: '1', item: 'Fertilizer', action: 'added', quantity: 200, unit: 'kg', date: '2023-05-10', user: 'John Doe' },
  //   { id: '2', name: 'Pesticide', action: 'used', quantity: 5, unit: 'liters', date: '2023-05-12', user: 'Sarah Williams' },
  //   { id: '3', name: 'Wheat Seeds', action: 'added', quantity: 100, unit: 'kg', date: '2023-05-15', user: 'Mike Johnson' },
  // ];

  useEffect(() => {
    // Simulate loading report data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const getFilteredItems = () => {
    switch (activeTab) {
      case 'seeds':
        return report?.inventorySummary?.inventoryItems.filter(item => item.type === 'seeds');
      case 'fertilizer':
        return report?.inventorySummary?.inventoryItems.filter(item => item.type === 'fertilizer');
      case 'pesticide':
        return report?.inventorySummary?.inventoryItems.filter(item => item.type === 'pesticide');
      case 'crop':
        return report?.inventorySummary?.inventoryItems.filter(item => item.type === 'crop');
      // case 'tools':
      //   return inventoryItems.filter(item => item.category === 'tools');
      case 'low':
        return report?.inventorySummary?.inventoryItems.filter(item => item.status === 'low' || item.status === 'depleted');
      default:
        return report?.inventorySummary?.inventoryItems;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'seeds':
        return <Sprout size={20} color={colors.success} />;
      case 'fertilizer':
        return <Wheat size={20} color={colors.warning} />;
      case 'pesticide':
        return <Droplets size={20} color={colors.danger} />;
      case 'equipment':
        return <Tractor size={20} color={colors.info} />;
      case 'tools':
        return <Shovel size={20} color={colors.secondary} />;
      default:
        return <Package size={20} color={colors.primary} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sufficient':
        return colors.success;
      case 'low':
        return colors.warning;
      case 'depleted':
        return colors.danger;
      case 'operational':
        return colors.success;
      case 'maintenance':
        return colors.warning;
      case 'repair':
        return colors.danger;
      default:
        return colors.gray[500];
    }
  };

  const renderInventoryItem = ({ item }: { item: any }) => (
    <View style={styles.inventoryItem}>
      <View style={styles.inventoryItemHeader}>
        <View style={styles.inventoryItemCategory}>
          {getCategoryIcon(item.category)}
          <Text style={styles.inventoryItemCategoryText}>{item.category}</Text>
        </View>
        <View style={[
          styles.inventoryItemStatus,
          { backgroundColor: getStatusColor(item.status) + '20' }
        ]}>
          <Text style={[
            styles.inventoryItemStatusText,
            { color: getStatusColor(item.status) }
          ]}>
            {item.status}
          </Text>
        </View>
      </View>

      <Text style={styles.inventoryItemName}>{item.name}</Text>

      <View style={styles.inventoryItemDetails}>
        <Text style={styles.inventoryItemQuantity}>
          {item.quantity} {item.unit}
        </Text>
        <Text style={styles.inventoryItemDate}>
          Updated: {item.lastUpdated}
        </Text>
      </View>
    </View>
  );

  const renderEquipmentItem = ({ item }: { item: any }) => (
    <View style={styles.equipmentItem}>
      <View style={styles.equipmentItemHeader}>
        <Text style={styles.equipmentItemType}>{item.type}</Text>
        <View style={[
          styles.equipmentItemStatus,
          { backgroundColor: getStatusColor(item.status) + '20' }
        ]}>
          <Text style={[
            styles.equipmentItemStatusText,
            { color: getStatusColor(item.status) }
          ]}>
            {item.status}
          </Text>
        </View>
      </View>

      <Text style={styles.equipmentItemName}>{item.name}</Text>

      <View style={styles.equipmentItemDetails}>
        <Text style={styles.equipmentItemMaintenance}>
          Last: {item.lastMaintenance}
        </Text>
        <Text style={styles.equipmentItemMaintenance}>
          Next: {item.nextMaintenance}
        </Text>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Stack.Screen
          options={{
            title: 'Inventory Status Report',
            headerShown: true,
          }}
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Generating report...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Inventory Status Report',
          headerShown: true,
        }}
      />

      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.reportHeader}>
          <View style={styles.farmInfo}>
            <Text style={styles.farmName}>{currentFarm?.name || 'Farm Name'}</Text>
            <View style={styles.reportMetaRow}>
              <Calendar size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                {normalizeDate(report?.date || 'N/A')}

              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <Clock size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>
                Generated at {normalizeDate(report?.createdAt || 'N/A')}
              </Text>
            </View>
            <View style={styles.reportMetaRow}>
              <FileText size={16} color={colors.gray[600]} />
              <Text style={styles.reportMetaText}>Inventory Status Report</Text>
            </View>
          </View>

          <View style={styles.reportActions}>
            <TouchableOpacity style={styles.reportAction}>
              <Download size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Share2 size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportAction}>
              <Printer size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>Inventory Summary</Text>

          <View style={styles.summaryCards}>
            <View style={styles.summaryCard}>
              <View style={[styles.summaryIconContainer, { backgroundColor: colors.primary + '20' }]}>
                <Package size={24} color={colors.primary} />
              </View>
              <Text style={styles.summaryValue}>{report?.inventorySummary?.totalItems}</Text>
              <Text style={styles.summaryLabel}>Total Items</Text>
            </View>

            <View style={styles.summaryCard}>
              <View style={[styles.summaryIconContainer, { backgroundColor: colors.success + '20' }]}>
                <Sprout size={24} color={colors.success} />
              </View>
              <Text style={styles.summaryValue}>{report?.inventorySummary?.categories?.seeds || 0}</Text>
              <Text style={styles.summaryLabel}>Seeds</Text>
            </View>

            <View style={styles.summaryCard}>
              <View style={[styles.summaryIconContainer, { backgroundColor: colors.warning + '20' }]}>
                <AlertTriangle size={24} color={colors.warning} />
              </View>
              <Text style={styles.summaryValue}>{report?.inventorySummary?.lowItems}</Text>
              <Text style={styles.summaryLabel}>Low Stock</Text>
            </View>
          </View>
        </View>

        <View style={styles.equipmentSummarySection}>
          <Text style={styles.sectionTitle}>Equipment Summary</Text>

          <View style={styles.equipmentSummaryCards}>
            <View style={styles.equipmentSummaryCard}>
              <View style={styles.equipmentSummaryHeader}>
                <Tractor size={20} color={colors.primary} />
                <Text style={styles.equipmentSummaryTitle}>Total Equipment</Text>
              </View>
              <Text style={styles.equipmentSummaryValue}>{report?.equipmentSummary?.totalItems}</Text>
            </View>

            <View style={styles.equipmentStatusRow}>
              <View style={styles.equipmentStatusItem}>
                <View style={[styles.statusDot, { backgroundColor: colors.success }]} />
                <Text style={styles.equipmentStatusText}>Operational: {report?.equipmentSummary?.operationalItems}</Text>
              </View>

              <View style={styles.equipmentStatusItem}>
                <View style={[styles.statusDot, { backgroundColor: colors.warning }]} />
                <Text style={styles.equipmentStatusText}>Maintenance: {report?.equipmentSummary?.maintenanceItems}</Text>
              </View>

              <View style={styles.equipmentStatusItem}>
                <View style={[styles.statusDot, { backgroundColor: colors.danger }]} />
                <Text style={styles.equipmentStatusText}>Repair: {report?.equipmentSummary?.repairItems}</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.inventoryListSection}>
          <View style={styles.inventoryListHeader}>
            <Text style={styles.sectionTitle}>Inventory Items</Text>
            <View style={styles.inventoryActions}>
              <TouchableOpacity style={styles.inventoryAction}>
                <Search size={20} color={colors.gray[600]} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.inventoryAction}>
                <Filter size={20} color={colors.gray[600]} />
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tabsContainer}
          >
            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'all' && styles.activeTab
              ]}
              onPress={() => setActiveTab('all')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'all' && styles.activeTabText
              ]}>All</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'seeds' && styles.activeTab
              ]}
              onPress={() => setActiveTab('seeds')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'seeds' && styles.activeTabText
              ]}>Seeds</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'fertilizer' && styles.activeTab
              ]}
              onPress={() => setActiveTab('fertilizer')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'fertilizer' && styles.activeTabText
              ]}>Fertilizer</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'pesticide' && styles.activeTab
              ]}
              onPress={() => setActiveTab('pesticide')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'pesticide' && styles.activeTabText
              ]}>Pesticide</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'crop' && styles.activeTab
              ]}
              onPress={() => setActiveTab('crop')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'crop' && styles.activeTabText
              ]}>Crop</Text>
            </TouchableOpacity>

            {/* <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'tools' && styles.activeTab
              ]}
              onPress={() => setActiveTab('tools')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'tools' && styles.activeTabText
              ]}>Tools</Text>
            </TouchableOpacity> */}

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'low' && styles.activeTab
              ]}
              onPress={() => setActiveTab('low')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'low' && styles.activeTabText
              ]}>Low Stock</Text>
            </TouchableOpacity>
          </ScrollView>

          <View style={styles.inventoryGrid}>
            {(getFilteredItems()||[]).map(item => (
              <View key={item.id} style={styles.inventoryGridItem}>
                {renderInventoryItem({ item })}
              </View>
            ))}
              {/* ])?.map(item => (
              <View key={item.id} style={styles.inventoryGridItem}>
                {renderInventoryItem({ item })}
              </View>
            ))} */}
          </View>
        </View>

        {/* <View style={styles.equipmentListSection}>
          <Text style={styles.sectionTitle}>Equipment Status</Text>

          <View style={styles.equipmentGrid}>
            {report?.inventorySummary?.equipmentItems.map(item => (
              <View key={item.id} style={styles.equipmentGridItem}>
                {renderEquipmentItem({ item })}
              </View>
            ))}
          </View>
        </View> */}

        <View style={styles.recentActivitiesSection}>
          <Text style={styles.sectionTitle}>Recent Activities</Text>

          {(report?.recentActivities||[]).map(activity => (
            <View key={activity.id} style={styles.activityItem}>
              <View style={styles.activityIconContainer}>
                <ArrowRight size={20} color={colors.primary} />
              </View>

              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>
                  {activity.quantity} {activity.unit} of {activity.name || activity.item} {activity.action}
                </Text>
                <Text style={styles.activityMeta}>
                  by {activity.user} on {activity.date}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* <View style={styles.recommendationsSection}>
          <Text style={styles.sectionTitle}>Recommendations</Text>

          <View style={styles.recommendationItem}>
            <View style={[styles.recommendationPriority, { backgroundColor: colors.danger }]} />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>Restock Herbicide</Text>
              <Text style={styles.recommendationText}>
                Herbicide stock is depleted. Order at least 20 liters.
              </Text>
            </View>
          </View>

          <View style={styles.recommendationItem}>
            <View style={[styles.recommendationPriority, { backgroundColor: colors.warning }]} />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>Maintenance Required</Text>
              <Text style={styles.recommendationText}>
                Schedule maintenance for Plow as it's overdue.
              </Text>
            </View>
          </View>

          <View style={styles.recommendationItem}>
            <View style={[styles.recommendationPriority, { backgroundColor: colors.warning }]} />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>Low Tomato Seeds</Text>
              <Text style={styles.recommendationText}>
                Tomato seed stock is running low. Consider reordering.
              </Text>
            </View>
          </View>
        </View> */}

        <View style={styles.signatureSection}>
          <Text style={styles.signatureLabel}>Report Generated By:</Text>
          <Text style={styles.signatureName}>{report?.createdBy || 'Farm Manager'}</Text>
          <Text style={styles.signatureDate}>{new Date().toLocaleDateString()}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[700],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmInfo: {
    flex: 1,
  },
  farmName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
  },
  reportMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  reportMetaText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 8,
  },
  reportActions: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  reportAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  summarySection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 12,
  },
  summaryCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: colors.gray[50],
    marginHorizontal: 4,
  },
  summaryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
  },
  summaryLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
    textAlign: 'center',
  },
  equipmentSummarySection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  equipmentSummaryCards: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 16,
  },
  equipmentSummaryCard: {
    marginBottom: 12,
  },
  equipmentSummaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  equipmentSummaryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginLeft: 8,
  },
  equipmentSummaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[900],
  },
  equipmentStatusRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: 12,
  },
  equipmentStatusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  equipmentStatusText: {
    fontSize: 12,
    color: colors.gray[700],
  },
  inventoryListSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inventoryListHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  inventoryActions: {
    flexDirection: 'row',
  },
  inventoryAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  tabsContainer: {
    paddingBottom: 12,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    marginRight: 8,
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  activeTabText: {
    color: colors.white,
    fontWeight: '500',
  },
  inventoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  inventoryGridItem: {
    width: '50%',
    padding: 4,
  },
  inventoryItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    // height: '100%',
  },
  inventoryItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  inventoryItemCategory: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inventoryItemCategoryText: {
    fontSize: 12,
    color: colors.gray[600],
    marginLeft: 4,
  },
  inventoryItemStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  inventoryItemStatusText: {
    fontSize: 10,
    fontWeight: '500',
  },
  inventoryItemName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  inventoryItemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inventoryItemQuantity: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
  },
  inventoryItemDate: {
    fontSize: 10,
    color: colors.gray[500],
  },
  equipmentListSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  equipmentGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  equipmentGridItem: {
    width: '50%',
    padding: 4,
  },
  equipmentItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    // height: '100%',
  },
  equipmentItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  equipmentItemType: {
    fontSize: 12,
    color: colors.gray[600],
  },
  equipmentItemStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  equipmentItemStatusText: {
    fontSize: 10,
    fontWeight: '500',
  },
  equipmentItemName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  equipmentItemDetails: {
    flexDirection: 'column',
  },
  equipmentItemMaintenance: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 2,
  },
  recentActivitiesSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  activityIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 2,
  },
  activityMeta: {
    fontSize: 12,
    color: colors.gray[600],
  },
  recommendationsSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recommendationItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  recommendationPriority: {
    width: 4,
    borderRadius: 2,
    marginRight: 12,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 4,
  },
  recommendationText: {
    fontSize: 12,
    color: colors.gray[600],
    lineHeight: 18,
  },
  signatureSection: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  signatureLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  signatureName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  signatureDate: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
  },
});