import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import DatePicker from '@/components/DatePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { Wheat, Calendar, ChevronDown, Check } from 'lucide-react-native';
import { v4 as uuidv4 } from 'uuid';
import { useTranslation } from '@/i18n/useTranslation';
import { MaterialIcons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

// Season options
const SEASONS = ['Spring', 'Summer', 'Fall', 'Winter'];

// Unit options
const UNITS = ['kg', 'lb', 'ton', 'bushel', 'count'];

export default function HarvestFieldScreen() {
  const { id, cropId } = useLocalSearchParams();
  const fieldIdString = Array.isArray(id) ? id[0] : id as string;
  const cropIdString = Array.isArray(cropId) ? cropId[0] : cropId as string;

  const { user } = useAuthStore();
  const {
    getField,
    recordFieldHarvest,
    getCrop,
    currentFarm
  } = useFarmStore();
  const { t, isRTL } = useTranslation()
  const [field, setField] = useState<any>(null);
  const [harvestDate, setHarvestDate] = useState(new Date());
  const [season, setSeason] = useState('');
  const [yieldQuantity, setYieldQuantity] = useState('');
  const [yieldUnit, setYieldUnit] = useState('kg');
  const [cropType, setCropType] = useState('');
  const [notes, setNotes] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    cropType: '',
    yieldQuantity: '',
    season: ''
  });
  const [showSeasonModal, setShowSeasonModal] = useState(false);
  const [showUnitModal, setShowUnitModal] = useState(false);
  const [crop, setCrop] = useState<any>(null);

  // Load field data
  useEffect(() => {
    if (fieldIdString) {
      const fieldData = getField(fieldIdString);
      const cropData = getCrop(cropIdString)
      // console.log('fieldData', fieldData, fieldIdString);
      if (fieldData) {
        setField(fieldData);
        // console.log({fieldData},{cropIdString},cropData);
        // Pre-populate crop type from field data
        if (cropData) {
          setCrop(cropData);
          if (cropData.cropType) {
            setCropType(cropData.cropType);
          }
        }


        // Set season based on current date
        const currentMonth = new Date().getMonth();
        if (currentMonth >= 2 && currentMonth <= 4) {
          setSeason('Spring');
        } else if (currentMonth >= 5 && currentMonth <= 7) {
          setSeason('Summer');
        } else if (currentMonth >= 8 && currentMonth <= 10) {
          setSeason('Fall');
        } else {
          setSeason('Winter');
        }
      }
    }
  }, [fieldIdString, getField]);

  const handleAddImage = (uri: string) => {
    setImages([...images, uri]);
  };

  const handleRemoveImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const validateFields = () => {
    const errors = {
      cropType: '',
      yieldQuantity: '',
      season: ''
    };

    let hasErrors = false;

    if (!cropType.trim()) {
      errors.cropType = t('field.cropTypeRequired');
      hasErrors = true;
    }

    if (!yieldQuantity || isNaN(Number(yieldQuantity))) {
      errors.yieldQuantity = t('field.validQuantityRequired');
      hasErrors = true;
    }

    if (!season) {
      errors.season = t('field.seasonRequired');
      hasErrors = true;
    }

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleHarvestField = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    try {
      setIsLoading(true);

      // Upload images if any
      const uploadedImages = [];
      for (const image of images) {
        if (image && !image.startsWith('http')) {
          const uploadedUrl = await uploadImageAsync(image, 'harvests');
          uploadedImages.push(uploadedUrl);
        } else if (image) {
          uploadedImages.push(image);
        }
      }

      // Create harvest data
      const harvestData = {
        harvestDate: harvestDate.toISOString(),
        season,
        yieldQuantity: Number(yieldQuantity),
        yieldUnit,
        cropType,
        notes,
        images: uploadedImages,
      };

      // Record the harvest
      await recordFieldHarvest(fieldIdString, harvestData);

      Alert.alert(
        'Success',
        'Harvest recorded successfully and crop marked as harvested',
        [{ text: 'OK', onPress: () => router.push(`/field/${fieldIdString}`) }]
      );
    } catch (error: any) {
      console.error('Error recording harvest:', error);
      Alert.alert('Error', error.message || 'Failed to record harvest. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderSeasonItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => {
        setSeason(item);
        setShowSeasonModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <Calendar size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item}</Text>
      </View>
      {season === item && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  const renderUnitItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => {
        setYieldUnit(item);
        setShowUnitModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <Wheat size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item}</Text>
      </View>
      {yieldUnit === item && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  if (!field) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: `Harvest ${field.name}`,
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.fieldInfoContainer}>
            <Text style={[styles.fieldName, isRTL && { textAlign: 'right' }]}>{field.name}</Text>
            <Text style={[styles.fieldDetails, isRTL && { textAlign: 'right' }]}>
              {field.size} {field.sizeUnit} • {field.type}
              {field.cropType ? ` • ${field.cropType}` : ''}
            </Text>
          </View>

          <View style={styles.formContainer}>
            <DatePicker
              label={t('field.harvestDate')}
              value={harvestDate}
              onChange={setHarvestDate}
              required={true}
            />

            <View style={styles.inputContainer}>
              <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
                <MaterialIcons name="date-range" size={18} /> {t('field.season')}
              </Text>
              <TouchableOpacity
                style={[styles.dropdownButton, isRTL && { flexDirection: 'row-reverse' }]}
                onPress={() => setShowSeasonModal(true)}
              >
                <MaterialIcons name="calendar-today" size={20} color={colors.gray[500]} style={styles.dropdownIcon} />
                <Text style={[styles.dropdownText, isRTL && { textAlign: 'right' }]}>
                  {season || t('field.selectSeason')}
                </Text>
                <ChevronDown size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            </View>

            <Input
              label={t('field.cropType')}
              placeholder={t('field.enterCropType')}
              value={cropType}
              onChangeText={setCropType}
              containerStyle={styles.inputContainer}
              leftIcon={<MaterialIcons name="agriculture" size={20} color={colors.gray[500]} />}
            />

            <View style={styles.rowContainer}>
              <Input
                label={t('field.yieldQuantity')}
                placeholder={t('field.enterQuantity')}
                value={yieldQuantity}
                onChangeText={setYieldQuantity}
                keyboardType="numeric"
                containerStyle={[styles.inputContainer, { flex: 2, marginRight: 8 }]}
              />

              <View style={[styles.inputContainer, { flex: 1 }]}>
                <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
                  <MaterialIcons name="straighten" size={18} /> {t('field.unit')}
                </Text>
                <TouchableOpacity
                  style={[styles.dropdownButton, isRTL && { flexDirection: 'row-reverse' }]}
                  onPress={() => setShowUnitModal(true)}
                >
                  <Text style={[styles.dropdownText, isRTL && { textAlign: 'right' }]}>{yieldUnit}</Text>
                  <ChevronDown size={20} color={colors.gray[500]} />
                </TouchableOpacity>
              </View>
            </View>

            <Input
              label={t('field.notesOptional')}
              placeholder={t('field.enterNotes')}
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
              leftIcon={<MaterialIcons name="notes" size={20} color={colors.gray[500]} />}
            />

            <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
              <MaterialIcons name="photo-library" size={18} /> {t('field.imagesOptional')}
            </Text>
            <View style={styles.imagesContainer}>
              {images.map((image, index) => (
                <View key={index} style={styles.imagePreviewContainer}>
                  <ImagePicker
                    image={image}
                    onImageSelected={(uri) => {
                      const newImages = [...images];
                      newImages[index] = uri;
                      setImages(newImages);
                    }}
                    size={100}
                    shape="square"
                  />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => handleRemoveImage(index)}
                  >
                    <Text style={styles.removeImageText}>×</Text>
                  </TouchableOpacity>
                </View>
              ))}
              <TouchableOpacity
                style={styles.addImageButton}
                onPress={() => handleAddImage('')}
              >
                <Text style={styles.addImageText}>+</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('common.cancel')}
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? t('common.recording') : t('field.recordHarvest')}
              onPress={handleHarvestField}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={
                isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined
              }
            />
          </View>
        </ScrollView>
        {/* Season Selection Modal */}
        <Modal
          visible={showSeasonModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowSeasonModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Season</Text>
                <TouchableOpacity onPress={() => setShowSeasonModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              <FlatList
                data={SEASONS}
                renderItem={renderSeasonItem}
                keyExtractor={item => item}
                style={styles.modalList}
              />
            </View>
          </View>
        </Modal>

        {/* Unit Selection Modal */}
        <Modal
          visible={showUnitModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowUnitModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Unit</Text>
                <TouchableOpacity onPress={() => setShowUnitModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              <FlatList
                data={UNITS}
                renderItem={renderUnitItem}
                keyExtractor={item => item}
                style={styles.modalList}
              />
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fieldInfoContainer: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  fieldName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[800],
    marginBottom: 4,
  },
  fieldDetails: {
    fontSize: 14,
    color: colors.gray[600],
  },
  formContainer: {
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dropdownIcon: {
    marginRight: 8,
  },
  dropdownText: {
    flex: 1,
    fontSize: 14,
    color: colors.gray[800],
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  imagePreviewContainer: {
    position: 'relative',
    margin: 4,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.danger,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
  },
  addImageText: {
    fontSize: 24,
    color: colors.gray[400],
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    padding: 8,
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
});
