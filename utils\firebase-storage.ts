import { getStorage, ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

/**
 * Uploads an image to Firebase Storage
 * @param uri Local URI of the image
 * @param folder Folder path in Firebase Storage
 * @returns Promise with the download URL
 */
export const uploadImageAsync = async (uri: string, folder: string): Promise<string> => {
  if (!uri) {
    throw new Error('No image URI provided');
  }

  try {
    // Compress the image first to reduce upload size
    const compressedImage = await compressImage(uri);
    const blob = await uriToBlob(compressedImage.uri);
    
    // Create a unique filename
    const filename = uri.split('/').pop() || `image-${Date.now()}.jpg`;
    const storageRef = ref(getStorage(), `${folder}/${filename}`);
    
    // Upload the blob
    const uploadTask = uploadBytesResumable(storageRef, blob);
    
    // Return a promise that resolves with the download URL
    return new Promise((resolve, reject) => {
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // Optional: Track upload progress
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          console.log(`Upload is ${progress}% done`);
        },
        (error) => {
          // Handle unsuccessful uploads
          console.error('Upload failed:', error);
          reject(error);
        },
        async () => {
          // Handle successful uploads
          try {
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
            resolve(downloadURL);
          } catch (error) {
            console.error('Error getting download URL:', error);
            reject(error);
          }
        }
      );
    });
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

/**
 * Converts a URI to a Blob
 * @param uri Image URI
 * @returns Promise with the Blob
 */
const uriToBlob = async (uri: string): Promise<Blob> => {
  if (Platform.OS === 'web') {
    // For web, fetch the image and convert to blob
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob;
  } else {
    // For native platforms, read the file and convert to blob
    const fileInfo = await FileSystem.getInfoAsync(uri);
    
    if (!fileInfo.exists) {
      throw new Error(`File does not exist at ${uri}`);
    }
    
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob;
  }
};

/**
 * Compresses an image to reduce file size
 * @param uri Image URI
 * @returns Promise with the compressed image
 */
const compressImage = async (uri: string) => {
  try {
    const result = await manipulateAsync(
      uri,
      [{ resize: { width: 1200 } }], // Resize to max width of 1200px
      { compress: 0.7, format: SaveFormat.JPEG } // 70% quality JPEG
    );
    return result;
  } catch (error) {
    console.error('Error compressing image:', error);
    // If compression fails, return the original URI
    return { uri };
  }
};