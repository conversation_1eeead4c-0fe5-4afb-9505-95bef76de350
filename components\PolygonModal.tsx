import { calculatePolygonArea } from '@/utils/map-utils';
import { reverseGeocodeAsync } from 'expo-location';
import React from 'react';
import {
  Modal,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import MapView, { Polygon as RNPolygon } from 'react-native-maps';

type Polygon = {
  name: string;
  coordinates: { latitude: number; longitude: number }[];
};

type PolygonModalProps = {
  visible: boolean;
  onClose: () => void;
  polygons: Polygon[];
  onSelect: (polygon: Polygon) => void;
};

const PolygonModal: React.FC<PolygonModalProps> = ({
  visible,
  onClose,
  polygons,
  onSelect,
}) => {

  const returnAddress = async (item: any) => {
    const centerLat = item?.coordinates.reduce((sum, p) => sum + p.latitude, 0) / item?.coordinates.length;
    const centerLng = item?.coordinates.reduce((sum, p) => sum + p.longitude, 0) / item?.coordinates.length;
    try {
      const location = await reverseGeocodeAsync({ latitude: centerLat, longitude: centerLng });
      if (location?.length > 0) {
        const loc = location[0];
        const addressParts = [loc.street, loc.city, loc.region, loc.country].filter(Boolean);
        // setAddress(addressParts.join(', '));
        return addressParts.join(',')
      }
    } catch (error) {
      console.log('Reverse geocode failed:', error);
    }
    return ""
  }
  const renderPolygonCard = ({ item }: { item: Polygon }) => {
    const region = getRegionFromCoordinates(item?.coordinates);
    // console.log(item?.geometry?.coordinates);
    // console.log(item)
    return (
      // <View style={styles.card}>
      //   <Text style={styles.cardTitle}>{item.name}</Text>
      //   <MapView
      //     style={styles.map}
      //     initialRegion={region}
      //     scrollEnabled={false}
      //     zoomEnabled={false}
      //     rotateEnabled={false}
      //     pitchEnabled={false}
      //     pointerEvents="none"
      //   >
      //     <RNPolygon
      //       coordinates={item.coordinates}
      //       strokeColor="rgba(0,0,255,0.8)"
      //       fillColor="rgba(0,0,255,0.2)"
      //       strokeWidth={2}
      //     />
      //   </MapView>
      //   <TouchableOpacity
      //     onPress={() => onSelect(item)}
      //     style={styles.selectButton}
      //   >
      //     <Text style={styles.selectButtonText}>Select</Text>
      //   </TouchableOpacity>
      // </View>
      <View style={styles.cardContainer}>
        <MapView
          style={styles.mapPreview}
          initialRegion={region}
          scrollEnabled={false}
          zoomEnabled={false}
          rotateEnabled={false}
          pitchEnabled={false}
          pointerEvents="none"
        >
          <RNPolygon
            coordinates={item.coordinates}
            strokeColor="rgba(0,0,255,0.8)"
            fillColor="rgba(0,0,255,0.2)"
            strokeWidth={2}
          />
        </MapView>

        <View style={styles.infoContainer}>
          <Text style={styles.fieldName}>{item.name}</Text>
          <Text style={styles.fieldMeta}>Area: {calculatePolygonArea(item?.coordinates)?.toFixed(2)} acres</Text>
          <Text style={styles.fieldAddress} numberOfLines={2}>
            {/* {returnAddress(item) || 'No address found'} */}
          </Text>

          <TouchableOpacity onPress={() => onSelect(item)} style={styles.selectButton}>
            <Text style={styles.selectButtonText}>Select</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };


  return (
    <Modal visible={visible} animationType="slide" transparent onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <FlatList
            data={polygons}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderPolygonCard}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        </View>
      </View>
    </Modal>
  );
};

// Helper: Get center of polygon for mini map
const getRegionFromCoordinates = (coordinates: { latitude: number; longitude: number }[]) => {
  // console.log(coordinates)

  const lats = coordinates.map((c) => c.latitude);
  const lngs = coordinates.map((c) => c.longitude);
  // console.log({ lats, lngs })
  const minLat = Math.min(...lats);
  const maxLat = Math.max(...lats);
  const minLng = Math.min(...lngs);
  const maxLng = Math.max(...lngs);

  return {
    latitude: (minLat + maxLat) / 2,
    longitude: (minLng + maxLng) / 2,
    latitudeDelta: Math.max(0.01, maxLat - minLat + 0.01),
    longitudeDelta: Math.max(0.01, maxLng - minLng + 0.01),
  };
};

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 12,
    elevation: 4,
    overflow: 'hidden',
  },
  mapPreview: {
    // width: 140,
    // height: 150,
    flex: 1,
  },
  infoContainer: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  fieldName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#222',
  },
  fieldMeta: {
    fontSize: 14,
    color: '#555',
    marginBottom: 4,
  },
  fieldAddress: {
    fontSize: 13,
    color: '#666',
    marginBottom: 10,
  },
  selectButton: {
    alignSelf: 'flex-start',
    backgroundColor: '#28A745', // Bootstrap Green
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  selectButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  modalContent: {
    height: Dimensions.get('window').height * 0.7,
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
  },
  card: {
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  map: {
    height: 200,
    borderRadius: 8,
    marginBottom: 10,
  },
  // selectButton: {
  //   backgroundColor: '#007AFF',
  //   padding: 10,
  //   borderRadius: 8,
  //   alignItems: 'center',
  // },
  // selectButtonText: {
  //   color: '#fff',
  //   fontWeight: '600',
  // },
});

export default PolygonModal;
