import React, { useState } from 'react';
import {
    Modal,
    View,
    Text,
    TouchableOpacity,
    FlatList,
    StyleSheet,
    Dimensions,
    TouchableWithoutFeedback,
} from 'react-native';
import { ChevronDown, Check } from 'lucide-react-native';
// import Icon from 'react-native-vector-icons/MaterialIcons';
import { MaterialIcons } from '@expo/vector-icons';
import AntDesign from '@expo/vector-icons/AntDesign';

import colors from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';


const SCREEN_HEIGHT = Dimensions.get('window').height;

type Option = { icon?: any, label: string; value: string | number };

type Props = {
    label: string;
    options: Option[];
    onSelect: (value: string | number | (string | number)[]) => void;
    selectedValue: string | number | null | undefined | (string | number)[];
    isMultiple?: boolean;
    disabled?: boolean;
    required?: boolean;
    error?: string;
    placeholder?: string;
};

const DropdownPicker: React.FC<Props> = ({
    label,
    options,
    onSelect,
    selectedValue,
    isMultiple = false,
    disabled,
    required = false,
    error,
    placeholder = 'Select an option...'
}) => {
    const [visible, setVisible] = useState(false);
    const { t, isRTL } = useTranslation()
    const handleSelect = (value: string | number) => {
        // console.log('handleSelect', value);
        if (isMultiple) {
            let newSelection = Array.isArray(selectedValue) ? [...selectedValue] : [];
            if (newSelection.includes(value)) {
                newSelection = newSelection.filter(v => v !== value);
            } else {
                newSelection.push(value);
            }
            onSelect(newSelection);
        } else {
            onSelect(value);
            setVisible(false);
        }
    };

    const isSelected = (value: string | number) => {
        return Array.isArray(selectedValue)
            ? selectedValue.includes(value)
            : selectedValue === value;
    };

    const renderItem = ({ item }: { item: Option }) => {
        const selected = isSelected(item.value);
        return (
            <View style={styles.container}>



                <TouchableOpacity
                    style={[styles.itemContainer, selected && styles.selectedItem, isRTL && styles.rtlDirection]}
                    disabled={disabled}
                    onPress={() => handleSelect(item.value)}

                >
                    {item?.icon ? <MaterialIcons name={item?.icon} size={22} color={selected ? 'green' : '#555'} /> : <MaterialIcons name="label-outline" size={22} color={selected ? 'green' : '#555'} />}
                     <Text style={[styles.labelText, selected && { color: 'green' }, isRTL && styles.rtlTextAlign]}>{item.label}</Text>
                    {selected && <AntDesign name="checkcircleo" size={24} color="green" />}
                </TouchableOpacity>

            </View>
        );
    };


    const getSelectedLabel = () => {
        if (isMultiple && Array.isArray(selectedValue)) {
            return options
                .filter(opt => selectedValue.includes(opt.value))
                .map(opt => opt.label)
                .join(', ') || placeholder;
        } else {
            return (
                (options || [])?.find(opt => opt.value === selectedValue)?.label || placeholder
            );
        }
    };

    return (
        <>
            {label && (
                <View style={[styles.labelContainer, isRTL && styles.rtlDirection]}>
                    <Text style={styles.label}>{label}</Text>
                    {required && <Text style={styles.required}>*</Text>}
                </View>
            )}
            <TouchableOpacity disabled={disabled} style={[styles.trigger, isRTL && styles.rtlDirection, disabled && styles.disabledItem, error && styles.triggerError]} onPress={() => setVisible(true)}>
                <Text style={[styles.triggerLabel, !selectedValue && styles.placeholderText]}>{getSelectedLabel()}</Text>
                <ChevronDown size={20} color="#555" />
            </TouchableOpacity>

            {error && (
                <Text style={[styles.errorText, isRTL && styles.rtlTextAlign]}>
                    {error}
                </Text>
            )}

            <Modal
                visible={visible}
                animationType="slide"
                transparent
                onRequestClose={() => setVisible(false)}
            >
                <TouchableWithoutFeedback onPress={() => setVisible(false)}>
                    <View style={styles.modalOverlay} />
                </TouchableWithoutFeedback>

                <View style={styles.bottomSheet}>
                    <View style={[styles.header, isRTL && styles.rtlDirection]}>
                        <Text style={styles.sheetTitle}>{label}</Text>
                        <TouchableOpacity onPress={() => setVisible(false)}>
                            <Text style={styles.closeText}>{t('common.close')}</Text>
                        </TouchableOpacity>
                    </View>
                    <FlatList
                        data={options}
                        keyExtractor={(item) => item.value.toString()}
                        renderItem={renderItem}
                        contentContainerStyle={{ paddingBottom: 30 }}
                    />
                </View>
            </Modal>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        marginBottom: 16,
    },
    rtlDirection: {
        flexDirection: 'row-reverse',
    },
    rtlTextAlign: {
        textAlign: 'right',
        marginRight:8
    },
    labelContainer: {
        flexDirection: 'row',
        marginBottom: 2,
    },
    // label: {
    //     fontSize: 14,
    //     fontWeight: '500',
    //     color: colors.gray[700],
    // },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: colors.gray[700],
        marginBottom: 8,
        textAlign: 'left',
    },
    required: {
        color: colors.danger,
        marginLeft: 4,
        fontSize: 14,
        fontWeight: '500',
    },
    trigger: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        // borderColor: '#aaa',
        borderColor: colors.gray[300],
        padding: 12,
        borderRadius: 8,
        justifyContent: 'space-between',
        marginBottom: 12,
    },
    triggerError: {
        borderColor: colors.danger,
    },
    // rtlDirection: {
    //     flexDirection: 'row-reverse',
    // },
    // rtlTextAlign: {
    //     textAlign: 'right',
    //     marginRight: 12
    // },
    triggerLabel: {
        fontSize: 16,
        color: '#333',
    },
    placeholderText: {
        color: colors.gray[400],
    },
    errorText: {
        color: colors.danger,
        fontSize: 12,
        marginTop: 4,
        marginBottom: 8,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: '#00000066',
    },
    bottomSheet: {
        position: 'absolute',
        bottom: 0,
        height: SCREEN_HEIGHT * 0.5,
        backgroundColor: '#fff',
        width: '100%',
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        zIndex: 9999,
        paddingTop: 16,
        paddingHorizontal: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
        paddingBottom: 8,
    },
    sheetTitle: {
        fontSize: 18,
        fontWeight: '600',
    },
    closeText: {
        color: 'red',
    },
    itemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f1f1f1',
    },
    labelText: {
        flex: 1,
        marginLeft: 12,
        fontSize: 16,
        color: '#333',
    },
    selectedItem: {
        backgroundColor: '#f0fff0',
    },
    disabledItem: {
        backgroundColor: '#f5f5f5',
        opacity: 0.6,
    },
});

export default DropdownPicker;
