import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image, 
  ScrollView, 
  KeyboardAvoidingView, 
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Stack, Link, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';

export default function ForgotPasswordScreen() {
  const { t } = useTranslation();
  const { resetPassword, isLoading, error, clearError } = useAuthStore();
  
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [resetSent, setResetSent] = useState(false);
  
  // Clear any errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);
  
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError(t('forgotPassword.emailRequired'));
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError(t('forgotPassword.invalidEmail'));
      return false;
    }
    setEmailError('');
    return true;
  };
  
  const handleResetPassword = async () => {
    clearError();
    
    const isEmailValid = validateEmail(email);
    
    if (!isEmailValid) {
      return;
    }
    
    try {
      await resetPassword(email);
      setResetSent(true);
    } catch (error) {
      console.error('Reset password error:', error);
    }
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Image
            source={require('@/assets/images/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        
        <View style={styles.formContainer}>
          {!resetSent ? (
            <>
              <Text style={styles.title}>{t('forgotPassword.forgotPassword')}</Text>
              <Text style={styles.subtitle}>{t('forgotPassword.enterEmail')}</Text>
              
              {error && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              )}
              
              <Input
                label={t('forgotPassword.email')}
                placeholder={t('forgotPassword.enterYourEmail')}
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  if (emailError) validateEmail(text);
                }}
                keyboardType="email-address"
                autoCapitalize="none"
                error={emailError}
                leftIcon={<Mail size={20} color={colors.gray[500]} />}
              />
              
              <Button
                title={isLoading ? t('forgotPassword.sending') : t('forgotPassword.resetPassword')}
                onPress={handleResetPassword}
                disabled={isLoading}
                leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
                style={styles.resetButton}
              />
              
              <Link href="/" asChild>
                <TouchableOpacity style={styles.signInContainer}>
                  <Text style={styles.signInText}>{t('forgotPassword.rememberPassword')}</Text>
                </TouchableOpacity>
              </Link>
            </>
          ) : (
            <View style={styles.successContainer}>
              <CheckCircle size={60} color={colors.success} style={styles.successIcon} />
              <Text style={styles.successTitle}>{t('forgotPassword.checkYourEmail')}</Text>
              <Text style={styles.successMessage}>{t('forgotPassword.resetLinkSent')}</Text>
              
              <Button
                title={t('forgotPassword.backToLogin')}
                onPress={() => router.replace('/')}
                style={styles.backButton}
                leftIcon={<ArrowLeft size={20} color={colors.white} />}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 40,
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 24,
  },
  errorContainer: {
    backgroundColor: colors.danger + '20',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: colors.danger,
  },
  errorText: {
    color: colors.danger,
    fontSize: 14,
  },
  resetButton: {
    marginTop: 16,
  },
  signInContainer: {
    alignItems: 'center',
    marginTop: 24,
  },
  signInText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  successIcon: {
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 16,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  backButton: {
    width: '100%',
  },
});