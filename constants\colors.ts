// Color palette for Kissan Dost app
export const colors = {
  primary: "#0D9F61", // Main green
  primaryLight: "#E7F7F0", // Light green background
  primaryDark: "#077A4B", // Dark green for buttons/accents
  secondary: "#F5A623", // Orange/yellow for warnings/alerts
  danger: "#E74C3C", // Red for errors/alerts
  success: "#27AE60", // Green for success states
  warning: "#F39C12", // Amber for warnings
  info: "#3498DB", // Blue for info
  white: "#FFFFFF",
  black: "#000000",
  gray: {
    50: "#F9FAFB",
    100: "#F3F4F6",
    200: "#E5E7EB",
    300: "#D1D5DB",
    400: "#9CA3AF",
    500: "#6B7280",
    600: "#4B5563",
    700: "#374151",
    800: "#1F2937",
    900: "#111827",
  },
  transparent: "transparent",
};

// Theme configuration
export const theme = {
  light: {
    text: colors.gray[800],
    textSecondary: colors.gray[600],
    background: colors.white,
    backgroundSecondary: colors.gray[50],
    card: colors.white,
    border: colors.gray[200],
    notification: colors.secondary,
    shadow: colors.gray[400],
    tint: colors.primary,
    tabIconDefault: colors.gray[400],
    tabIconSelected: colors.primary,
  },
};

export default {
  ...colors,
  ...theme,
};