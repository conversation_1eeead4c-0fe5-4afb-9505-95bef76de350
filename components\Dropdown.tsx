import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  Dimensions,
  TouchableWithoutFeedback,
  I18nManager,
  ViewStyle,
  TextStyle,
  Platform,
  Animated,
  Easing,
  TextInput, // Add this import
} from 'react-native';
import { ChevronDown, Check } from 'lucide-react-native';
import { colors } from '@/constants/colors';

export interface DropdownOption {
  label: string;
  value: string;
  icon?: React.ReactNode;
  color?: string;
}

interface DropdownProps {
  label?: string;
  placeholder?: string;
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  dropdownStyle?: ViewStyle;
  optionTextStyle?: TextStyle;
  error?: string;
  disabled?: boolean;
  isRTL?: boolean;
  renderOption?: (option: DropdownOption, isSelected: boolean) => React.ReactNode;
  renderSelectedOption?: (option: DropdownOption | undefined) => React.ReactNode;
  onBlur?: () => void;
  searchable?: boolean;
}

const Dropdown: React.FC<DropdownProps> = ({
  label,
  placeholder = 'Select an option',
  options,
  value,
  onChange,
  containerStyle,
  labelStyle,
  dropdownStyle,
  optionTextStyle,
  error,
  disabled = false,
  isRTL = I18nManager.isRTL,
  renderOption,
  renderSelectedOption,
  onBlur,
  searchable = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const dropdownRef = useRef<View>(null);
  const [dropdownLayout, setDropdownLayout] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const windowHeight = Dimensions.get('window').height;
  const windowWidth = Dimensions.get('window').width;
  
  // Animation values
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  
  // Find the selected option
  const selectedOption = options.find(option => option.value === value);
  
  // Filter options based on search text
  const filteredOptions = searchText 
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchText.toLowerCase()))
    : options;
  
  // Toggle dropdown
  const toggleDropdown = () => {
    if (disabled) return;
    
    if (Platform.OS === 'web') {
      setIsOpen(!isOpen);
      
      // Animate chevron rotation
      Animated.timing(rotateAnim, {
        toValue: isOpen ? 0 : 1,
        duration: 200,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
      
      // Animate dropdown menu
      if (!isOpen) {
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            easing: Easing.out(Easing.back(1.5)),
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
        ]).start();
      }
    } else {
      dropdownRef.current?.measureInWindow((x, y, width, height) => {
        setDropdownLayout({ x, y, width, height });
        setModalVisible(true);
        setSearchText('');
      });
    }
  };
  
  // Handle option selection
  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    if (Platform.OS === 'web') {
      setIsOpen(false);
      
      // Animate chevron rotation back
      Animated.timing(rotateAnim, {
        toValue: 0,
        duration: 200,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
      
      // Animate dropdown menu closing
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      setModalVisible(false);
    }
    
    if (onBlur) {
      onBlur();
    }
  };
  
  // Close dropdown when clicking outside (web only)
  useEffect(() => {
    if (Platform.OS !== 'web' || !isOpen) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !(dropdownRef.current as any).contains(event.target)) {
        setIsOpen(false);
        
        // Animate chevron rotation back
        Animated.timing(rotateAnim, {
          toValue: 0,
          duration: 200,
          easing: Easing.ease,
          useNativeDriver: true,
        }).start();
        
        // Animate dropdown menu closing
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
        ]).start();
        
        if (onBlur) {
          onBlur();
        }
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onBlur, rotateAnim, scaleAnim, opacityAnim]);
  
  // Calculate rotation for the chevron icon
  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });
  
  // Calculate transform origin for the dropdown menu
  const scaleInterpolate = scaleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.9, 1],
  });
  
  // Render option item
  const renderOptionItem = (option: DropdownOption, isSelected: boolean) => {
    if (renderOption) {
      return renderOption(option, isSelected);
    }
    
    return (
      <View style={[
        styles.optionItem,
        isRTL && styles.optionItemRTL
      ]}>
        {option.icon && (
          <View style={[
            styles.optionIcon,
            isRTL && styles.optionIconRTL
          ]}>
            {option.icon}
          </View>
        )}
        <Text style={[
          styles.optionText,
          optionTextStyle,
          isRTL && styles.optionTextRTL,
          option.color ? { color: option.color } : null,
          isSelected && styles.selectedOptionText
        ]}>
          {option.label}
        </Text>
        {isSelected && (
          <Check size={18} color={colors.primary} style={isRTL ? styles.checkIconRTL : styles.checkIcon} />
        )}
      </View>
    );
  };
  
  // Mobile dropdown modal
  const renderMobileDropdown = () => {
    if (!modalVisible) return null;
    
    // Calculate if dropdown should appear above or below the trigger
    const spaceBelow = windowHeight - dropdownLayout.y - dropdownLayout.height;
    const spaceAbove = dropdownLayout.y;
    const showBelow = spaceBelow >= Math.min(300, options.length * 50) || spaceBelow >= spaceAbove;
    
    // Calculate position and dimensions
    const maxHeight = showBelow 
      ? Math.min(300, spaceBelow - 20) 
      : Math.min(300, spaceAbove - 20);
    
    const top = showBelow 
      ? dropdownLayout.y + dropdownLayout.height 
      : undefined;
    
    const bottom = !showBelow 
      ? windowHeight - dropdownLayout.y 
      : undefined;
    
    return (
      <Modal
        transparent
        visible={modalVisible}
        animationType="fade"
        onRequestClose={() => {
          setModalVisible(false);
          if (onBlur) onBlur();
        }}
      >
        <TouchableWithoutFeedback onPress={() => {
          setModalVisible(false);
          if (onBlur) onBlur();
        }}>
          <View style={styles.modalOverlay}>
            <View style={[
              styles.modalContent,
              {
                maxHeight,
                width: Math.min(dropdownLayout.width, windowWidth - 40),
                top,
                bottom,
                left: Math.max(10, dropdownLayout.x),
              }
            ]}>
              {searchable && (
                <View style={styles.searchContainer}>
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search..."
                    value={searchText}
                    onChangeText={setSearchText}
                    autoFocus
                  />
                </View>
              )}
              
              <FlatList
                data={filteredOptions}
                keyExtractor={(item) => item.value}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.modalOption}
                    onPress={() => handleSelect(item.value)}
                    activeOpacity={0.7}
                  >
                    {renderOptionItem(item, item.value === value)}
                  </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={
                  <View style={styles.emptyList}>
                    <Text style={styles.emptyListText}>No options found</Text>
                  </View>
                }
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    );
  };
  
  // Web dropdown
  const renderWebDropdown = () => {
    if (!isOpen) return null;
    
    return (
      <Animated.View style={[
        styles.dropdownMenu,
        dropdownStyle,
        {
          opacity: opacityAnim,
          transform: [
            { scale: scaleInterpolate },
          ],
        }
      ]}>
        {searchable && (
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search..."
              value={searchText}
              onChangeText={setSearchText}
              autoFocus
            />
          </View>
        )}
        
        {filteredOptions.length > 0 ? (
          filteredOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={styles.option}
              onPress={() => handleSelect(option.value)}
              activeOpacity={0.7}
            >
              {renderOptionItem(option, option.value === value)}
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyList}>
            <Text style={styles.emptyListText}>No options found</Text>
          </View>
        )}
      </Animated.View>
    );
  };
  
  // Render custom selected option or default selected option
  const renderSelected = () => {
    if (renderSelectedOption && selectedOption) {
      return renderSelectedOption(selectedOption);
    }
    
    return (
      <View style={[
        styles.selectedContainer,
        isRTL && styles.selectedContainerRTL
      ]}>
        {selectedOption?.icon && (
          <View style={[
            styles.selectedIcon,
            isRTL && styles.selectedIconRTL
          ]}>
            {selectedOption.icon}
          </View>
        )}
        <Text style={[
          styles.selectedText,
          !selectedOption && styles.placeholderText,
          isRTL && styles.selectedTextRTL,
          selectedOption?.color ? { color: selectedOption.color } : null
        ]}>
          {selectedOption ? selectedOption.label : placeholder}
        </Text>
        <Animated.View style={{ transform: [{ rotate: rotateInterpolate }] }}>
          <ChevronDown size={20} color={colors.gray[500]} />
        </Animated.View>
      </View>
    );
  };
  
  return (
    <View style={[styles.container, containerStyle]}>
      {label ? (
        <Text style={[
          styles.label,
          labelStyle,
          isRTL && styles.labelRTL
        ]}>
          {label}
        </Text>
      ) : null}
      
      <View ref={dropdownRef} style={[
        styles.dropdownContainer,
        error ? styles.dropdownError : null,
        disabled ? styles.dropdownDisabled : null
      ]}>
        <TouchableOpacity
          activeOpacity={disabled ? 1 : 0.7}
          onPress={toggleDropdown}
          style={styles.dropdownTrigger}
        >
          {renderSelected()}
        </TouchableOpacity>
        
        {Platform.OS === 'web' ? renderWebDropdown() : renderMobileDropdown()}
      </View>
      
      {error ? (
        <Text style={[
          styles.errorText,
          isRTL && styles.errorTextRTL
        ]}>
          {error}
        </Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 6,
    textAlign: 'left',
  },
  labelRTL: {
    textAlign: 'right',
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    backgroundColor: colors.white,
    position: 'relative',
    zIndex: 1,
    overflow: 'visible',
  },
  dropdownError: {
    borderColor: colors.danger,
  },
  dropdownDisabled: {
    backgroundColor: colors.gray[100],
    opacity: 0.7,
  },
  dropdownTrigger: {
    width: '100%',
  },
  selectedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  selectedContainerRTL: {
    flexDirection: 'row-reverse',
  },
  selectedIcon: {
    marginRight: 8,
  },
  selectedIconRTL: {
    marginRight: 0,
    marginLeft: 8,
  },
  selectedText: {
    flex: 1,
    fontSize: 14,
    color: colors.gray[800],
    textAlign: 'left',
  },
  selectedTextRTL: {
    textAlign: 'right',
  },
  placeholderText: {
    color: colors.gray[400],
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    maxHeight: 300,
    overflow: 'hidden',
    zIndex: 10,
    elevation: 5,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginTop: 4,
    transformOrigin: 'top center',
  },
  option: {
    width: '100%',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  optionItemRTL: {
    flexDirection: 'row-reverse',
  },
  optionIcon: {
    marginRight: 8,
  },
  optionIconRTL: {
    marginRight: 0,
    marginLeft: 8,
  },
  optionText: {
    flex: 1,
    fontSize: 14,
    color: colors.gray[700],
    textAlign: 'left',
  },
  optionTextRTL: {
    textAlign: 'right',
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: '500',
  },
  checkIcon: {
    marginLeft: 8,
  },
  checkIconRTL: {
    marginLeft: 0,
    marginRight: 8,
  },
  errorText: {
    fontSize: 12,
    color: colors.danger,
    marginTop: 4,
    textAlign: 'left',
  },
  errorTextRTL: {
    textAlign: 'right',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    position: 'absolute',
    backgroundColor: colors.white,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  modalOption: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  searchContainer: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  searchInput: {
    height: 36,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 4,
    paddingHorizontal: 8,
    fontSize: 14,
  },
  emptyList: {
    padding: 16,
    alignItems: 'center',
  },
  emptyListText: {
    color: colors.gray[500],
    fontSize: 14,
  },
});

export default Dropdown;

