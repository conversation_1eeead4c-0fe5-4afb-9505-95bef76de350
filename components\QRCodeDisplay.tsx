import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Alert,
  Platform,
} from 'react-native';
import { colors } from '@/constants/colors';
import { Download, Share2 } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';
import { saveQRCode, shareQRCode } from '@/utils/qrcode';

// Import QRCode from react-native-qrcode-svg if available
let QRCode;
try {
  QRCode = require('react-native-qrcode-svg').default;
} catch (error) {
  // QRCode component not available
  console.log('QRCode component not available');
}

interface QRCodeDisplayProps {
  value: string;
  size?: number;
  itemType?: string;
  itemName?: string;
}

export default function QRCodeDisplay({
  value,
  size = 200,
  itemType = 'item',
  itemName = 'Item',
}: QRCodeDisplayProps) {
  const { t } = useTranslation();
  const qrRef = React.useRef();
  
  const handleSave = async () => {
    if (Platform.OS === 'web') {
      Alert.alert('Not available', 'Saving QR codes is not available on web');
      return;
    }
    
    const success = await saveQRCode(qrRef.current);
    
    if (success) {
      Alert.alert(t('common.success'), t('qrCode.saveSuccess'));
    } else {
      Alert.alert(t('common.error'), 'Failed to save QR code');
    }
  };
  
  const handleShare = async () => {
    if (Platform.OS === 'web') {
      Alert.alert('Not available', 'Sharing QR codes is not available on web');
      return;
    }
    
    const success = await shareQRCode(qrRef.current, itemType, itemName);
    
    if (!success) {
      Alert.alert(t('common.error'), 'Failed to share QR code');
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.qrContainer}>
        {QRCode ? (
          <QRCode
            value={value}
            size={size}
            color={colors.gray[800]}
            backgroundColor={colors.white}
            // @ts-ignore
            getRef={(ref) => (qrRef.current = ref)}
          />
        ) : (
          <View style={[styles.placeholderQR, { width: size, height: size }]}>
            <Text style={styles.placeholderText}>QR Code</Text>
            <Text style={styles.placeholderValue}>{value}</Text>
          </View>
        )}
      </View>
      
      <View style={styles.actions}>
        <TouchableOpacity style={styles.actionButton} onPress={handleSave}>
          <Download size={20} color={colors.primary} style={styles.actionIcon} />
          <Text style={styles.actionText}>{t('qrCode.save')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Share2 size={20} color={colors.primary} style={styles.actionIcon} />
          <Text style={styles.actionText}>{t('qrCode.share')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  qrContainer: {
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 16,
  },
  placeholderQR: {
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  placeholderText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[600],
    marginBottom: 8,
  },
  placeholderValue: {
    fontSize: 12,
    color: colors.gray[500],
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  actionIcon: {
    marginRight: 8,
  },
  actionText: {
    color: colors.primary,
    fontWeight: '500',
  },
});