import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getDatabase } from 'firebase/database';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDDKOb-qvTF2516cWV_9TEAgk5pukmozjc",
  authDomain: "kissandost-9570f.firebaseapp.com",
  databaseURL: "https://kissandost-9570f-default-rtdb.firebaseio.com",
  projectId: "kissandost-9570f",
  storageBucket: "kissandost-9570f.firebasestorage.app",
  messagingSenderId: "400828673471",
  appId: "1:400828673471:web:ef962bc0150b3bb0e3c50a",
  measurementId: "G-Z91G06JJVD"
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();
const auth = getAuth(app);
const firestore = getFirestore(app);
const storage = getStorage(app);
const database = getDatabase(app);

export { app, auth, firestore, storage, database };