import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  Alert,
  StyleSheet,
} from 'react-native';
import { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { Plus, ClipboardList, ChevronRight, X, Eye, Check, User } from 'lucide-react-native';
import { collection, query, where, orderBy, getDocs } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { useFarmStore } from '@/store/farm-store';
import { useTranslation } from '@/i18n/useTranslation';
import { colors } from '@/constants/colors';
import Toast from 'react-native-toast-message';

interface EntityChecklistManagerProps {
  entityType: string; // 'plant', 'animal', 'equipment', 'field', 'garden'
  entityId: string;
  farmId: string;
  onSelectChecklist: (checklist: any) => void;
  disabled?: boolean;
}

const EntityChecklistManager: React.FC<EntityChecklistManagerProps> = ({
  entityType,
  entityId,
  farmId,
  onSelectChecklist,
  disabled = false,
}) => {
  const { t, isRTL } = useTranslation();
  const [completedChecklists, setCompletedChecklists] = useState([]);
  const [availableChecklists, setAvailableChecklists] = useState([]);
  const [isLoadingCompleted, setIsLoadingCompleted] = useState(false);
  const [isLoadingAvailable, setIsLoadingAvailable] = useState(false);
  const [showAvailableModal, setShowAvailableModal] = useState(false);
  const [showChecklistDetailsModal, setShowChecklistDetailsModal] = useState(false);
  const [selectedChecklistForView, setSelectedChecklistForView] = useState(null);

  // Entity type mapping for Firestore collections
  const getEntityCollectionName = (type: string) => {
    const collectionMap = {
      plant: 'DailyPlantHealthCheck',
      animal: 'animalsChecklist',
      equipment: 'equipmentChecklist',
      field: 'fieldsChecklist',
      garden: 'gardensChecklist',
    };
    return collectionMap[type] || `${type}sChecklist`;
  };

  // Entity type mapping for checklist categories
  const getEntityCategoryId = (type: string) => {
    const categoryMap = {
      plant: 'EIkF12Ewx9idmbF69wCJ',
      animal: '3h9A6NeNdJNX2tm6vSY0',
      equipment: 'vldIZre7gYu64Y42UyvT',
      field: 'lxRKXdJgjL9qURotrMo8',
      garden: 'waUnV31wc9JXr1debFN4',
    };
    return categoryMap[type] || type;
  };

  // Load completed checklists for this entity
  const loadCompletedChecklists = async () => {
    setIsLoadingCompleted(true);
    try {
      const collectionName = getEntityCollectionName(entityType);
      const checklistsRef = collection(firestore, `farms/${farmId}/`, collectionName);
      const q = query(
        checklistsRef,
        // where('category', '==', 'plant'),
        where(`${entityType}Id`, '==', entityId),
        // where('farmId', '==', farmId),
        // orderBy('date', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const checklists = [];

      querySnapshot.forEach((doc) => {
        checklists.push({
          id: doc.id,
          ...doc.data(),
        });
      });
      // console.log(checklists, { collectionName });
      setCompletedChecklists(checklists);
    } catch (error) {
      console.error('Error loading completed checklists:', error);
      Toast.show({
        type: 'error',
        text1: t('common.error'),
        text2: t('entity.checklist.loadError'),
      });
    } finally {
      setIsLoadingCompleted(false);
    }
  };

  // Load available checklists for this entity type
  const loadAvailableChecklists = async () => {
    setIsLoadingAvailable(true);
    try {
      const categoryId = getEntityCategoryId(entityType);
      const checklistsRef = collection(firestore, 'checklists');
      const q = query(
        checklistsRef,
        where('category', '==', 'plant')
      );

      const querySnapshot = await getDocs(q);
      const checklists = [];

      querySnapshot.forEach((doc) => {
        checklists.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      setAvailableChecklists(checklists);
    } catch (error) {
      console.error('Error loading available checklists:', error);
      Toast.show({
        type: 'error',
        text1: t('common.error'),
        text2: t('entity.checklist.fetchError'),
      });
    } finally {
      setIsLoadingAvailable(false);
    }
  };

  useEffect(() => {
    if (entityId && farmId) {
      loadCompletedChecklists();
    }
  }, [entityId, farmId, entityType]);

  // Render completed checklist item
  const renderCompletedChecklistItem = ({ item }) => {
    const date = new Date(item.date);
    const isToday = date.toDateString() === new Date().toDateString();

    return (
      <View style={styles.checklistListItem}>
        {/* Header: Title + Meta (Date, Today Badge, Completion, User) */}
        <View style={[styles.checklistListItemHeader, isRTL && { flexDirection: 'row-reverse' }]}>
          <Text
            style={[
              styles.checklistListItemTitle,
              { flex: 1 },
              isRTL && { textAlign: 'right' }
            ]}
            numberOfLines={1}
          >
            {item.title || t('entity.checklist.defaultTitle')}
          </Text>

          <View style={[
            styles.checklistListItemMetaRow,
            { flexDirection: 'row', alignItems: 'center', gap: 8 },
            isRTL && { flexDirection: 'row-reverse' }
          ]} >
            {isToday && (
              <View style={styles.todayBadge}>
                <Text style={styles.todayBadgeText}>
                  {t('entity.checklist.todayBadge')}
                </Text>
              </View>
            )}

            <Text style={styles.checklistListItemDate}>
              {date.toLocaleDateString()}
            </Text>


          </View>

        </View>
        <View style={[styles.checklistListItemMeta,
        { flexDirection: 'row', alignItems: 'center', gap: 8 },
        isRTL && { flexDirection: 'row-reverse' }]}>
          {item.checklistItems && (
            <Text style={styles.checklistListItemStats}>
              {item?.checklistItems?.items?.filter(i => i.value).length} / {item?.checklistItems?.items.length} {t('entity.checklist.itemsCompleted')}
            </Text>
          )}

          {item.userName && (
            <View style={styles.checklistListItemUser}>
              <User size={14} color={colors.gray[500]} />
              <Text style={styles.checklistListItemUserText}>
                {item.userName}
              </Text>
            </View>
          )}
        </View>
        {/* Action */}
        <TouchableOpacity
          style={[styles.viewDetailsButton, { alignSelf: isRTL ? 'flex-start' : 'flex-end' }]}
          onPress={() => {
            setSelectedChecklistForView(item);
            setShowChecklistDetailsModal(true);
          }}
        >
          <Text style={styles.viewDetailsButtonText}>
            {t('entity.checklist.viewDetails')}
          </Text>
          <ChevronRight size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

    );
  };

  // Render available checklist item
  const renderAvailableChecklistItem = ({ item }) => (
    <View style={styles.availableChecklistItem}>
      <View style={styles.availableChecklistContent}>
        <ClipboardList size={24} color={colors.primary} />
        <View style={styles.availableChecklistTextContainer}>
          <Text style={[styles.availableChecklistTitle, isRTL && { textAlign: 'right' }]}>
            {item.title}
          </Text>
          {item.description && (
            <Text style={[styles.availableChecklistDescription, isRTL && { textAlign: 'right' }]}>
              {item.description}
            </Text>
          )}
          <Text style={[styles.availableChecklistItemCount, isRTL && { textAlign: 'right' }]}>
            {item.items?.length || 0} {t('entity.checklist.items')}
          </Text>
        </View>
      </View>

      <View style={[styles.availableChecklistActions, isRTL && { flexDirection: 'row-reverse' }]}>
        <TouchableOpacity
          style={styles.viewButton}
          onPress={() => {
            setSelectedChecklistForView(item);
            setShowChecklistDetailsModal(true);
          }}
        >
          <Eye size={16} color={colors.gray[600]} />
          <Text style={styles.viewButtonText}>{t('common.view')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.selectButton}
          onPress={() => {
            onSelectChecklist(item);
            setShowAvailableModal(false);
          }}
        >
          <Check size={16} color={colors.white} />
          <Text style={styles.selectButtonText}>{t('common.select')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render checklist details modal
  const renderChecklistDetailsModal = () => (
    <Modal
      visible={showChecklistDetailsModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowChecklistDetailsModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.detailsModalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {selectedChecklistForView?.title || t('entity.checklist.details')}
            </Text>
            <TouchableOpacity onPress={() => setShowChecklistDetailsModal(false)}>
              <X size={24} color={colors.gray[700]} />
            </TouchableOpacity>
          </View>

          {selectedChecklistForView && (
            <FlatList
              data={selectedChecklistForView.items || selectedChecklistForView.checklistItems?.items || []}
              keyExtractor={(item, index) => item.id || index.toString()}
              renderItem={({ item }) => (
                <View style={styles.checklistDetailItem}>
                  <View style={styles.checklistDetailItemHeader}>
                    <Text style={styles.checklistDetailItemTitle}>{item.label || item.title}</Text>
                    <Text style={styles.checklistDetailItemType}>({item.type})</Text>
                  </View>
                  {item?.value ? <View style={styles.checklistDetailItemHeader}>
                    <Text style={styles.checklistDetailItemTitle}>{item.value?.toString()}</Text>
                    {/* <Text style={styles.checklistDetailItemType}>({item.type})</Text> */}
                  </View> : null}
                  {item.completed !== undefined && (
                    <View style={[styles.completionStatus, item.completed && styles.completionStatusCompleted]}>
                      <Text style={[styles.completionStatusText, item.completed && styles.completionStatusTextCompleted]}>
                        {item.completed ? t('common.completed') : t('common.pending')}
                      </Text>
                    </View>
                  )}
                </View>
              )}
              contentContainerStyle={{ paddingBottom: 20 }}
            />
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Header with Add Button */}
      <View style={[styles.header, isRTL && { flexDirection: 'row-reverse' }]}>
        <Text style={[styles.headerTitle, isRTL && { textAlign: 'right' }]}>
          {t('entity.checklist.title')}
        </Text>
        <TouchableOpacity
          style={[styles.addButton, disabled && styles.disabledButton]}
          onPress={() => {
            loadAvailableChecklists();
            setShowAvailableModal(true);
          }}
          disabled={disabled}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>{t('entity.checklist.add')}</Text>
        </TouchableOpacity>
      </View>

      {/* Completed Checklists List */}
      {isLoadingCompleted ? (
        <ActivityIndicator size="large" color={colors.primary} style={{ marginVertical: 20 }} />
      ) : completedChecklists.length > 0 ? (
        <FlatList
          data={completedChecklists}
          keyExtractor={(item) => item.id}
          renderItem={renderCompletedChecklistItem}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyState}>
          <ClipboardList size={48} color={colors.gray[300]} />
          <Text style={styles.emptyStateTitle}>
            {t('entity.checklist.noChecklistsTitle')}
          </Text>
          <Text style={styles.emptyStateText}>
            {t('entity.checklist.noChecklistsDesc')}
          </Text>
        </View>
      )}

      {/* Available Checklists Modal */}
      <Modal
        visible={showAvailableModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAvailableModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.bottomModalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('entity.checklist.selectTitle')}</Text>
              <TouchableOpacity onPress={() => setShowAvailableModal(false)}>
                <X size={24} color={colors.gray[700]} />
              </TouchableOpacity>
            </View>

            {isLoadingAvailable ? (
              <ActivityIndicator size="large" color={colors.primary} style={{ marginVertical: 20 }} />
            ) : availableChecklists.length > 0 ? (
              <FlatList
                data={availableChecklists}
                keyExtractor={(item) => item.id}
                renderItem={renderAvailableChecklistItem}
                contentContainerStyle={{ paddingBottom: 20 }}
              />
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>
                  {t('entity.checklist.noAvailableChecklists')}
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Checklist Details Modal */}
      {renderChecklistDetailsModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black,
  },
  addButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  checklistListItemMetaRow: {
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
    rowGap: 4
  },
  checklistListItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  checklistListItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary
  },
  checklistListItemStats: {
    fontSize: 12,
    color: colors.gray[500]
  },
  checklistListItemDate: {
    fontSize: 12,
    color: colors.gray[500]
  },
  addButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  disabledButton: {
    backgroundColor: colors.gray[400],
  },
  listContainer: {
    paddingBottom: 20,
  },
  checklistListItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  // checklistListItemHeader: {
  //   marginBottom: 12,
  // },
  checklistListItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  todayBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  todayBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '500',
  },
  checklistListItemDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  checklistListItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  checklistListItemSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  checklistListItemStats: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListItemUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checklistListItemUserText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 4,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  viewDetailsButtonText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  bottomModalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    paddingTop: 20,
  },
  detailsModalContent: {
    backgroundColor: colors.white,
    borderRadius: 20,
    margin: 20,
    maxHeight: '80%',
    paddingTop: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  availableChecklistItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 12,
  },
  availableChecklistContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  availableChecklistTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  availableChecklistTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  availableChecklistDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  availableChecklistItemCount: {
    fontSize: 12,
    color: colors.gray[500],
  },
  availableChecklistActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: colors.gray[100],
    marginRight: 8,
  },
  viewButtonText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 4,
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: colors.primary,
  },
  selectButtonText: {
    fontSize: 14,
    color: colors.white,
    marginLeft: 4,
  },
  checklistDetailItem: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 20,
    marginBottom: 8,
  },
  checklistDetailItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  checklistDetailItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    flex: 1,
  },
  checklistDetailItemType: {
    fontSize: 12,
    color: colors.gray[500],
  },
  completionStatus: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    backgroundColor: colors.gray[200],
  },
  completionStatusCompleted: {
    backgroundColor: colors.success,
  },
  completionStatusText: {
    fontSize: 12,
    color: colors.gray[600],
  },
  completionStatusTextCompleted: {
    color: colors.white,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});

export default EntityChecklistManager;