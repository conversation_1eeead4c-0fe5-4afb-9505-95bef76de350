import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams, Stack } from 'expo-router';
import DateTimePicker from '@react-native-community/datetimepicker';
// import { format } from 'date-fns';
import { Droplets, ChevronLeft, Calendar, Save, List, Clipboard } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import { useTranslation } from '@/i18n/useTranslation';
import { collection, addDoc, query, where, getDocs, orderBy } from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { FlatList } from 'react-native-gesture-handler';
import Input from '@/components/Input';

// Define the MilkRecord type
interface MilkRecord {
  id?: string;
  animal_id: string;
  date: string;
  morning_liters: number;
  evening_liters: number;
  total: number;
  milker_name?: string;
  notes?: string;
  createdAt: Date;
}

export default function MilkRecordScreen() {
  const { id } = useLocalSearchParams();
  const { t, isRTL } = useTranslation();
  const { language, } = useAuthStore();
  const { getAnimal, currentFarm } = useFarmStore();

  const animal = getAnimal(id as string);

  // State for form fields
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [morningMilk, setMorningMilk] = useState('');
  const [eveningMilk, setEveningMilk] = useState('');
  const [milkerName, setMilkerName] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [records, setRecords] = useState<MilkRecord[]>([]);
  const [isLoadingRecords, setIsLoadingRecords] = useState(false);
  const [viewMode, setViewMode] = useState<'form' | 'history'>('form');

  // Calculate total milk
  const totalMilk =
    (parseFloat(morningMilk) || 0) +
    (parseFloat(eveningMilk) || 0);

  // Fetch previous records
  useEffect(() => {
    fetchMilkRecords();
  }, [id]);

  const fetchMilkRecords = async () => {
    if (!id || !currentFarm) return;

    setIsLoadingRecords(true);
    try {
      const milkRecordsRef = collection(firestore, 'milk_records');
      const q = query(
        milkRecordsRef,
        where('animal_id', '==', id),
        where('farmId', '==', currentFarm.id),
        orderBy('date', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const fetchedRecords: MilkRecord[] = [];

      querySnapshot.forEach((doc) => {
        fetchedRecords.push({ id: doc.id, ...doc.data() } as MilkRecord);
      });

      setRecords(fetchedRecords);
    } catch (error) {
      console.error('Error fetching milk records:', error);
      Alert.alert(t('common.error'), t('animal.milkRecords.fetchError'));
    } finally {
      setIsLoadingRecords(false);
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  const handleSubmit = async () => {
    if (!morningMilk && !eveningMilk) {
      Alert.alert(
        t('common.error'),
        t('animal.milkRecords.emptyFieldsError')
      );
      return;
    }

    if (!animal || !currentFarm) {
      Alert.alert(
        t('common.error'),
        t('animal.notFound')
      );
      return;
    }

    setIsLoading(true);
    try {
      const milkRecord: Omit<MilkRecord, 'id'> = {
        animal_id: animal.id,
        date: new Date(date).toISOString(),
        morning_liters: parseFloat(morningMilk) || 0,
        evening_liters: parseFloat(eveningMilk) || 0,
        total: totalMilk,
        milker_name: milkerName,
        notes: notes,
        createdAt: new Date(),
      };

      // Add farmId for querying
      const recordWithFarmId = {
        ...milkRecord,
        farmId: currentFarm.id,
      };

      // Save to Firestore
      await addDoc(collection(firestore, 'milk_records'), recordWithFarmId);

      // Reset form
      setMorningMilk('');
      setEveningMilk('');
      setMilkerName('');
      setNotes('');
      setDate(new Date());

      // Refresh records
      await fetchMilkRecords();

      Alert.alert(
        t('common.success'),
        t('animal.milkRecords.saveSuccess')
      );

      // Switch to history view after saving
      setViewMode('history');
    } catch (error) {
      console.error('Error saving milk record:', error);
      Alert.alert(
        t('common.error'),
        t('animal.milkRecords.saveError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderRecordItem = ({ item }: { item: MilkRecord }) => {
    return (
      <View style={styles.recordItem}>
        <View style={styles.recordHeader}>
          <Text style={[styles.recordDate, { textAlign: isRTL ? 'right' : 'left' }]}>
            {item.date}
          </Text>
          <Text style={styles.recordTotal}>
            {item.total.toFixed(1)} {t('animal.milkRecords.liters')}
          </Text>
        </View>

        <View style={styles.recordDetails}>
          <View style={styles.recordDetails}>
            <Text style={[styles.recordLabel, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('animal.milkRecords.morning')}:
            </Text>
            <Text style={styles.recordValue}>
              {item.morning_liters.toFixed(1)} {t('animal.milkRecords.liters')}
            </Text>
          </View>

          <View style={styles.recordDetails}>
            <Text style={[styles.recordLabel, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('animal.milkRecords.evening')}:
            </Text>
            <Text style={styles.recordValue}>
              {item.evening_liters.toFixed(1)} {t('animal.milkRecords.liters')}
            </Text>
          </View>

          {item.milker_name && (
            <View style={styles.recordDetails}>
              <Text style={[styles.recordLabel, { textAlign: isRTL ? 'right' : 'left' }]}>
                {t('animal.milkRecords.milker')}:
              </Text>
              <Text style={styles.recordValue}>{item.milker_name}</Text>
            </View>
          )}

          {item.notes && (
            <View style={styles.recordNotes}>
              <Text style={[styles.recordLabel, { textAlign: isRTL ? 'right' : 'left' }]}>
                {t('common.notes')}:
              </Text>
              <Text style={[styles.recordNotesText, { textAlign: isRTL ? 'right' : 'left' }]}>
                {item.notes}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />

      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: animal ?
            `${t('animal.milkRecords.title')} - ${animal.name || animal.species}` :
            t('animal.milkRecords.title'),
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <ChevronLeft size={24} color={colors.black} />
            </TouchableOpacity>
          ),
        }}
      />

      <View style={styles.tabButtons}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            viewMode === 'form' && styles.activeTabButton
          ]}
          onPress={() => setViewMode('form')}
        >
          <Save size={18} color={viewMode === 'form' ? colors.white : colors.primary} />
          <Text
            style={[
              styles.tabButtonText,
              viewMode === 'form' && styles.activeTabButtonText
            ]}
          >
            {t('animal.milkRecords.newRecord')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            viewMode === 'history' && styles.activeTabButton
          ]}
          onPress={() => setViewMode('history')}
        >
          <List size={18} color={viewMode === 'history' ? colors.white : colors.primary} />
          <Text
            style={[
              styles.tabButtonText,
              viewMode === 'history' && styles.activeTabButtonText
            ]}
          >
            {t('animal.milkRecords.history')}
          </Text>
        </TouchableOpacity>
      </View>

      {viewMode === 'form' ? (
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1 }}
        >
          <ScrollView style={styles.scrollView}>
            <View style={styles.formContainer}>
              {/* Date Picker */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('animal.milkRecords.date')}
                </Text>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Calendar size={20} color={colors.primary} />
                  <Text style={styles.dateText}>
                    {new Date(date)?.toISOString()}
                  </Text>
                </TouchableOpacity>
                {showDatePicker && (
                  <DateTimePicker
                    value={date}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                    maximumDate={new Date()}
                  />
                )}
              </View>

              {/* Morning Milk */}
              {/* <View style={styles.formGroup}>
                <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {} ()
                </Text>
                <View style={styles.inputWithIcon}> */}

              <Input
                label={`${t('animal.milkRecords.morningMilk')} (${t('animal.milkRecords.liters')})`}
                style={[styles.input, { textAlign: isRTL ? 'right' : 'left' }]}
                value={morningMilk}
                onChangeText={setMorningMilk}
                placeholder="0.0"
                keyboardType="numeric"
                placeholderTextColor={colors.gray[400]}
                leftIcon={<Droplets size={20} color={colors.primary} />}
              />
              {/* </View>
              </View> */}

              {/* Evening Milk */}
              {/* <View style={styles.formGroup}> */}
              {/* <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('animal.milkRecords.eveningMilk')} ({t('animal.milkRecords.liters')})
                </Text> */}
              {/* <View style={styles.inputWithIcon}>
                  <Droplets size={20} color={colors.primary} /> */}
              <Input
                label={`${t('animal.milkRecords.eveningMilk')} (${t('animal.milkRecords.liters')})`}// ({t('animal.milkRecords.liters')})}
                style={[styles.input, { textAlign: isRTL ? 'right' : 'left' }]}
                value={eveningMilk}
                onChangeText={setEveningMilk}
                placeholder="0.0"
                keyboardType="numeric"
                placeholderTextColor={colors.gray[400]}
                leftIcon={<Droplets size={20} color={colors.primary} />}
              />
              {/* // </View> */}
              {/* </View> */}

              {/* Total Milk (Calculated) */}
              <View style={styles.formGroup}>
                <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {t('animal.milkRecords.totalMilk')} ({t('animal.milkRecords.liters')})
                </Text>
                <View style={styles.totalContainer}>
                  <Text style={styles.totalValue}>
                    {totalMilk.toFixed(1)}
                  </Text>
                </View>
              </View>

              {/* Milker Name */}
              {/* <View style={styles.formGroup}>
                <Text style={[styles.label, ]}> */}

              {/* </Text> */}
              <Input
                label={`${t('animal.milkRecords.milker')} (${t('common.optional')})`}
                style={[styles.input, { textAlign: isRTL ? 'right' : 'left' }]}
                value={milkerName}
                onChangeText={setMilkerName}
                placeholder={t('animal.milkRecords.milkerPlaceholder')}
                placeholderTextColor={colors.gray[400]}
              />

              <Input
                label={`${t('common.notes')} (${t('common.optional')})`}
                style={[styles.textArea, { textAlign: isRTL ? 'right' : 'left' }]}
                value={notes}
                onChangeText={setNotes}
                
                placeholder={t('animal.milkRecords.notesPlaceholder')}
                placeholderTextColor={colors.gray[400]}
                multiline
                numberOfLines={4}
                containerStyle={styles.inputContainer}
                // textAlignVertical="top"
                 leftIcon={<Clipboard size={20} color={colors.gray[500]} />}

              />
              {/* </View> */}

              {/* Submit Button */}
              <Button
                title={t('common.save')}
                onPress={handleSubmit}
                disabled={isLoading}
                loading={isLoading}
                style={styles.submitButton}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      ) : (
        <View style={styles.historyContainer}>
          {isLoadingRecords ? (
            <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
          ) : records.length > 0 ? (
            <FlatList
              data={records || []}
              renderItem={renderRecordItem}
              keyExtractor={(item) => item.id || item.date}
              contentContainerStyle={styles.recordsList}
            />
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                {t('animal.milkRecords.noRecords')}
              </Text>
              <Button
                title={t('animal.milkRecords.addFirst')}
                onPress={() => setViewMode('form')}
                style={{ marginTop: 16 }}
              />
            </View>
          )}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  backButton: {
    padding: 8,
  },
  tabButtons: {
    flexDirection: 'row',
    padding: 8,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  activeTabButton: {
    backgroundColor: colors.primary,
  },
  tabButtonText: {
    marginLeft: 8,
    color: colors.primary,
    fontWeight: '500',
  },
  activeTabButtonText: {
    color: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[400],
    marginBottom: 8,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: colors.gray[200],
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: colors.gray[400],
    backgroundColor: colors.white,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[200],
    borderRadius: 8,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
  },
  textArea: {
    borderWidth: 1,
    borderColor: colors.gray[200],
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: colors.gray[400],
    backgroundColor: colors.white,
    minHeight: 120,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[200],
    borderRadius: 8,
    padding: 12,
    backgroundColor: colors.white,
  },
  dateText: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.gray[700],
  },
  totalContainer: {
    borderWidth: 1,
    borderColor: colors.gray[200],
    borderRadius: 8,
    padding: 12,
    backgroundColor: colors.gray[400],
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    textAlign: 'center',
  },
  submitButton: {
    marginTop: 16,
  },
  historyContainer: {
    flex: 1,
    padding: 16,
  },
  recordsList: {
    paddingBottom: 20,
  },
  recordItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    paddingBottom: 8,
  },
  recordDetails: {
    marginTop: 8,
  },
  recordDate: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[700],
  },
  recordTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    textAlign: 'right',
  },
  recordLabel: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 2,
  },
  recordValue: {
    fontSize: 15,
    color: colors.gray[700],
    marginBottom: 2,
  },
  recordNotes: {
    marginTop: 8,
    padding: 8,
    backgroundColor: colors.gray[400],
    borderRadius: 6,
  },
  recordNotesText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
  },
});
