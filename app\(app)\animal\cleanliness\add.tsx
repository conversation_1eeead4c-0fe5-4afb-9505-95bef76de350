import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Switch, Text,} from 'react-native';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
// import { Text, TextInput } from '@/components/Themed';
import Button from '@/components/Button';
import { useFarmStore } from '@/store/farm-store';
// import { useTranslation } from '@/i18n';
import Input from '@/components/Input';
import DatePicker from '@/components/DatePicker';
import { useAuthStore } from '@/store/auth-store';
import Toast from 'react-native-toast-message';
import { useTranslation } from '@/i18n/useTranslation';

export default function AddAnimalCleanlinessScreen() {
  const { t,isRTL } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { addAnimalCleanlinessRecord, getAnimal } = useFarmStore();
  const { language } = useAuthStore();

  const [bathGiven, setBathGiven] = useState(false);
  const [shelterCleaned, setShelterCleaned] = useState(false);
  const [groomingDone, setGroomingDone] = useState(false);
  const [date, setDate] = useState(new Date());
  const [remarks, setRemarks] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const animal = getAnimal(id);

  const handleSubmit = async () => {
    if (!bathGiven && !shelterCleaned && !groomingDone) {
      Alert.alert(t('error'), t('animal.cleanliness.errorNoActionSelected'));
      return;
    }

    try {
      setIsSubmitting(true);

      await addAnimalCleanlinessRecord(id, {
        bath_given: bathGiven,
        shelter_cleaned: shelterCleaned,
        grooming_done: groomingDone,
        date: date.toISOString(),
        remarks: remarks.trim() || undefined,
      });

      Toast.show({
        type: 'success',
        text1: t('success'),
        text2: t('animal.cleanliness.addSuccess'),
      });

      router.back();
    } catch (error) {
      console.error('Error adding cleanliness record:', error);
      Alert.alert(t('error'), t('animal.cleanliness.addError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Stack.Screen options={{ title: t('animal.cleanliness.addTitle') }} />

      <View style={styles.header}>
        <Text style={styles.title}>{t('animal.cleanliness.addCleanlinessFor', { name: animal?.name || id })}</Text>
      </View>

      <View style={styles.form}>
        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>{t('animal.cleanliness.bathGiven')}</Text>
          <Switch
            value={bathGiven}
            onValueChange={setBathGiven}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>{t('animal.cleanliness.shelterCleaned')}</Text>
          <Switch
            value={shelterCleaned}
            onValueChange={setShelterCleaned}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>{t('animal.cleanliness.groomingDone')}</Text>
          <Switch
            value={groomingDone}
            onValueChange={setGroomingDone}
          />
        </View>

        <Text style={styles.label}>{t('animal.cleanliness.date')}</Text>
        <DatePicker
          date={date}
          onDateChange={setDate}
          maximumDate={new Date()}
        />

        <Text style={styles.label}>{t('animal.cleanliness.remarks')}</Text>
        <Input
          style={[styles.input, styles.textArea]}
          value={remarks}
          onChangeText={setRemarks}
          placeholder={t('animal.cleanliness.enterRemarks')}
          multiline
          numberOfLines={4}
        />

        <Button
          title={isSubmitting ? t('submitting') : t('save')}
          onPress={handleSubmit}
          disabled={isSubmitting}
          style={styles.button}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  form: {
    gap: 12,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
  },
  input: {
    width: '100%',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
  },
  button: {
    marginTop: 16,
  },
});