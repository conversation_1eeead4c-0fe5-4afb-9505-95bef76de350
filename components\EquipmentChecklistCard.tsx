import { View, Text, StyleSheet, FlatList } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { normalizeDate } from '@/utils/dateUtils';
import { capitalizeFirstLetter } from '@/utils/util';
import React, { useState } from 'react';
import { useTranslation } from '@/i18n/useTranslation';
import colors from '@/constants/colors';
// import { format } from 'date-fns';

const EquipmentChecklistCard = ({ checklist }) => {
    const formattedDate = normalizeDate(checklist.date.toDate()); // example: Jun 26, 2025
    const { t, isRTL } = useTranslation()
    return (
        <View style={[styles.card, isRTL]}>
            <View style={[styles.headerRow, isRTL && { flexDirection: 'row-reverse' }]}>
                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <MaterialIcons name="person" size={16} color="#888" />
                    <Text style={[styles.userText, isRTL && { textAlign: 'right' }]}>{capitalizeFirstLetter(checklist.userName)}</Text>

                </View>
                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <MaterialIcons name="calendar-today" size={16} color="#888" style={styles.iconMargin} />
                    <Text style={[styles.dateText, isRTL && { textAlign: 'right' }]}>{formattedDate}</Text>
                </View>

            </View>
            <View style={{ width: '100%', borderWidth: .5, borderColor: colors.gray[200], marginBottom: 2 }}></View>
            <View style={[styles.iconContainer]}>
                {checklist.items.map((item, idx) => (

                    <View key={item.value} style={[styles.checkItem, isRTL && { flexDirection: 'row-reverse' }]}>
                        <MaterialIcons
                            name={item.completed ? "check-circle" : "cancel"}
                            size={16}
                            color={item.completed ? "green" : "gray"}
                        />
                        <Text style={[styles.itemText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t(`entity.equipment.checklistItems.${item.title}`)}</Text>
                    </View>
                ))}
            </View>
        </View>
    );
};
{/* <View key={item.title} style={[styles.itemBadge, isRTL && {alignSelf:'flex-end', flexDirection: 'row-reverse' }]}>
                       
                    </View> */}

// const capitalizeFirstLetter = (str: string) =>
//   str.charAt(0).toUpperCase() + str.slice(1);

const styles = StyleSheet.create({
    card: {
        backgroundColor: '#fff',
        marginVertical: 6,
        padding: 10,
        borderRadius: 10,
        elevation: 2,
    },
    checkItem: {
        flexDirection: 'row',
        alignItems: 'center',
        flexBasis: '45%', // Adjust to control how many per row
        marginBottom: 4,
    },
    iconContainer: {
        marginTop: 2,
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        rowGap: 4, // Optional: adds space between rows
        columnGap: 12,
    },
    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 6,
    },
    userText: {
        marginLeft: 4,
        marginRight: 12,
        fontSize: 14,
        color: '#333',
        fontWeight: '600',
    },
    dateText: {
        marginLeft: 4,
        fontSize: 13,
        color: '#666',
    },
    iconMargin: {
        marginLeft: 8,
    },
    itemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,

    },
    itemBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f4f4f4',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 6,
        marginRight: 6,
        marginBottom: 6,
    },
    itemText: {
        fontSize: 12,
        marginLeft: 4,
        color: '#333',
    },
});
export default EquipmentChecklistCard