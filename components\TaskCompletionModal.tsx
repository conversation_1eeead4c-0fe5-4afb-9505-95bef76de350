import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Image,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { ChecklistItem } from '@/types';
import { X, Check, Camera, Upload, AlertTriangle } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import Button from '@/components/Button';
import { uploadImageAsync } from '@/utils/firebase-storage';

interface TaskCompletionModalProps {
  visible: boolean;
  onClose: () => void;
  onComplete: (data: {
    checklist: ChecklistItem[];
    images: string[];
    notes: string;
  }) => Promise<void>;
  checklist: ChecklistItem[];
  evidenceRequired: boolean;
}

export default function TaskCompletionModal({
  visible,
  onClose,
  onComplete,
  checklist: initialChecklist,
  evidenceRequired
}: TaskCompletionModalProps) {
  const { t } = useTranslation();
  const [checklist, setChecklist] = useState<ChecklistItem[]>([]);
  const [images, setImages] = useState<string[]>([]);
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);

  useEffect(() => {
    if (visible) {
      // Reset state when modal opens
      setChecklist(initialChecklist || []);
      setImages([]);
      setNotes('');
    }
  }, [visible, initialChecklist]);

  const toggleChecklistItem = (id: string) => {
    setChecklist(checklist.map(item => 
      item.id === id ? { ...item, completed: !item.completed } : item
    ));
  };

  const handleAddImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setUploadingImage(true);
        // Upload image to Firebase Storage
        const uploadedUrl = await uploadImageAsync(result.assets[0].uri, 'task-evidence');
        setImages([...images, uploadedUrl]);
        setUploadingImage(false);
      }
    } catch (error) {
      console.error('Error picking or uploading image:', error);
      Alert.alert(t('common.error'), t('task.imageUploadFailed'));
      setUploadingImage(false);
    }
  };

  const handleTakePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(t('common.error'), t('permissions.cameraRequired'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setUploadingImage(true);
        // Upload image to Firebase Storage
        const uploadedUrl = await uploadImageAsync(result.assets[0].uri, 'task-evidence');
        setImages([...images, uploadedUrl]);
        setUploadingImage(false);
      }
    } catch (error) {
      console.error('Error taking or uploading photo:', error);
      Alert.alert(t('common.error'), t('task.imageUploadFailed'));
      setUploadingImage(false);
    }
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    // Check if all required items are completed
    const incompleteRequired = checklist.filter(item => item.required && !item.completed);
    
    if (incompleteRequired.length > 0) {
      Alert.alert(
        t('task.incompleteRequiredItems'),
        t('task.pleaseCompleteRequiredItems'),
        [{ text: t('common.ok') }]
      );
      return;
    }

    // Check if evidence is required but not provided
    if (evidenceRequired && images.length === 0) {
      Alert.alert(
        t('task.evidenceRequired'),
        t('task.pleaseAddEvidence'),
        [{ text: t('common.ok') }]
      );
      return;
    }

    try {
      setIsLoading(true);
      await onComplete({
        checklist,
        images,
        notes
      });
      setIsLoading(false);
      onClose();
    } catch (error) {
      console.error('Error completing task:', error);
      setIsLoading(false);
      Alert.alert(t('common.error'), t('task.completionFailed'));
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.centeredView}
      >
        <View style={styles.modalView}>
          <View style={styles.header}>
            <Text style={styles.title}>{t('task.completeTask')}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* Checklist Section */}
            {checklist.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>{t('task.checklist')}</Text>
                
                {checklist.map((item) => (
                  <View key={item.id} style={styles.checklistItem}>
                    <TouchableOpacity 
                      style={[
                        styles.checkbox, 
                        item.completed && styles.checkboxChecked
                      ]}
                      onPress={() => toggleChecklistItem(item.id)}
                    >
                      {item.completed && <Check size={16} color={colors.white} />}
                    </TouchableOpacity>
                    
                    <View style={styles.checklistItemContent}>
                      <Text style={styles.checklistItemTitle}>{item.title}</Text>
                      {item.description && (
                        <Text style={styles.checklistItemDescription}>{item.description}</Text>
                      )}
                      {item.required && (
                        <View style={styles.requiredBadge}>
                          <Text style={styles.requiredText}>{t('task.required')}</Text>
                        </View>
                      )}
                    </View>
                  </View>
                ))}
              </View>
            )}

            {/* Evidence Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>{t('task.evidence')}</Text>
                {evidenceRequired && (
                  <View style={styles.requiredBadge}>
                    <Text style={styles.requiredText}>{t('task.required')}</Text>
                  </View>
                )}
              </View>

              <View style={styles.imageButtons}>
                <TouchableOpacity 
                  style={styles.imageButton}
                  onPress={handleTakePhoto}
                  disabled={uploadingImage}
                >
                  <Camera size={20} color={colors.primary} />
                  <Text style={styles.imageButtonText}>{t('task.takePhoto')}</Text>
                </TouchableOpacity>

                <TouchableOpacity 
                  style={styles.imageButton}
                  onPress={handleAddImage}
                  disabled={uploadingImage}
                >
                  <Upload size={20} color={colors.primary} />
                  <Text style={styles.imageButtonText}>{t('task.uploadImage')}</Text>
                </TouchableOpacity>
              </View>

              {uploadingImage && (
                <View style={styles.uploadingContainer}>
                  <ActivityIndicator size="small" color={colors.primary} />
                  <Text style={styles.uploadingText}>{t('task.uploadingImage')}</Text>
                </View>
              )}

              {images.length > 0 && (
                <View style={styles.imagesContainer}>
                  {images.map((image, index) => (
                    <View key={index} style={styles.imageContainer}>
                      <Image source={{ uri: image }} style={styles.image} />
                      <TouchableOpacity 
                        style={styles.removeImageButton}
                        onPress={() => removeImage(index)}
                      >
                        <X size={16} color={colors.white} />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}

              {evidenceRequired && images.length === 0 && (
                <View style={styles.warningContainer}>
                  <AlertTriangle size={16} color={colors.warning} />
                  <Text style={styles.warningText}>{t('task.evidenceRequiredWarning')}</Text>
                </View>
              )}
            </View>

            {/* Notes Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>{t('task.notes')}</Text>
              <TextInput
                style={styles.notesInput}
                value={notes}
                onChangeText={setNotes}
                placeholder={t('task.notesPlaceholder')}
                multiline
                textAlignVertical="top"
              />
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <Button
              title={t('task.markAsCompleted')}
              onPress={handleSubmit}
              loading={isLoading}
              style={styles.submitButton}
              leftIcon={<Check size={20} color={colors.white} />}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
    maxHeight: '70%',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  checklistItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.gray[400],
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  checklistItemContent: {
    flex: 1,
  },
  checklistItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 4,
  },
  checklistItemDescription: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  requiredBadge: {
    backgroundColor: colors.warning + '30',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  requiredText: {
    fontSize: 10,
    color: colors.warning,
    fontWeight: '500',
  },
  imageButtons: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  imageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginRight: 12,
    flex: 1,
  },
  imageButtonText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 8,
  },
  uploadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  uploadingText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 8,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageContainer: {
    width: '30%',
    aspectRatio: 1,
    margin: '1.5%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.danger,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warning + '20',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  warningText: {
    fontSize: 14,
    color: colors.warning,
    marginLeft: 8,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    height: 100,
    fontSize: 14,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  submitButton: {
    backgroundColor: colors.success,
  },
});