// services/analyticsService.ts
import { firestore } from "@/firebase/config";
import { collection, addDoc, serverTimestamp } from "firebase/firestore";
// import { firestore } from "./firebase";

/**
 * Logs an analytics event to Firestore
 * @param uid - Firebase Auth user ID
 * @param type - Event type (e.g., "login", "submit_task", etc.)
 * @param message - Human-readable description
 * @param extra - Optional additional data
 */
export const logAnalyticsEvent = async (
  uid: string,
  type: string,
  message: string,
  extra: Record<string, any> = {}
) => {
  try {
    await addDoc(collection(firestore, "analytics"), {
      uid,
      type,
      message,
      timestamp: serverTimestamp(),
      ...extra, // Add any additional context
    });
  } catch (error) {
    console.error("Failed to log analytics event:", error);
  }
};
