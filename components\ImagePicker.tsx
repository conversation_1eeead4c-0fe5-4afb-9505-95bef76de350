// import React, { useState } from 'react';
// import { 
//   View, 
//   Text, 
//   StyleSheet, 
//   TouchableOpacity, 
//   Image, 
//   ActivityIndicator,
//   Alert,
//   Platform,
// } from 'react-native';
// import * as ImagePickerExpo from 'expo-image-picker';
// import { colors } from '@/constants/colors';
// import { Camera, Image as ImageIcon, Scan, X } from 'lucide-react-native';
// import { analyzeImage } from '@/utils/openai-vision';

// interface ImagePickerProps {
//   image: string;
//   onImageSelected: (uri: string) => void;
//   placeholder?: string;
//   size?: number;
//   shape?: 'circle' | 'square';
//   editable?: boolean;
//   showAnalyzeButton?: boolean;
// }

// const ImagePicker: React.FC<ImagePickerProps> = ({
//   image,
//   onImageSelected,
//   placeholder = 'Add Image',
//   size = 150,
//   shape = 'square',
//   editable = true,
//   showAnalyzeButton = true,
// }) => {
//   const [loading, setLoading] = useState(false);
//   const [analyzing, setAnalyzing] = useState(false);
//   const [showOptions, setShowOptions] = useState(false);

//   const pickImage = async () => {
//     setShowOptions(false);

//     try {
//       setLoading(true);

//       // Request permission
//       const { status } = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();

//       if (status !== 'granted') {
//         Alert.alert('Permission Denied', 'We need camera roll permission to upload images');
//         setLoading(false);
//         return;
//       }

//       // Launch image library
//       const result = await ImagePickerExpo.launchImageLibraryAsync({
//         mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
//         allowsEditing: true,
//         aspect: [1, 1],
//         quality: 0.8,
//       });

//       if (!result.canceled && result.assets && result.assets.length > 0) {
//         onImageSelected(result.assets[0].uri);
//       }
//     } catch (error) {
//       console.error('Error picking image:', error);
//       Alert.alert('Error', 'Failed to pick image');
//     } finally {
//       setLoading(false);
//     }
//   };

//   const takePhoto = async () => {
//     setShowOptions(false);

//     try {
//       setLoading(true);

//       // Request permission
//       const { status } = await ImagePickerExpo.requestCameraPermissionsAsync();

//       if (status !== 'granted') {
//         Alert.alert('Permission Denied', 'We need camera permission to take photos');
//         setLoading(false);
//         return;
//       }

//       // Launch camera
//       const result = await ImagePickerExpo.launchCameraAsync({
//         allowsEditing: true,
//         aspect: [1, 1],
//         quality: 0.8,
//       });

//       if (!result.canceled && result.assets && result.assets.length > 0) {
//         onImageSelected(result.assets[0].uri);
//       }
//     } catch (error) {
//       console.error('Error taking photo:', error);
//       Alert.alert('Error', 'Failed to take photo');
//     } finally {
//       setLoading(false);
//     }
//   };

//   const analyzePhoto = async () => {
//     if (!image) {
//       Alert.alert('No Image', 'Please select an image first');
//       return;
//     }

//     try {
//       setAnalyzing(true);

//       const analysis = await analyzeImage(image);

//       Alert.alert(
//         'Image Analysis',
//         analysis,
//         [{ text: 'OK' }]
//       );
//     } catch (error) {
//       console.error('Error analyzing image:', error);
//       Alert.alert('Error', 'Failed to analyze image');
//     } finally {
//       setAnalyzing(false);
//     }
//   };

//   const clearImage = () => {
//     onImageSelected('');
//   };

//   const containerStyle = {
//     width: size,
//     height: size,
//     borderRadius: shape === 'circle' ? size / 2 : size / 10,
//   };

//   return (
//     <View style={[styles.container, { width: size }]}>
//       {image ? (
//         <View style={[styles.imageContainer, containerStyle]}>
//           <Image 
//             source={{ uri: image }} 
//             style={[
//               styles.image, 
//               { borderRadius: shape === 'circle' ? size / 2 : size / 10 }
//             ]} 
//           />

//           {editable && (
//             <TouchableOpacity 
//               style={styles.clearButton}
//               onPress={clearImage}
//             >
//               <X size={16} color={colors.white} />
//             </TouchableOpacity>
//           )}

//           {showAnalyzeButton && (
//             <TouchableOpacity 
//               style={styles.analyzeButton}
//               onPress={analyzePhoto}
//               disabled={analyzing}
//             >
//               {analyzing ? (
//                 <ActivityIndicator size="small" color={colors.white} />
//               ) : (
//                 <>
//                   <Scan size={16} color={colors.white} />
//                   <Text style={styles.analyzeText}>Analyze</Text>
//                 </>
//               )}
//             </TouchableOpacity>
//           )}
//         </View>
//       ) : (
//         <TouchableOpacity 
//           style={[
//             styles.placeholderContainer, 
//             containerStyle,
//             !editable && styles.disabledContainer
//           ]}
//           onPress={() => editable && setShowOptions(true)}
//           disabled={!editable || loading}
//         >
//           {loading ? (
//             <ActivityIndicator size="small" color={colors.primary} />
//           ) : (
//             <>
//               <ImageIcon size={24} color={colors.gray[400]} />
//               <Text style={styles.placeholderText}>{placeholder}</Text>
//             </>
//           )}
//         </TouchableOpacity>
//       )}

//       {showOptions && (
//         <View style={styles.optionsContainer}>
//           <TouchableOpacity 
//             style={styles.optionButton}
//             onPress={takePhoto}
//           >
//             <Camera size={20} color={colors.white} />
//             <Text style={styles.optionText}>Camera</Text>
//           </TouchableOpacity>

//           <TouchableOpacity 
//             style={styles.optionButton}
//             onPress={pickImage}
//           >
//             <ImageIcon size={20} color={colors.white} />
//             <Text style={styles.optionText}>Gallery</Text>
//           </TouchableOpacity>

//           <TouchableOpacity 
//             style={[styles.optionButton, styles.cancelButton]}
//             onPress={() => setShowOptions(false)}
//           >
//             <X size={20} color={colors.white} />
//             <Text style={styles.optionText}>Cancel</Text>
//           </TouchableOpacity>
//         </View>
//       )}
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     alignItems: 'center',
//     position: 'relative',
//   },
//   imageContainer: {
//     overflow: 'hidden',
//     position: 'relative',
//   },
//   image: {
//     width: '100%',
//     height: '100%',
//   },
//   placeholderContainer: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderStyle: 'dashed',
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: colors.gray[50],
//   },
//   disabledContainer: {
//     opacity: 0.5,
//   },
//   placeholderText: {
//     fontSize: 14,
//     color: colors.gray[500],
//     marginTop: 8,
//     textAlign: 'center',
//   },
//   clearButton: {
//     position: 'absolute',
//     top: 8,
//     right: 8,
//     backgroundColor: 'rgba(0, 0, 0, 0.6)',
//     width: 24,
//     height: 24,
//     borderRadius: 12,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   analyzeButton: {
//     position: 'absolute',
//     bottom: 8,
//     right: 8,
//     backgroundColor: 'rgba(0, 0, 0, 0.6)',
//     paddingHorizontal: 10,
//     paddingVertical: 6,
//     borderRadius: 16,
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   analyzeText: {
//     color: colors.white,
//     fontSize: 12,
//     fontWeight: '500',
//     marginLeft: 4,
//   },
//   optionsContainer: {
//     position: 'absolute',
//     bottom: -120,
//     left: 0,
//     right: 0,
//     backgroundColor: colors.gray[800],
//     borderRadius: 8,
//     padding: 8,
//     zIndex: 10,
//     shadowColor: colors.gray[900],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.2,
//     shadowRadius: 4,
//     elevation: 4,
//   },
//   optionButton: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingVertical: 8,
//     paddingHorizontal: 12,
//     borderRadius: 4,
//   },
//   optionText: {
//     fontSize: 14,
//     color: colors.white,
//     marginLeft: 8,
//   },
//   cancelButton: {
//     borderTopWidth: 1,
//     borderTopColor: 'rgba(255, 255, 255, 0.2)',
//     marginTop: 4,
//     paddingTop: 8,
//   },
// });

// export default ImagePicker;

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import * as ImagePickerExpo from 'expo-image-picker';
import { colors } from '@/constants/colors';
import { Camera, Image as ImageIcon, Scan, X } from 'lucide-react-native';
import { analyzeImage } from '@/utils/openai-vision';

interface ImagePickerProps {
  image: string;
  onImageSelected: (uri: string) => void;
  placeholder?: string;
  size?: number;
  shape?: 'circle' | 'square';
  editable?: boolean;
  showAnalyzeButton?: boolean;
  isPreview?: boolean;
  onAnalyzeImage?: any;
  onAnalysisComplete?: any;
  type?: string
}

const ImagePicker: React.FC<ImagePickerProps> = ({
  image,
  onImageSelected,
  placeholder = 'Add Image',
  size = 120,
  shape = 'square',
  editable = true,
  showAnalyzeButton = true,
  isPreview = true,
  onAnalyzeImage,
  onAnalysisComplete,
  type
}) => {
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const IMAGE_HEIGHT = 140; // matches 3 buttons + 2 gaps
  useEffect(() => {
    (async () => {
      await ImagePickerExpo.requestCameraPermissionsAsync();
    })();
  }, []);
  const pickImage = async () => {
    try {

      // First, check for existing permissions
      let permissionResult = await ImagePickerExpo.getMediaLibraryPermissionsAsync();

      if (permissionResult.status !== 'granted') {
        // If not granted, request them
        permissionResult = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
      }

      if (permissionResult.status !== 'granted') {
        Alert.alert('Permission Denied', 'We need permission to access your gallery. Please enable it in your device settings.');
        return;
      }
      setLoading(true);
      const result = await ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        // allowsEditing: true,
        // aspect: [16, 20],
        quality: 0.8,
      });

      if (!result.canceled && result.assets?.length > 0) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Image pick error:', error);
      Alert.alert('Error', 'Unable to pick image.');
    } finally {
      setLoading(false);
    }
  };

  const takePhoto = async () => {
    try {
      setLoading(true);
      // First, check for existing permissions
      let permissionResult = await ImagePickerExpo.getCameraPermissionsAsync();

      if (permissionResult.status !== 'granted') {
        // If not granted, request them
        permissionResult = await ImagePickerExpo.requestCameraPermissionsAsync();
      }

      if (permissionResult.status !== 'granted') {
        Alert.alert('Permission Denied', 'We need permission to access your camera. Please enable it in your device settings.');
        return;
      }

      const result = await ImagePickerExpo.launchCameraAsync({
        allowsEditing: true,
        // aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets?.length > 0) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Camera error:', error);
      Alert.alert('Error', 'Unable to take photo.');
    } finally {
      setLoading(false);
    }
  };
  // const pickImage = async (useCamera = false) => {
  //   try {
  //     setLoading(true);

  //     let permissionResult;

  //     if (useCamera) {
  //       // Request camera permissions
  //       permissionResult = await ImagePickerExpo.getCameraPermissionsAsync();

  //       if (permissionResult.status !== 'granted') {
  //         permissionResult = await ImagePickerExpo.requestCameraPermissionsAsync();
  //       }

  //       if (permissionResult.status !== 'granted') {
  //         Alert.alert('Permission Denied', 'We need access to your camera. Please enable it in your device settings.');
  //         return;
  //       }
  //     } else {
  //       // Request media library permissions
  //       permissionResult = await ImagePickerExpo.getMediaLibraryPermissionsAsync();

  //       if (permissionResult.status !== 'granted') {
  //         permissionResult = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
  //       }

  //       if (permissionResult.status !== 'granted') {
  //         Alert.alert('Permission Denied', 'We need permission to access your gallery.');
  //         return;
  //       }
  //     }

  //     const result = useCamera
  //       ? await ImagePickerExpo.launchCameraAsync({
  //           allowsEditing: true,
  //           aspect: [16, 20],
  //           quality: 0.8,
  //         })
  //       : await ImagePickerExpo.launchImageLibraryAsync({
  //           mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
  //           allowsEditing: true,
  //           aspect: [16, 20],
  //           quality: 0.8,
  //         });

  //     if (!result.canceled && result.assets?.length > 0) {
  //       onImageSelected(result.assets[0].uri);
  //     }

  //   } catch (error) {
  //     console.error('Image pick error:', error);
  //     Alert.alert('Error', 'Unable to pick image.');
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // const analyzePhoto = async () => {
  //   if (!image) {
  //     Alert.alert('No Image', 'Please select an image first');
  //     return;
  //   }

  //   try {
  //     setAnalyzing(true);
  //     const result = await onAnalyzeImage(image);
  //     onAnalysisComplete(result)
  //     Alert.alert('Image Analysis', result.analysis);
  //   } catch (error) {
  //     console.error('Analyze error:', error);
  //     Alert.alert('Error', 'Unable to analyze image');
  //   } finally {
  //     setAnalyzing(false);
  //   }
  // };
  const analyzePhoto = async () => {
    if (!image) {
      Alert.alert('No Image', 'Please select an image first');
      return;
    }

    try {
      setAnalyzing(true);
      const result = await onAnalyzeImage(image, type); // Pass the appropriate category
      onAnalysisComplete(result);

      // Create a more readable summary of the analysis
      let summaryText = result.analysis;

      if (result.details) {
        const details = result.details;
        const detailsArray = [];

        // Format details based on what's available
        if (details.species) detailsArray.push(`Species: ${details.species}`);
        if (details.breed) detailsArray.push(`Breed: ${details.breed}`);
        if (details.gender) detailsArray.push(`Gender: ${details.gender}`);
        if (details.ageEstimate) detailsArray.push(`Age: ${details.ageEstimate}`);
        if (details.healthStatus) detailsArray.push(`Health: ${details.healthStatus}`);
        if (details.variety) detailsArray.push(`Variety: ${details.variety}`);
        if (details.growthStage) detailsArray.push(`Growth Stage: ${details.growthStage}`);
        if (details.issues) detailsArray.push(`Issues: ${details.issues}`);

        if (detailsArray.length > 0) {
          summaryText += '\n\n' + detailsArray.join('\n');
        }
      }

      Alert.alert('Image Analysis', summaryText);
    } catch (error) {
      console.error('Analyze error:', error);
      Alert.alert('Error', 'Unable to analyze image');
    } finally {
      setAnalyzing(false);
    }
  };
  const clearImage = () => {
    onImageSelected('');
  };

  const containerStyle = {
    width: IMAGE_HEIGHT,
    height: IMAGE_HEIGHT,
    borderRadius: shape === 'circle' ? IMAGE_HEIGHT / 2 : 16,
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.row}>
        {editable && (
          <View style={[styles.buttonColumn, { flexDirection: isPreview ? 'column' : 'row' }]}>
            <TouchableOpacity style={styles.actionButton} onPress={takePhoto}>
              <Camera size={18} color={colors.white} />
              <Text style={styles.buttonText}>Take Photo</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={pickImage}>
              <ImageIcon size={18} color={colors.white} />
              <Text style={styles.buttonText}>Upload</Text>
            </TouchableOpacity>

            {showAnalyzeButton && image && (
              <TouchableOpacity
                style={styles.analyzeButton}
                onPress={analyzePhoto}
                disabled={analyzing}
              >
                {analyzing ? (
                  <ActivityIndicator size="small" color={colors.white} />
                ) : (
                  <>
                    <Scan size={16} color={colors.white} />
                    <Text style={styles.analyzeText}>Analyze</Text>
                  </>
                )}
              </TouchableOpacity>
            )}
          </View>

        )}
        {isPreview &&
          <View style={[styles.imageWrapper, containerStyle]}>
            {loading ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : image ? (
              <View style={styles.imageContainer}>
                <Image source={{ uri: image }} style={[styles.image, containerStyle]} />
                {editable && (
                  <TouchableOpacity style={styles.clearButton} onPress={clearImage}>
                    <X size={16} color={colors.white} />
                  </TouchableOpacity>
                )}
              </View>
            ) : (
              <View style={[styles.placeholderContainer, containerStyle]}>
                <ImageIcon size={24} color={colors.gray[400]} />
                <Text style={styles.placeholderText}>{placeholder}</Text>
              </View>
            )}
          </View>
        }
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    // height: 160,
  },
  buttonColumn: {
    flex: 1,
    justifyContent: 'center',
    gap: 10,
  },
  actionButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 6,
  },

  analyzeButton: {
    backgroundColor: '#222',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 6,
  },
  buttonText: {
    color: colors.white,
    fontSize: 14,
  },
  analyzeText: {
    color: colors.white,
    fontSize: 14,
  },
  imageWrapper: {
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.gray[300],
    backgroundColor: colors.gray[50],
  },
  imageContainer: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: colors.gray[300],
    backgroundColor: colors.gray[50],
  },
  placeholderText: {
    marginTop: 6,
    color: colors.gray[500],
    fontSize: 12,
    textAlign: 'center',
  },
  clearButton: {
    position: 'absolute',
    top: 6,
    right: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ImagePicker;
