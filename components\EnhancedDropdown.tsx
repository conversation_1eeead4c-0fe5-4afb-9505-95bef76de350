import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, I18nManager, ViewStyle, TextStyle } from 'react-native';
import DropDownPicker from 'react-native-dropdown-picker';
import { collection, addDoc, query, where, getDocs } from 'firebase/firestore';
import { database } from '@/firebase/config';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';

export interface DropdownOption {
  label: string;
  value: string;
  icon?: React.ReactNode;
  color?: string;
}

interface EnhancedDropdownProps {
  label?: string;
  placeholder?: string;
  options: DropdownOption[];
  value: string | string[];
  onChange: (value: string | string[]) => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  dropdownStyle?: ViewStyle;
  error?: string;
  disabled?: boolean;
  isRTL?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  lookupCollection?: string; // Firestore collection for saving new items
  lookupCategory?: string; // The category to assign to a new item
  zIndex?: number;
  zIndexInverse?: number;
  onBlur?: () => void;
}

const EnhancedDropdown: React.FC<EnhancedDropdownProps> = ({
  label,
  placeholder = 'Select an option',
  options: initialOptions,
  value,
  onChange,
  containerStyle,
  labelStyle,
  dropdownStyle,
  error,
  disabled = false,
  isRTL = I18nManager.isRTL,
  multiple = false,
  searchable = true,
  lookupCollection,
  lookupCategory,
  zIndex = 5000,
  zIndexInverse = 1000,
  onBlur,
}) => {
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState(initialOptions);
  const { user } = useAuthStore();
  
  // Update items when initialOptions change - but only if they're different
  useEffect(() => {
    // Only update if the arrays are different to prevent infinite loops
    if (JSON.stringify(items) !== JSON.stringify(initialOptions)) {
      setItems(initialOptions);
    }
  }, [initialOptions]);

  // Function to add a new item to Firestore
  const addItemToFirestore = async (newItem: DropdownOption) => {
    if (!lookupCollection || !lookupCategory) return;
    
    try {
      // Check if item already exists
      const itemsRef = collection(database, lookupCollection);
      const q = query(itemsRef, where("value", "==", newItem.value));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        // Add the new item
        await addDoc(itemsRef, {
          ...newItem,
          createdBy: user?.id,
          category: lookupCategory,
          createdAt: new Date(),
        });
      }
    } catch (error) {
      console.error("Error adding item to Firestore:", error);
    }
  };

  // Handle adding a new item
  const handleAddItem = (inputValue: string) => {
    const newItem = {
      label: inputValue,
      value: inputValue.toLowerCase().replace(/\s+/g, '_'),
    };
    
    Alert.alert(
      "Add New Item",
      `Do you want to add "${inputValue}" to the list?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Add",
          onPress: async () => {
            // Add to local state
            const updatedItems = [...items, newItem];
            setItems(updatedItems);
            
            // Save to Firestore if lookupCollection is provided
            if (lookupCollection) {
              await addItemToFirestore(newItem);
            }
            
            // Select the new item
            onChange(multiple ? [...(value as string[]), newItem.value] : newItem.value);
          }
        }
      ]
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[
          styles.label,
          labelStyle,
          isRTL && styles.labelRTL
        ]}>
          {label}
        </Text>
      )}
      
      <DropDownPicker
        open={open}
        value={value}
        items={items}
        setOpen={setOpen}
        setValue={(callback) => {
          if (typeof callback === 'function') {
            const newValue = callback(value);
            onChange(newValue);
          } else {
            onChange(callback);
          }
        }}
        setItems={setItems}
        placeholder={placeholder}
        searchable={searchable}
        searchPlaceholder="Search..."
        multiple={multiple}
        disabled={disabled}
        rtl={isRTL}
        style={[
          styles.dropdown,
          error ? styles.dropdownError : null,
          disabled ? styles.dropdownDisabled : null,
          dropdownStyle
        ]}
        textStyle={styles.dropdownText}
        labelStyle={styles.dropdownLabel}
        placeholderStyle={styles.placeholderText}
        searchContainerStyle={styles.searchContainer}
        searchTextInputStyle={styles.searchInput}
        listMode="SCROLLVIEW"
        addCustomItem={searchable}
        customItemLabelStyle={styles.customItemLabel}
        onSelectItem={(item) => {
          if (item.custom) {
            handleAddItem(item.label);
          }
        }}
        onClose={() => {
          if (onBlur) onBlur();
        }}
        zIndex={zIndex}
        zIndexInverse={zIndexInverse}
      />
      
      {error && (
        <Text style={[
          styles.errorText,
          isRTL && styles.errorTextRTL
        ]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 6,
    textAlign: 'left',
  },
  labelRTL: {
    textAlign: 'right',
  },
  dropdown: {
    borderColor: colors.gray[300],
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.white,
  },
  dropdownError: {
    borderColor: colors.error,
  },
  dropdownDisabled: {
    backgroundColor: colors.gray[100],
    opacity: 0.7,
  },
  dropdownText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  dropdownLabel: {
    color: colors.gray[800],
  },
  placeholderText: {
    color: colors.gray[400],
  },
  errorText: {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
    textAlign: 'left',
  },
  errorTextRTL: {
    textAlign: 'right',
  },
  searchContainer: {
    borderBottomColor: colors.gray[200],
  },
  searchInput: {
    borderColor: colors.gray[300],
    borderWidth: 1,
    borderRadius: 8,
  },
  customItemLabel: {
    color: colors.primary,
    fontWeight: '500',
  },
});

export default EnhancedDropdown;
