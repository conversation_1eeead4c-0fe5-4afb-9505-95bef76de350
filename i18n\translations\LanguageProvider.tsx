// import React, { createContext, useState, useContext, useEffect } from 'react';
// import { I18nManager } from 'react-native';
// import AsyncStorage from '@react-native-async-storage/async-storage';
// import { changeLanguage } from './i18n';

// interface LanguageContextType {
//   locale: string;
//   isRTL: boolean;
//   setLocale: (lang: string) => Promise<void>;
//   ready: boolean;
// }

// const LanguageContext = createContext<LanguageContextType>({
//   locale: 'en',
//   isRTL: false,
//   setLocale: async () => {},
//   ready: false,
// });

// // export const useLanguage = () => useContext(LanguageContext);

// export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
//   const [locale, setLocaleState] = useState('en');
//   const [isRTLState, setIsRTL] = useState(false);
//   const [ready, setReady] = useState(false);

//   const init = async () => {
//     try {
//       // Get stored language from AsyncStorage
//       const storedLang = await AsyncStorage.getItem('appLanguage');
//       const languageToUse = storedLang || 'en';
//       const isRTL = languageToUse === 'ur';

//       console.log('Initializing language:', { storedLang, isRTL });
      
//       // IMPORTANT: We're setting allowRTL to false to prevent layout changes
//       // This ensures headers and tabs don't flip positions
//       I18nManager.allowRTL(false);
//       I18nManager.forceRTL(false);
      
//       // Set the language in the i18n system
//       changeLanguage(languageToUse);
//       setLocaleState(languageToUse);
//       setIsRTL(isRTL); // We still track isRTL for our custom RTL text handling
//       setReady(true);
//     } catch (error) {
//       console.error('Error initializing language:', error);
//       // Fallback to English if there's an error
//       changeLanguage('en');
//       setLocaleState('en');
//       setIsRTL(false);
//       setReady(true);
//     }
//   };

//   const setLocale = async (lang: string) => {
//     try {
//       const rtl = lang === 'ur';
      
//       // Store the selected language
//       await AsyncStorage.setItem('appLanguage', lang);
      
//       // Change language in the i18n system
//       changeLanguage(lang);
      
//       // IMPORTANT: Keep layout direction as LTR regardless of language
//       // This ensures headers and tabs don't flip positions
//       I18nManager.allowRTL(false);
//       I18nManager.forceRTL(false);
      
//       // Update state
//       setLocaleState(lang);
//       setIsRTL(rtl); // We still track isRTL for our custom RTL text handling
//     } catch (error) {
//       console.error('Error setting language:', error);
//     }
//   };

//   useEffect(() => {
//     init();
//   }, []);

//   return (
//     <LanguageContext.Provider value={{ locale, isRTL: isRTLState, setLocale, ready }}>
//       {children}
//     </LanguageContext.Provider>
//   );  
// };

// export const useLanguage = () => useContext(LanguageContext);



import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { en } from './en';
import { ur } from './ur';

// Define the supported languages
export type Language = 'en' | 'ur';

// Define the context type
interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, options?: any) => string;
  isRTL: boolean;
}

// Create the context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Define translations
const translations = {
  en: en,
  ur:ur,
};

// Create the provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');

  // Load the saved language on mount
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem('language');
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ur')) {
          setLanguageState(savedLanguage);
        }
      } catch (error) {
        console.error('Error loading language:', error);
      }
    };

    loadLanguage();
  }, []);

  // Function to set the language
  const setLanguage = async (newLanguage: Language) => {
    try {
      setLanguageState(newLanguage);
      await AsyncStorage.setItem('language', newLanguage);
    } catch (error) {
      console.error('Error saving language:', error);
    }
  };

  // Function to translate keys
  const t = (key: string, options?: any) => {
    try {
      const keys = key.split('.');
      let translation: any = translations[language];
      
      for (const k of keys) {
        if (translation && typeof translation === 'object' && k in translation) {
          translation = translation[k];
        } else {
          // If translation not found, try English as fallback
          if (language !== 'en') {
            let englishTranslation: any = translations.en;
            for (const k of keys) {
              if (englishTranslation && typeof englishTranslation === 'object' && k in englishTranslation) {
                englishTranslation = englishTranslation[k];
              } else {
                return key; // Return key if not found in English either
              }
            }
            return englishTranslation;
          }
          return key; // Return key if translation not found
        }
      }
      
      return typeof translation === 'string' ? translation : key;
    } catch (error) {
      console.warn(`Translation error for key: ${key}`, error);
      return key;
    }
  };

  // Check if the language is RTL
  const isRTL = language === 'ur';

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};