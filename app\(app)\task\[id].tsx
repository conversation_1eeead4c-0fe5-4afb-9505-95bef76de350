// import React, { useState, useEffect } from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   ScrollView,
//   TouchableOpacity,
//   SafeAreaView,
//   Alert,
//   Modal,
//   Image,
//   ActivityIndicator,
//   Switch,
// } from 'react-native';
// import { useLocalSearchParams, router, Stack } from 'expo-router';
// import { colors } from '@/constants/colors';
// import { useFarmStore } from '@/store/farm-store';
// import { useAuthStore } from '@/store/auth-store';
// import Button from '@/components/Button';
// import {
//   Calendar,
//   Clock,
//   Flag,
//   User,
//   MapPin,
//   CheckCircle,
//   MessageCircle,
//   MoreVertical,
//   Edit,
//   Trash,
//   ClipboardList,
//   Check,
//   X,
//   Image as ImageIcon,
//   Camera,
//   Trash2,
// } from 'lucide-react-native';
// import { ChecklistItem } from '@/types';
// import ImagePicker from '@/components/ImagePicker';
// import { uploadImageAsync } from '@/utils/firebase-storage';
// import { useTranslation } from '@/i18n/useTranslation';
// import { logEvent } from '@/store/logging';
// import { CheckListItemCard } from '@/components/CheckListItemCard';
// import DropdownPicker from '@/components/DropdownPicker';
// import Input from '@/components/Input';

// import * as ImagePicker1 from 'expo-image-picker';

// export default function TaskDetailScreen() {
//   const { id } = useLocalSearchParams();
//   const {
//     tasks,
//     fields,
//     gardens,
//     plants,
//     animals,
//     equipment,
//     completeTask,
//     deleteTask,
//     updateTask,
//     getUserById
//   } = useFarmStore();
//   const { user } = useAuthStore();
//   const { t, isRTL } = useTranslation()
//   const [showOptions, setShowOptions] = useState(false);
//   const [showCompleteModal, setShowCompleteModal] = useState(false);
//   const [showImageModal, setShowImageModal] = useState(false);
//   const [selectedImage, setSelectedImage] = useState('');
//   const [evidenceImages, setEvidenceImages] = useState<string[]>([]);
//   const [isSubmitting, setIsSubmitting] = useState(false);

//   // Find the task with the matching ID
//   const task = tasks.find(t => t.id === id);

//   // console.log({ task }, { id })
//   // Find the related entity
//   const getRelatedEntity = () => {
//     if (!task) return null;

//     if (task.fieldId) {
//       return { type: 'field', entity: fields.find(f => f.id === task.fieldId) };
//     } else if (task.gardenId) {
//       return { type: 'garden', entity: gardens.find(g => g.id === task.gardenId) };
//     } else if (task.plantId) {
//       return { type: 'plant', entity: plants.find(p => p.id === task.plantId) };
//     } else if (task.animalId) {
//       return { type: 'animal', entity: animals.find(a => a.id === task.animalId) };
//     } else if (task.equipmentId) {
//       return { type: 'equipment', entity: equipment.find(e => e.id === task.equipmentId) };
//     }

//     return null;
//   };

//   const relatedEntity = getRelatedEntity();

//   // Check if the current user is assigned to this task
//   const isAssignedToCurrentUser = user?.id === task?.assignedTo;

//   useEffect(() => {
//     if (task) {
//       console.log(task?.checklistDetails?.items)
//       logEvent('view_task', { taskId: task.id, taskTitle: task.title });
//     }
//   }, [task]);

//   const [checklistItems, setChecklistItems] = useState(task?.checklistDetails?.items || []);
//   const handleFieldChange = (id, value) => {
//     setChecklistItems(prev =>
//       prev.map(item =>
//         item.id === id ? { ...item, value } : item
//       )
//     );
//   };

//   const handleDeleteTask = () => {
//     Alert.alert(
//       'Delete Task',
//       'Are you sure you want to delete this task?',
//       [
//         { text: 'Cancel', style: 'cancel' },
//         {
//           text: 'Delete',
//           style: 'destructive',
//           onPress: () => {
//             deleteTask(task.id);
//             router.back();
//           }
//         }
//       ]
//     );
//   };

//   const handleDirectCompletionSubmit = async () => {
//     if (isSubmitting) return;
//     setIsSubmitting(true);

//     try {
//       // 1. Validate required checklist items
//       const incompleteRequired = task?.checklistDetails?.items?.filter(
//         item => item.required && !values[item.id] && !item.value
//       );

//       if (incompleteRequired && incompleteRequired.length > 0) {
//         Alert.alert(
//           t('task.incompleteRequiredItems'),
//           t('task.pleaseCompleteRequiredItems')
//         );
//         setIsSubmitting(false);
//         return;
//       }

//       // 2. Validate required evidence
//       if (task.evidence?.required && images.length === 0){
//         Alert.alert('Evidence Required', 'Please add at least one image as evidence.');
//         return;
//       }

//       // 2. Upload new images
//       const uploadedImageUrls: string[] = [];
//       if (newEvidenceImages.length > 0) {
//         for (const uri of newEvidenceImages) {
//           if (uri && !uri.startsWith('http')) {
//             const url = await uploadImageAsync(uri, 'task-evidence');
//             uploadedImageUrls.push(url);
//           }
//         }
//       }

//       // 3. Prepare the update payload
//       const updatePayload = {
//         checklist: completedChecklist,
//         evidence: {
//           ...task?.evidence,
//           images: [...(task.evidence?.images || []), ...uploadedImageUrls],
//           notes: completionNotes,
//         },
//       };

//       // 4. Update the task with new data, then mark as complete
//       await updateTask(task.id, updatePayload);
//       await completeTask(task?.id);

//       // Add history entry to related entity if any
//       if (relatedEntity) {
//         // This would be handled by the completeTask function in a real implementation
//       }

//       setShowCompleteModal(false);
//       Alert.alert('Success', 'Task completed successfully');
//       logEvent('submit_task', {
//         taskId: task.id,
//         taskTitle: task.title,
//         status: 'completed',
//       });
//     } catch (error) {
//       console.error('Error completing task:', error);
//       logEvent('error', { context: 'complete_task', error: error.message, taskId: task.id });
//       Alert.alert('Error', 'Failed to complete task. Please try again.');
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   const formatDueDate = (dateString: string) => {
//     const date = new Date(dateString);
//     return date.toLocaleDateString([], {
//       weekday: 'long',
//       month: 'long',
//       day: 'numeric',
//       year: 'numeric',
//     });
//   };

//   const formatDueTime = (dateString: string) => {
//     const date = new Date(dateString);
//     return date.toLocaleTimeString([], {
//       hour: '2-digit',
//       minute: '2-digit',
//     });
//   };

//   const getPriorityColor = (priority: string) => {
//     switch (priority) {
//       case 'high':
//         return colors.danger;
//       case 'medium':
//         return colors.warning;
//       case 'low':
//         return colors.success;
//       default:
//         return colors.gray[500];
//     }
//   };

//   const addEvidenceImage = (uri: string) => {
//     setEvidenceImages([...evidenceImages, uri]);
//   };

//   const removeEvidenceImage = (index: number) => {
//     setEvidenceImages(evidenceImages.filter((_, i) => i !== index));
//   };
//   const fetchAssigneeName = async (assignedTo: string) => {
//     if (assignedTo) {
//       try {
//         const assignee = await getUserById(assignedTo);
//         if (assignee) {
//           return assignee.name || assignee.displayName || '';
//         }
//       } catch (error) {
//         console.error('Error fetching assignee:', error);
//       }
//     }
//     return 'unknown';
//   };

//   const [assigneeName, setAssigneeName] = useState<string>('unknown');

//   useEffect(() => {
//     if (task?.assignedTo && task?.assignedTo !== user?.id) {
//       fetchAssigneeName(task.assignedTo).then(name => setAssigneeName(name));
//     }
//   }, [task?.assignedTo, user, user?.id]);


//   const [values, setValues] = useState<Record<string, any>>({});
//   const [images, setImages] = useState<string[]>([]);
//   const [notes, setNotes] = useState('');
//   const handleChange = (itemId: string, value: any) => {
//     setValues((prev) => ({ ...prev, [itemId]: value }));
//   };
//   const handlePickImage = async (itemId: string) => {
//     const permission = await ImagePicker1.requestMediaLibraryPermissionsAsync();
//     if (!permission.granted) {
//       alert('Permission to access media library is required!');
//       return;
//     }

//     const result = await ImagePicker1.launchImageLibraryAsync({
//       mediaTypes: ImagePicker1.MediaTypeOptions.Images,
//       quality: 0.5,
//     });

//     if (!result.canceled && result.assets && result.assets.length > 0) {
//       handleChange(itemId, result.assets[0].uri);
//     }
//   };
//   const addImage = (uri: string) => {
//     setImages([...images, uri]);
//     if (addEvidenceImage) addEvidenceImage(uri);
//   };
//   const removeImage = (index: number) => {
//     setImages(images.filter((_, i) => i !== index));
//   };
//   if (!task) {
//     return (
//       <SafeAreaView style={styles.container}>
//         <View style={styles.notFoundContainer}>
//           <Text style={styles.notFoundText}>{t('common.notFound')}</Text>
//           <Button
//             title="Go Back"
//             onPress={() => router.back()}
//             style={styles.backButton}
//           />
//         </View>
//       </SafeAreaView>
//     );
//   }
//   return (
//     <>
//       <Stack.Screen
//         options={{
//           title: 'Task Details',

//         }}
//       />

//       <SafeAreaView style={styles.container}>

//         <ScrollView contentContainerStyle={styles.scrollContent}>

//           <View style={[styles.header,]}>
//             <Text style={[styles.title, isRTL && { textAlign: 'right' }]}>
//               {task?.title ?? t('task.untitled')}
//             </Text>

//             <View style={[styles.statusContainer, isRTL && { flexDirection: 'row-reverse' }]}>
//               <View
//                 style={[
//                   styles.statusBadge,
//                   {
//                     backgroundColor: task?.status === 'completed' ? colors.success + '20' : task?.status === 'overdue' ? colors.danger + '20' : colors.primary + '20',
//                   },
//                 ]}
//               >
//                 <Text
//                   style={[
//                     styles.statusText,
//                     { color: task?.status === 'completed' ? colors.success : task?.status === 'overdue' ? colors.danger : colors.primary, },]}
//                 >
//                   {task?.status
//                     ? t(`task.status.${task.status}`)
//                     : t('task.status.unknown')}
//                 </Text>
//               </View>

//               <View style={styles.priorityBadge}>
//                 <Text
//                   style={[
//                     styles.priorityText,
//                     { color: getPriorityColor(task?.priority ?? 'low') },
//                   ]}
//                 >
//                   {task?.priority
//                     ? t(`task.priority.${task.priority}`)
//                     : t('task.priority.low')}{' '}
//                   {/* {t('task.priority.label')} */}
//                 </Text>
//               </View>

//               {task?.frequency && task.frequency !== 'once' && (
//                 <View style={styles.recurringBadge}>
//                   <Text style={styles.recurringText}>
//                     {t(`task.frequency.${task.frequency}`)}
//                   </Text>
//                 </View>
//               )}
//             </View>
//           </View>

//           <View style={styles.detailsContainer}>
//             {task?.description ? (
//               <View style={styles.descriptionContainer}>
//                 <Text style={[styles.descriptionText, isRTL && { textAlign: 'right' }]}>{task.description}</Text>
//               </View>
//             ) : null}

//             <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
//               <View style={styles.detailIcon}>
//                 <Calendar size={20} color={colors.primary} />
//               </View>
//               <View style={[styles.detailContent]}>
//                 <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('task.dueDate')}</Text>
//                 <Text style={[styles.detailValue, isRTL && styles.rtlTextAlign]}>
//                   {task?.dueDate ? formatDueDate(task.dueDate) : t('common.na')}
//                 </Text>
//               </View>
//             </View>

//             <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
//               <View style={styles.detailIcon}>
//                 <Clock size={20} color={colors.primary} />
//               </View>
//               <View style={styles.detailContent}>
//                 <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('tasks.dueTime')}</Text>
//                 <Text style={[styles.detailValue, isRTL && styles.rtlTextAlign]}>
//                   {task?.dueDate ? formatDueTime(task.dueDate) : t('common.na')}
//                 </Text>
//               </View>
//             </View>

//             <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
//               <View style={styles.detailIcon}>
//                 <Flag size={20} color={colors.primary} />
//               </View>
//               <View style={styles.detailContent}>
//                 <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('tasks.priority')}</Text>
//                 <Text
//                   style={[
//                     styles.detailValue, , isRTL && styles.rtlTextAlign,
//                     { color: getPriorityColor(task?.priority ?? 'low') },
//                   ]}
//                 >
//                   {task?.priority
//                     ? t(`task.priority.${task.priority}`)
//                     : t('task.priority.low')}
//                 </Text>
//               </View>
//             </View>

//             <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
//               <View style={styles.detailIcon}>
//                 <User size={20} color={colors.primary} />
//               </View>
//               <View style={styles.detailContent}>
//                 <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('task.assignedTo')}</Text>
//                 <Text style={[styles.detailValue, isRTL && styles.rtlTextAlign]}>
//                   {task?.assignedTo
//                     ? task.assignedTo === user?.id
//                       ? t('task.you')
//                       : assigneeName ?? t('task.unknown')
//                     : t('task.unassigned')}
//                 </Text>
//               </View>
//             </View>
//           </View>

//           {relatedEntity?.entity?.name && (
//             <View style={styles.detailItem}>
//               <View style={styles.detailIcon}>
//                 <MapPin size={20} color={colors.primary} />
//               </View>
//               <View style={styles.detailContent}>
//                 <Text style={styles.detailLabel}>
//                   {t('task.related')}{" "}
//                   {relatedEntity?.type
//                     ? t(`entities.${relatedEntity.type}`, relatedEntity.type)
//                     : t('task.entity')}
//                 </Text>
//                 <Text style={styles.detailValue}>{relatedEntity.entity.name}</Text>
//               </View>
//             </View>
//           )}


//           {/* <ScrollView style={styles.container}> */}

//           <View style={styles.card}>
//             <Text style={styles.title}>{task?.checklistDetails?.title}</Text>
//             <Text style={styles.instructions}>{task?.checklistDetails?.instructions}</Text>

//             {task?.checklistDetails?.items?.map((item) => (
//               <View key={item.id} style={styles.card}>
//                 <Text style={styles.label}>{item.label}</Text>

//                 {item.type === 'boolean' ? (
//                   <View style={styles.booleanWrapper}>
//                     <Text style={styles.booleanStatus}>
//                       {item?.value ? 'Yes' : 'No'}
//                     </Text>
//                     <Switch
//                       // value={item?.value || false}
//                       value={!!values[item.id] || item?.value || false}
//                       disabled={item?.value ? true : false}
//                       onValueChange={(val) => handleChange(item.id, val)}
//                     />
//                   </View>
//                 ) : item.type === 'select' && item.options ? (
//                   <DropdownPicker
//                     style={[
//                       styles.dropdown,
//                       !!item?.value && styles.disabledInput,
//                     ]}
//                     label={''}
//                     options={item.options.map((option) => ({ label: option, value: option }))}
//                     onSelect={(val) => handleChange(item.id, val)}
//                     selectedValue={values[item.id] || item?.value || undefined}
//                     disabled={item?.value ? true : false}
//                   />
//                 ) : item.type === 'text' ? (
//                   <Input
//                     style={[
//                       styles.textInput,
//                       !!item?.value && styles.disabledInput,
//                     ]}
//                     // value={item?.value || ''}
//                     value={values[item.id] || item?.value || undefined}
//                     editable={!item?.value ? true : false}
//                     onChangeText={(val) => handleChange(item.id, val)}
//                     placeholder="Enter text"
//                   />
//                 ) : item.type === 'image' ? (
//                   <View style={styles.imageWrapper}>
//                     {item?.value || values[item.id] && (
//                       <Image
//                         source={{ uri: item.value||values[item.id] }}
//                         style={styles.imagePreview}
//                       />
//                     )}
//                     <Button
//                       title={item?.value ? 'Change Image' : 'Pick Image'}
//                       onPress={() => handlePickImage(item.id)}
//                       disabled={item?.value ? true : false}
//                       color={!!item?.value ? '#aaa' : '#007AFF'}
//                     />
//                   </View>
//                 ) : null}
//               </View>
//             ))}
//           </View>



//           {task?.evidence && (
//             <View style={styles.evidenceContainer}>
//               <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign]}>
//                 {t('task.evidence')}{' '}
//                 {task.evidence.required && (
//                   <Text style={styles.requiredText}>
//                     ({t('task.required')})
//                   </Text>
//                 )}
//               </Text>

//               { //task.status === 'completed' &&
//                 Array.isArray(task.evidence.images) &&
//                   task.evidence.images.length > 0 ? (

//                   <View style={styles.evidenceImagesContainer}>
//                     {task?.evidence?.images?.map((imageUrl, index) =>
//                       imageUrl ? (
//                         <TouchableOpacity
//                           key={index}
//                           style={styles.evidenceImageWrapper}
//                           onPress={() => {
//                             setSelectedImage(imageUrl);
//                             setShowImageModal(true);
//                           }}
//                         >
//                           <Image
//                             source={{ uri: imageUrl }}
//                             style={styles.evidenceImage}
//                             resizeMode="cover"
//                           />
//                         </TouchableOpacity>
//                       ) : null
//                     )}
//                   </View>
//                 ) : (
//                   <View style={styles.noEvidenceContainer}>
//                     <ImageIcon size={24} color={colors.gray[400]} />
//                     <Text style={styles.noEvidenceText}>
//                       {task.status === 'completed'
//                         ? t('task.noEvidenceProvided')
//                         : t('task.evidenceRequired')}
//                     </Text>
//                   </View>
//                 )}

//               <View style={styles.imagesContainer}>
//                 {images.map((image, index) => (
//                   <View key={index} style={styles.imageWrapper}>
//                     <ImagePicker
//                       image={image}
//                       onImageSelected={() => { }}
//                       size={100}
//                       editable={false}
//                     />
//                     <TouchableOpacity
//                       style={styles.removeImageButton}
//                       onPress={() => removeImage(index)}
//                     >
//                       <Trash2 size={16} color={colors.white} />
//                     </TouchableOpacity>
//                   </View>
//                 ))}
//                 <ImagePicker
//                   image=""
//                   onImageSelected={addImage}
//                   placeholder="Add Image"
//                   size={200}
//                   isPreview={false}
//                 />
//               </View>
//               {/* <View style={styles.imagesContainer}>
//                 {images.map((image, index) => (
//                   <View key={index} style={styles.imageWrapper}>
//                     <ImagePicker
//                       image={image}
//                       onImageSelected={() => { }}
//                       size={100}
//                       editable={false}
//                     />
//                     <TouchableOpacity
//                       style={styles.removeImageButton}
//                       onPress={() => removeImage(index)}
//                     >
//                       <Trash2 size={16} color={colors.white} />
//                     </TouchableOpacity>
//                   </View>
//                 ))}

//                 <ImagePicker
//                   image=""
//                   onImageSelected={addImage}
//                   placeholder="Add Image"
//                   size={200}
//                   isPreview={false}
//                 />
//               </View> */}

//               {task.evidence.notes && (
//                 <View style={styles.evidenceNotesContainer}>
//                   <Text style={styles.evidenceNotesLabel}>{t('task.notes')}</Text>
//                   <Text style={styles.evidenceNotesText}>{task.evidence.notes}</Text>
//                 </View>
//               )}
//             </View>
//           )}

//           <Text style={styles.sectionTitle}>Notes</Text>
//           <Input
//             style={styles.notesInput}
//             value={notes}
//             onChangeText={setNotes}
//             placeholder="Add any additional notes here..."
//             multiline
//             numberOfLines={4}
//           />
//           <View style={styles.timelineContainer}>
//             <Text style={[styles.timelineTitle, isRTL && styles.rtlTextAlign]}>{t('task.timeline')}</Text>

//             <View style={styles.timelineItem}>
//               <View style={styles.timelineDot} />
//               <View style={styles.timelineContent}>
//                 <Text style={[styles.timelineEvent, isRTL && styles.rtlTextAlign]}>{t('task.created')}</Text>
//                 <Text style={[styles.timelineDate, isRTL && styles.rtlTextAlign]}>
//                   {task?.createdAt
//                     ? new Date(task.createdAt).toLocaleDateString([], {
//                       month: 'short',
//                       day: 'numeric',
//                       hour: '2-digit',
//                       minute: '2-digit',
//                     })
//                     : t('common.na')}
//                 </Text>
//               </View>
//             </View>

//             {task?.status === 'completed' && task?.completedAt && (
//               <View style={styles.timelineItem}>
//                 <View
//                   style={[styles.timelineDot, { backgroundColor: colors.success }]}
//                 />
//                 <View style={styles.timelineContent}>
//                   <Text style={[styles.timelineEvent, isRTL && styles.rtlTextAlign]}>{t('task.completed')}</Text>
//                   <Text style={[styles.timelineDate, isRTL && styles.rtlTextAlign]}>
//                     {new Date(task.completedAt).toLocaleDateString([], {
//                       month: 'short',
//                       day: 'numeric',
//                       hour: '2-digit',
//                       minute: '2-digit',
//                     })}
//                   </Text>
//                 </View>
//               </View>
//             )}
//           </View>
//         </ScrollView>
//         {
//           task.status !== 'completed' && isAssignedToCurrentUser && (
//             <View style={styles.actionContainer}>
//               <Button
//                 title={t('task.markAsCompleted')}
//                 onPress={() => setShowCompleteModal(true)}
//                 leftIcon={<CheckCircle size={20} color={colors.white} />}
//               />
//             </View>
//           )
//         }
//         {/* Complete Task Modal */}
//         <Modal
//           visible={showCompleteModal}
//           animationType="slide"
//           onRequestClose={() => setShowCompleteModal(false)}
//         >
//           <SafeAreaView style={styles.modalContainer}>
//             <View style={styles.modalHeader}>
//               <TouchableOpacity
//                 onPress={() => setShowCompleteModal(false)}
//                 style={styles.modalCloseButton}
//               >
//                 <X size={24} color={colors.gray[700]} />
//               </TouchableOpacity>
//               <Text style={styles.modalTitle}>{t('task.completeTask')}</Text>
//               <View style={styles.modalHeaderRight} />
//             </View>
//             <ChecklistForm
//               checklist={task?.checklistDetails}
//               onSave={handleCompletionSubmit}
//               requireImages={task.evidence?.required}
//             />
//           </SafeAreaView>
//         </Modal>

//         {/* Image Viewer Modal */}
//         <Modal
//           visible={showImageModal}
//           transparent={true}
//           animationType="fade"
//           onRequestClose={() => setShowImageModal(false)}
//         >
//           <View style={styles.imageModalOverlay}>
//             <TouchableOpacity
//               style={styles.imageModalCloseButton}
//               onPress={() => setShowImageModal(false)}
//             >
//               <X size={24} color={colors.white} />
//             </TouchableOpacity>

//             <Image
//               source={{ uri: selectedImage }}
//               style={styles.fullImage}
//               resizeMode="contain"
//             />
//           </View>
//         </Modal>
//       </SafeAreaView >
//     </>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.gray[50],
//   },
//   instructions: {
//     fontSize: 14,
//     color: colors.gray[700],
//     marginBottom: 12,
//   },
//   rtlDirection: {
//     flexDirection: 'row-reverse'
//   },
//   rtlTextAlign: {
//     textAlign: 'right',
//     marginRight: 8

//   },
//   card: {
//     backgroundColor: '#fff',
//     padding: 16,
//     marginBottom: 16,
//     borderRadius: 12,
//     shadowColor: '#000',
//     shadowOpacity: 0.05,
//     shadowRadius: 6,
//     shadowOffset: { width: 0, height: 2 },
//     elevation: 3,
//   },
//   label: {
//     fontSize: 16,
//     fontWeight: '600',
//     marginBottom: 10,
//     color: '#222',
//   },
//   booleanWrapper: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//   },
//   booleanStatus: {
//     fontSize: 14,
//     color: '#555',
//   },
//   dropdown: {
//     borderWidth: 1,
//     borderColor: '#ccc',
//     borderRadius: 8,
//     paddingVertical: 10,
//     paddingHorizontal: 12,
//     backgroundColor: '#f9f9f9',
//   },
//   imagesContainer: {
//     flex: 1,
//     flexDirection: 'row',
//     flexWrap: 'wrap',
//     marginTop: 8,
//   },
//   imageWrapper: {
//     position: 'relative',
//     marginRight: 12,
//     marginBottom: 12,
//   },
//   removeImageButton: {
//     position: 'absolute',
//     top: 0,
//     right: 0,
//     backgroundColor: colors.danger,
//     borderRadius: 12,
//     padding: 4,
//   },
//   notesInput: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: 8,
//     padding: 12,
//     fontSize: 16,
//     color: colors.gray[800],
//     minHeight: 100,
//     textAlignVertical: 'top',
//     width:'100%'
//   },

//   textInput: {
//     // borderWidth: 1,
//     // borderColor: '#ccc',
//     // borderRadius: 8,
//     paddingVertical: 10,
//     paddingHorizontal: 12,
//     // backgroundColor: '#f9f9f9',
//     fontSize: 14,
//   },
//   disabledInput: {
//     backgroundColor: '#f0f0f0',
//     width: '100%',
//     color: '#999',
//   },
//   imageWrapper: {
//     marginTop: 10,
//     alignItems: 'center',
//   },
//   imagePreview: {
//     width: 150,
//     height: 150,
//     borderRadius: 12,
//     marginBottom: 10,
//   },
//   removeImageButton: {
//     position: 'absolute',
//     top: 0,
//     right: 0,
//     width: 32,
//     height: 32,
//     borderRadius: 16,
//     backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   headerButton: {
//     width: 40,
//     height: 40,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   optionsMenu: {
//     position: 'absolute',
//     top: 0,
//     right: 16,
//     backgroundColor: colors.white,
//     borderRadius: 8,
//     padding: 8,
//     zIndex: 10,
//     shadowColor: colors.gray[800],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 4,
//   },
//   optionItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingVertical: 8,
//     paddingHorizontal: 12,
//   },
//   optionText: {
//     fontSize: 14,
//     color: colors.gray[800],
//     marginLeft: 8,
//   },
//   scrollContent: {
//     padding: 16,
//     paddingBottom: 80,
//   },
//   notFoundContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     padding: 20,
//   },
//   notFoundText: {
//     fontSize: 18,
//     color: colors.gray[600],
//     marginBottom: 20,
//   },
//   backButton: {
//     width: 120,
//   },
//   header: {
//     marginBottom: 24,
//   },
//   priorityBadge: {
//     alignSelf: 'flex-start',
//     paddingHorizontal: 12,
//     paddingVertical: 6,
//     borderRadius: 16,
//     // marginBottom: 12,s
//     backgroundColor: colors.gray[100],
//   },
//   priorityText: {
//     fontSize: 12,
//     fontWeight: '600',
//   },
//   title: {
//     fontSize: 24,
//     fontWeight: '700',
//     color: colors.gray[800],
//     marginBottom: 12,
//   },
//   statusContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   statusBadge: {
//     paddingHorizontal: 12,
//     paddingVertical: 6,
//     borderRadius: 16,
//     marginRight: 8,
//   },
//   statusText: {

//     fontSize: 12,
//     fontWeight: '600',
//   },
//   recurringBadge: {
//     paddingHorizontal: 12,
//     paddingVertical: 6,
//     borderRadius: 16,
//     backgroundColor: colors.info + '20',
//   },
//   recurringText: {
//     fontSize: 12,
//     fontWeight: '600',
//     color: colors.info,
//   },
//   detailsContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 24,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   descriptionContainer: {
//     marginBottom: 16,
//     paddingBottom: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   descriptionText: {
//     fontSize: 14,
//     lineHeight: 20,
//     color: colors.gray[700],
//   },
//   detailItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingVertical: 12,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   detailIcon: {
//     width: 40,
//     height: 40,
//     borderRadius: 20,
//     backgroundColor: colors.primaryLight,
//     justifyContent: 'center',
//     alignItems: 'center',
//     marginRight: 12,
//   },
//   detailContent: {
//     flex: 1,
//   },
//   detailLabel: {
//     fontSize: 12,
//     color: colors.gray[500],
//     marginBottom: 4,
//   },
//   detailValue: {
//     fontSize: 14,
//     fontWeight: '500',
//     color: colors.gray[800],
//   },
//   checklistContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 24,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   sectionTitle: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     marginBottom: 16,
//   },
//   checklistItem: {
//     flexDirection: 'row',
//     alignItems: 'flex-start',
//     paddingVertical: 12,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   checkbox: {
//     width: 24,
//     height: 24,
//     borderRadius: 4,
//     borderWidth: 2,
//     borderColor: colors.gray[400],
//     justifyContent: 'center',
//     alignItems: 'center',
//     marginRight: 12,
//     marginTop: 2,
//   },
//   checkboxChecked: {
//     backgroundColor: colors.primary,
//     borderColor: colors.primary,
//   },
//   checklistItemContent: {
//     flex: 1,
//   },
//   checklistItemTitle: {
//     fontSize: 16,
//     color: colors.gray[800],
//     marginBottom: 4,
//   },
//   checklistItemDescription: {
//     fontSize: 14,
//     color: colors.gray[600],
//     marginBottom: 4,
//   },
//   requiredBadge: {
//     backgroundColor: colors.danger + '20',
//     paddingHorizontal: 8,
//     paddingVertical: 2,
//     borderRadius: 4,
//     alignSelf: 'flex-start',
//   },
//   requiredText: {
//     fontSize: 12,
//     color: colors.danger,
//   },
//   evidenceContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 24,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   evidenceImagesContainer: {
//     flexDirection: 'row',
//     flexWrap: 'wrap',
//     marginBottom: 16,
//   },
//   evidenceImageWrapper: {
//     width: 100,
//     height: 100,
//     borderRadius: 8,
//     marginRight: 8,
//     marginBottom: 8,
//     overflow: 'hidden',
//   },
//   evidenceImage: {
//     width: '100%',
//     height: '100%',
//   },
//   noEvidenceContainer: {
//     alignItems: 'center',
//     justifyContent: 'center',
//     padding: 24,
//     backgroundColor: colors.gray[100],
//     borderRadius: 8,
//     marginBottom: 16,
//   },
//   noEvidenceText: {
//     fontSize: 14,
//     color: colors.gray[600],
//     marginTop: 8,
//     textAlign: 'center',
//   },
//   evidenceNotesContainer: {
//     backgroundColor: colors.gray[100],
//     borderRadius: 8,
//     padding: 12,
//   },
//   evidenceNotesLabel: {
//     fontSize: 14,
//     fontWeight: '500',
//     color: colors.gray[700],
//     marginBottom: 4,
//   },
//   evidenceNotesText: {
//     fontSize: 14,
//     color: colors.gray[700],
//   },
//   timelineContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 24,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   timelineTitle: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     marginBottom: 16,
//   },
//   timelineItem: {
//     flexDirection: 'row',
//     marginBottom: 16,
//     position: 'relative',
//   },
//   timelineDot: {
//     width: 12,
//     height: 12,
//     borderRadius: 6,
//     backgroundColor: colors.primary,
//     marginTop: 4,
//     marginRight: 12,
//   },
//   timelineContent: {
//     flex: 1,
//   },
//   timelineEvent: {
//     fontSize: 14,
//     fontWeight: '500',
//     color: colors.gray[800],
//     marginBottom: 4,
//   },
//   timelineDate: {
//     fontSize: 12,
//     color: colors.gray[500],
//   },
//   actionContainer: {
//     position: 'absolute',
//     bottom: 0,
//     left: 0,
//     right: 0,
//     backgroundColor: colors.white,
//     padding: 16,
//     borderTopWidth: 1,
//     borderTopColor: colors.gray[200],
//   },
//   modalContainer: {
//     flex: 1,
//     backgroundColor: colors.white,
//   },
//   modalHeader: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     padding: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   modalCloseButton: {
//     padding: 4,
//   },
//   modalTitle: {
//     fontSize: 18,
//     fontWeight: '600',
//     color: colors.gray[800],
//   },
//   modalHeaderRight: {
//     width: 32,
//   },
//   completeButtonContainer: {
//     padding: 16,
//     borderTopWidth: 1,
//     borderTopColor: colors.gray[200],
//   },
//   imageModalOverlay: {
//     flex: 1,
//     backgroundColor: 'rgba(0, 0, 0, 0.9)',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   imageModalCloseButton: {
//     position: 'absolute',
//     top: 40,
//     right: 20,
//     zIndex: 10,
//     padding: 8,
//   },
//   fullImage: {
//     width: '100%',
//     height: '80%',
//   },
// });




import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal, // Keep Modal for Image Viewer
  Image,
  ActivityIndicator, // Keep ActivityIndicator for loading state
  Switch,
} from 'react-native';
import { useLocalSearchParams, router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import {
  Calendar,
  Clock,
  Flag,
  User,
  MapPin,
  CheckCircle,
  MessageCircle,
  MoreVertical,
  Edit,
  Trash,
  ClipboardList,
  Check,
  X,
  Image as ImageIcon,
  Camera,
  Trash2,
} from 'lucide-react-native';
import { ChecklistItem } from '@/types';
// import ChecklistForm from '@/components/ChecklistForm'; // No longer needed directly
import ImagePicker from '@/components/ImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { useTranslation } from '@/i18n/useTranslation';
import { logEvent } from '@/store/logging';
import { CheckListItemCard } from '@/components/CheckListItemCard';
import DropdownPicker from '@/components/DropdownPicker';
import Input from '@/components/Input';
import ImageSlider from '@/components/ImageSlider';

import * as ImagePicker1 from 'expo-image-picker'; // Renamed to avoid conflict with custom ImagePicker component

export default function TaskDetailScreen() {
  const { id } = useLocalSearchParams();
  const {
    tasks,
    fields,
    gardens,
    plants,
    animals,
    equipment,
    completeTask,
    deleteTask,
    updateTask,
    getUserById
  } = useFarmStore();
  const { user } = useAuthStore();
  const { t, isRTL } = useTranslation()
  const [showOptions, setShowOptions] = useState(false);
  // const [showCompleteModal, setShowCompleteModal] = useState(false); // REMOVED
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState('');
  // const [evidenceImages, setEvidenceImages] = useState<string[]>([]); // This will be managed by 'images' state

  // States for checklist and evidence input
  const [checklistValues, setChecklistValues] = useState<Record<string, any>>({});
  const [evidenceImages, setEvidenceImages] = useState<string[]>([]); // Renamed from 'images' to be clearer for evidence
  const [completionNotes, setCompletionNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false); // Added for submission loading state

  // Find the task with the matching ID
  const task = tasks.find(t => t.id === id);

  // Initialize checklistValues and evidenceImages from existing task data on load
  useEffect(() => {
    if (task) {
      const initialChecklistValues: Record<string, any> = {};
      task.checklistDetails?.items?.forEach(item => {
        if (item.value !== undefined) {
          initialChecklistValues[item.id] = item.value;
        }
      });
      setChecklistValues(initialChecklistValues);
      setEvidenceImages(task.evidence?.images || []);
      setCompletionNotes(task.evidence?.notes || '');
      logEvent('view_task', { taskId: task.id, taskTitle: task.title });
    }
  }, [task]);


  // Find the related entity
  const getRelatedEntity = () => {
    if (!task) return null;

    if (task.fieldId) {
      return { type: 'field', entity: fields.find(f => f.id === task.fieldId) };
    } else if (task.gardenId) {
      return { type: 'garden', entity: gardens.find(g => g.id === task.gardenId) };
    } else if (task.plantId) {
      return { type: 'plant', entity: plants.find(p => p.id === task.plantId) };
    } else if (task.animalId) {
      return { type: 'animal', entity: animals.find(a => a.id === task.animalId) };
    } else if (task.equipmentId) {
      return { type: 'equipment', entity: equipment.find(e => e.id === task.equipmentId) };
    }

    return null;
  };

  const relatedEntity = getRelatedEntity();

  // Check if the current user is assigned to this task
  const isAssignedToCurrentUser = user?.id === task?.assignedTo;
  const isTaskCompleted = task?.status === 'completed';

  const handleChecklistItemChange = (itemId: string, value: any) => {
    setChecklistValues(prev => ({ ...prev, [itemId]: value }));
  };

  const handleDeleteTask = () => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            if (task) {
              deleteTask(task.id);
              router.back();
            }
          }
        }
      ]
    );
  };

  const handleCompleteTask = async () => {
    if (!task) return;

    setIsSubmitting(true);
    try {
      // 1. Validate required fields
      if (task.evidence?.required && evidenceImages.length === 0) {
        Alert.alert('Evidence Required', 'Please add at least one image as evidence.');
        setIsSubmitting(false);
        return;
      }

      // 2. Upload new images (only those not starting with 'http' - assumed already uploaded)
      const newUrisToUpload = evidenceImages.filter(uri => uri && !uri.startsWith('http'));
      const uploadedImageUrls: string[] = [];

      for (const uri of newUrisToUpload) {
        const url = await uploadImageAsync(uri, 'task-evidence');
        uploadedImageUrls.push(url);
      }

      // Combine existing and newly uploaded images
      const finalEvidenceImages = [
        ...(task.evidence?.images || []).filter(img => img && img.startsWith('http')), // Keep existing uploaded images
        ...uploadedImageUrls, // Add newly uploaded images
      ];


      // 3. Prepare the completed checklist items
      const completedChecklistItems = task.checklistDetails?.items?.map(item => ({
        ...item,
        value: checklistValues[item.id] !== undefined ? checklistValues[item.id] : item.value,
      })) || [];

      // 4. Prepare the update payload
      const updatePayload = {
        checklistDetails: {
          ...task.checklistDetails,
          items: completedChecklistItems,
        },
        evidence: {
          ...task?.evidence,
          images: finalEvidenceImages,
          notes: completionNotes,
        },
      };

      // 5. Update the task with new data, then mark as complete
      await updateTask(task.id, updatePayload); // Update with checklist and evidence values
      await completeTask(task.id); // Mark as complete

      Alert.alert('Success', 'Task completed successfully');
      logEvent('submit_task', {
        taskId: task.id,
        taskTitle: task.title,
        status: 'completed',
      });
      // Optionally navigate back or refresh data after completion
      // router.back();
    } catch (error: any) {
      console.error('Error completing task:', error);
      logEvent('error', { context: 'complete_task', error: error.message, taskId: task.id });
      Alert.alert('Error', `Failed to complete task: ${error.message}. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDueDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatDueTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return colors.danger;
      case 'medium':
        return colors.warning;
      case 'low':
        return colors.success;
      default:
        return colors.gray[500];
    }
  };

  const addEvidenceImage = (uri: string) => {
    setEvidenceImages([...evidenceImages, uri]);
  };

  const removeEvidenceImage = (index: number) => {
    setEvidenceImages(evidenceImages.filter((_, i) => i !== index));
  };

  const fetchAssigneeName = async (assignedTo: string) => {
    if (assignedTo) {
      try {
        const assignee = await getUserById(assignedTo);
        if (assignee) {
          return assignee.name || assignee.displayName || '';
        }
      } catch (error) {
        console.error('Error fetching assignee:', error);
      }
    }
    return 'unknown';
  };

  const [assigneeName, setAssigneeName] = useState<string>('unknown');

  useEffect(() => {
    if (task?.assignedTo && task?.assignedTo !== user?.id) {
      fetchAssigneeName(task.assignedTo).then(name => setAssigneeName(name));
    }
  }, [task?.assignedTo, user, user?.id]);


  const handlePickImageForChecklist = async (itemId: string) => {
    const permission = await ImagePicker1.requestMediaLibraryPermissionsAsync();
    if (!permission.granted) {
      Alert.alert('Permission Required', 'Permission to access media library is required to pick an image!');
      return;
    }

    const result = await ImagePicker1.launchImageLibraryAsync({
      mediaTypes: ImagePicker1.MediaTypeOptions.Images,
      quality: 0.7, // Slightly higher quality for checklist images
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      handleChecklistItemChange(itemId, result.assets[0].uri);
    }
  };

  if (!task) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>{t('common.notFound')}</Text>
          <Button
            title="Go Back"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Task Details',
          headerRight: () => (
            <TouchableOpacity onPress={() => setShowOptions(!showOptions)} style={styles.headerButton}>
              <MoreVertical size={24} color={colors.gray[700]} />
            </TouchableOpacity>
          ),
        }}
      />

      <SafeAreaView style={styles.container}>
        {showOptions && (
          <View style={styles.optionsMenu}>
            {/* You might want to add conditions here for who can edit/delete */}
            <TouchableOpacity style={styles.optionItem} onPress={() => {
              setShowOptions(false);
              // router.push(`/tasks/edit/${task.id}`); // Assuming an edit task route
              Alert.alert('Edit Task', 'Edit functionality not yet implemented.');
            }}>
              <Edit size={20} color={colors.gray[700]} />
              <Text style={styles.optionText}>Edit Task</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.optionItem} onPress={() => {
              setShowOptions(false);
              handleDeleteTask();
            }}>
              <Trash size={20} color={colors.danger} />
              <Text style={[styles.optionText, { color: colors.danger }]}>Delete Task</Text>
            </TouchableOpacity>
          </View>
        )}

        <ScrollView contentContainerStyle={styles.scrollContent}>

          <View style={[styles.header,]}>
            <Text style={[styles.title, isRTL && { textAlign: 'right' }]}>
              {task?.title ?? t('task.untitled')}
            </Text>

            <View style={[styles.statusContainer, isRTL && { flexDirection: 'row-reverse' }]}>
              <View
                style={[
                  styles.statusBadge,
                  {
                    backgroundColor: isTaskCompleted ? colors.success + '20' : task?.status === 'overdue' ? colors.danger + '20' : colors.primary + '20',
                  },
                ]}
              >
                <Text
                  style={[
                    styles.statusText,
                    { color: isTaskCompleted ? colors.success : task?.status === 'overdue' ? colors.danger : colors.primary, },]}
                >
                  {task?.status
                    ? t(`task.status.${task.status}`)
                    : t('task.status.unknown')}
                </Text>
              </View>

              <View style={styles.priorityBadge}>
                <Text
                  style={[
                    styles.priorityText,
                    { color: getPriorityColor(task?.priority ?? 'low') },
                  ]}
                >
                  {task?.priority
                    ? t(`task.priority.${task.priority}`)
                    : t('task.priority.low')}{' '}
                </Text>
              </View>

              {task?.frequency && task.frequency !== 'once' && (
                <View style={styles.recurringBadge}>
                  <Text style={styles.recurringText}>
                    {t(`task.frequency.${task.frequency}`)}
                  </Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.detailsContainer}>
            {task?.description ? (
              <View style={styles.descriptionContainer}>
                <Text style={[styles.descriptionText, isRTL && { textAlign: 'right' }]}>{task.description}</Text>
              </View>
            ) : null}

            <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
              <View style={styles.detailIcon}>
                <Calendar size={20} color={colors.primary} />
              </View>
              <View style={[styles.detailContent]}>
                <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('task.dueDate')}</Text>
                <Text style={[styles.detailValue, isRTL && styles.rtlTextAlign]}>
                  {task?.dueDate ? formatDueDate(task.dueDate) : t('common.na')}
                </Text>
              </View>
            </View>

            <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
              <View style={styles.detailIcon}>
                <Clock size={20} color={colors.primary} />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('tasks.dueTime')}</Text>
                <Text style={[styles.detailValue, isRTL && styles.rtlTextAlign]}>
                  {task?.dueDate ? formatDueTime(task.dueDate) : t('common.na')}
                </Text>
              </View>
            </View>

            <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
              <View style={styles.detailIcon}>
                <Flag size={20} color={colors.primary} />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('tasks.priority')}</Text>
                <Text
                  style={[
                    styles.detailValue, isRTL && styles.rtlTextAlign,
                    { color: getPriorityColor(task?.priority ?? 'low') },
                  ]}
                >
                  {task?.priority
                    ? t(`task.priority.${task.priority}`)
                    : t('task.priority.low')}
                </Text>
              </View>
            </View>

            <View style={[styles.detailItem, isRTL && { flexDirection: 'row-reverse' }]}>
              <View style={styles.detailIcon}>
                <User size={20} color={colors.primary} />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, isRTL && styles.rtlTextAlign]}>{t('task.assignedTo')}</Text>
                <Text style={[styles.detailValue, isRTL && styles.rtlTextAlign]}>
                  {task?.assignedTo
                    ? task.assignedTo === user?.id
                      ? t('task.you')
                      : assigneeName ?? t('task.unknown')
                    : t('task.unassigned')}
                </Text>
              </View>
            </View>
          </View>

          {relatedEntity?.entity?.name && (
            <View style={styles.detailItem}>
              <View style={styles.detailIcon}>
                <MapPin size={20} color={colors.primary} />
              </View>
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>
                  {t('task.related')}{" "}
                  {relatedEntity?.type
                    ? t(`entities.${relatedEntity.type}`, relatedEntity.type)
                    : t('task.entity')}
                </Text>
                <Text style={styles.detailValue}>{relatedEntity.entity.name}</Text>
              </View>
            </View>
          )}

          {/* Checklist Section */}
          {task?.checklistDetails && (
            <View style={styles.checklistContainer}>
              <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign]}>
                {task.checklistDetails.title || t('task.checklist')}
              </Text>
              {task.checklistDetails.instructions ? (
                <Text style={[styles.instructions, isRTL && { textAlign: 'right' }]}>
                  {task.checklistDetails.instructions}
                </Text>
              ) : null}

              {task.checklistDetails.items?.map((item) => (
                <View key={item.id} style={styles.checklistItem}>
                  {item.type === 'boolean' ? (
                    <View style={[styles.checklistItemContent, styles.booleanWrapper]}>
                      <Text style={styles.checklistItemTitle}>{item.label}</Text>
                      <Switch
                        value={checklistValues[item.id] ?? false} // Use local state, fallback to initial task value
                        onValueChange={(val) => handleChecklistItemChange(item.id, val)}
                        disabled={isTaskCompleted || !isAssignedToCurrentUser} // Disable if completed or not assigned
                        trackColor={{ false: colors.gray[300], true: colors.primary }}
                        thumbColor={colors.white}
                      />
                    </View>
                  ) : item.type === 'select' && item.options ? (
                    <View style={styles.checklistItemContent}>
                      <Text style={styles.checklistItemTitle}>{item.label}</Text>
                      <DropdownPicker
                        style={[
                          styles.dropdown,
                          (isTaskCompleted || !isAssignedToCurrentUser) && styles.disabledInput,
                        ]}
                        label={''}
                        options={item.options.map((option) => ({ label: option, value: option }))}
                        onSelect={(val) => handleChecklistItemChange(item.id, val)}
                        selectedValue={checklistValues[item.id] || undefined} // Use local state
                        disabled={isTaskCompleted || !isAssignedToCurrentUser}
                      />
                    </View>
                  ) : item.type === 'text' ? (
                    <View style={styles.checklistItemContent}>
                      <Text style={styles.checklistItemTitle}>{item.label}</Text>
                      <Input
                        style={[
                          styles.textInput,
                          (isTaskCompleted || !isAssignedToCurrentUser) && styles.disabledInput,
                        ]}
                        value={checklistValues[item.id] || ''} // Use local state
                        editable={!isTaskCompleted && isAssignedToCurrentUser} // Enable editing based on status and assignment
                        onChangeText={(val) => handleChecklistItemChange(item.id, val)}
                        placeholder="Enter text"
                        multiline
                      />
                    </View>
                  ) : item.type === 'image' ? (
                    <View style={styles.checklistItemContent}>
                      <Text style={styles.checklistItemTitle}>{item.label}</Text>
                      <View style={styles.imageInputContainer}>
                        {(checklistValues[item.id]) && (
                          <Image
                            source={{ uri: checklistValues[item.id] }}
                            style={styles.imagePreview}
                            resizeMode="cover"
                          />
                        )}
                        <Button
                          title={checklistValues[item.id] ? 'Change Image' : 'Pick Image'}
                          onPress={() => handlePickImageForChecklist(item.id)}
                          disabled={isTaskCompleted || !isAssignedToCurrentUser}
                          color={(isTaskCompleted || !isAssignedToCurrentUser) ? colors.gray[400] : colors.primary}
                          style={styles.pickImageButton}
                        />
                        {checklistValues[item.id] && !isTaskCompleted && isAssignedToCurrentUser && (
                          <TouchableOpacity
                            style={styles.removeImageButtonAbsolute}
                            onPress={() => handleChecklistItemChange(item.id, null)} // Set value to null to remove image
                          >
                            <Trash2 size={18} color={colors.white} />
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  ) : null}
                </View>
              ))}
            </View>
          )}

          {/* Evidence Section */}
          {task?.evidence && (
            <View style={styles.evidenceContainer}>
              <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign]}>
                {t('task.evidence')}{' '}
                {task.evidence.required && (
                  <Text style={styles.requiredText}>
                    ({t('task.required')})
                  </Text>
                )}
              </Text>

              {/* Display existing evidence images with slider */}
              {evidenceImages.length > 0 ? (
                <View style={styles.imageSliderContainer}>
                  <ImageSlider
                    images={evidenceImages}
                    height={200}
                    showIndicators={true}
                  />
                  {!isTaskCompleted && isAssignedToCurrentUser && (
                    <TouchableOpacity
                      style={styles.clearImagesButton}
                      onPress={() => setEvidenceImages([])}
                    >
                      <Text style={styles.clearImagesText}>
                        {t('task.clearImages')}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              ) : (
                <View style={styles.noEvidenceContainer}>
                  <ImageIcon size={24} color={colors.gray[400]} />
                  <Text style={styles.noEvidenceText}>
                    {isTaskCompleted
                      ? t('task.noEvidenceProvided')
                      : t('task.evidenceRequired')}
                  </Text>
                </View>
              )}

              {/* Add new evidence image picker (only if task is not completed and user is assigned) */}
              {!isTaskCompleted && isAssignedToCurrentUser && (
                <ImagePicker
                  image="" // No initial image for adding new ones
                  onImageSelected={addEvidenceImage}
                  placeholder="Add Evidence Image"
                  size={100} // Adjust size as needed for a button
                  isPreview={false}
                  iconComponent={<Camera size={40} color={colors.gray[500]} />}
                  style={styles.addImagePicker}
                />
              )}

              {/* Notes input */}
              <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign, { marginTop: 24 }]}>
                {t('task.notes')}
              </Text>
              <Input
                style={styles.notesInput}
                value={completionNotes}
                onChangeText={setCompletionNotes}
                placeholder="Add any additional notes here..."
                multiline
                numberOfLines={4}
                editable={!isTaskCompleted && isAssignedToCurrentUser} // Enable editing based on status and assignment
              />
            </View>
          )}

          <View style={styles.timelineContainer}>
            <Text style={[styles.timelineTitle, isRTL && styles.rtlTextAlign]}>{t('task.timeline')}</Text>

            <View style={styles.timelineItem}>
              <View style={styles.timelineDot} />
              <View style={styles.timelineContent}>
                <Text style={[styles.timelineEvent, isRTL && styles.rtlTextAlign]}>{t('task.created')}</Text>
                <Text style={[styles.timelineDate, isRTL && styles.rtlTextAlign]}>
                  {task?.createdAt
                    ? new Date(task.createdAt).toLocaleDateString([], {
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                    : t('common.na')}
                </Text>
              </View>
            </View>

            {task?.status === 'completed' && task?.completedAt && (
              <View style={styles.timelineItem}>
                <View
                  style={[styles.timelineDot, { backgroundColor: colors.success }]}
                />
                <View style={styles.timelineContent}>
                  <Text style={[styles.timelineEvent, isRTL && styles.rtlTextAlign]}>{t('task.completed')}</Text>
                  <Text style={[styles.timelineDate, isRTL && styles.rtlTextAlign]}>
                    {new Date(task.completedAt).toLocaleDateString([], {
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Action Button at the bottom */}
        {!isTaskCompleted && isAssignedToCurrentUser && (
          <View style={styles.actionContainer}>
            <Button
              title={isSubmitting ? <ActivityIndicator color={colors.white} /> : t('task.markAsCompleted')}
              onPress={handleCompleteTask}
              leftIcon={<CheckCircle size={20} color={colors.white} />}
              disabled={isSubmitting}
            />
          </View>
        )}

        {/* Image Viewer Modal - Remains the same */}
        <Modal
          visible={showImageModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowImageModal(false)}
        >
          <View style={styles.imageModalOverlay}>
            <TouchableOpacity
              style={styles.imageModalCloseButton}
              onPress={() => setShowImageModal(false)}
            >
              <X size={24} color={colors.white} />
            </TouchableOpacity>

            <Image
              source={{ uri: selectedImage }}
              style={styles.fullImage}
              resizeMode="contain"
            />
          </View>
        </Modal>
      </SafeAreaView >
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  instructions: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 12,
  },
  rtlDirection: {
    flexDirection: 'row-reverse'
  },
  rtlTextAlign: {
    textAlign: 'right',
    marginRight: 8

  },
  card: { // This style might be redundant if used for checklist items directly
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  label: { // This style might be redundant
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#222',
  },
  booleanWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8, // Added padding for better touch area
  },
  booleanStatus: { // This style might be redundant
    fontSize: 14,
    color: '#555',
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: '#f9f9f9',
  },
  // Removed top-level imagesContainer and related styles as they were for the modal
  // New styles for image slider
  imageSliderContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  clearImagesButton: {
    marginTop: 8,
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: colors.danger,
    borderRadius: 8,
  },
  clearImagesText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  // Legacy styles for evidence images grid (keeping for fallback)
  evidenceImagesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  evidenceImageWrapper: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
    overflow: 'hidden',
    borderWidth: 1, // Added border for clarity
    borderColor: colors.gray[300],
  },
  evidenceImage: {
    width: '100%',
    height: '100%',
  },
  removeEvidenceImageButton: { // Specific for evidence images
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: colors.danger,
    borderRadius: 12,
    padding: 4,
    zIndex: 1, // Ensure it's above the image
  },
  addImagePicker: { // Style for the ImagePicker component itself when adding new images
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
  },

  notesInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.gray[800],
    minHeight: 100,
    textAlignVertical: 'top',
    width: '100%'
  },
  textInput: {
    paddingVertical: 10,
    paddingHorizontal: 12,
    fontSize: 14,
    width:'100%',
    // borderWidth: 1, // Added border for consistency with other inputs
    // borderColor: '#ccc', // Added border for consistency
    borderRadius: 8, // Added border radius for consistency
  },
  disabledInput: {
    backgroundColor: colors.gray[100], // Lighter background for disabled
    color: colors.gray[500], // Lighter text for disabled
    // width: '100%', // Already set
  },
  imageInputContainer: { // Container for image type checklist items
    marginTop: 10,
    alignItems: 'flex-start', // Align to start for better layout
    // flexDirection: 'row', // Consider if you want button next to image
    // flexWrap: 'wrap',
  },
  imagePreview: {
    width: 150,
    height: 150,
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  pickImageButton: {
    // Custom styles for the pick image button within checklist
    marginTop: 8,
  },
  removeImageButtonAbsolute: { // Specific for checklist image items
    position: 'absolute',
    top: 8, // Adjust as needed
    left: 130, // Adjust as needed to position over the image
    backgroundColor: colors.danger,
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionsMenu: {
    position: 'absolute',
    top: 50, // Adjust based on header height
    right: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 8,
    zIndex: 10,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  optionText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 80, // Ensure space for the action button
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.gray[600],
    marginBottom: 20,
  },
  backButton: {
    width: 120,
  },
  header: {
    marginBottom: 24,
  },
  priorityBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: colors.gray[100],
    marginLeft: 8, // Spacing from status badge
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  recurringBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: colors.info + '20',
    marginLeft: 8, // Spacing from priority badge
  },
  recurringText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.info,
  },
  detailsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  descriptionContainer: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.gray[700],
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  checklistContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  checklistItem: {
    // This now acts as a container for each checklist item's render
    // It's good that it has padding/borderBottom to separate items visually
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  checklistItemContent: {
    flex: 1,
  },
  checklistItemTitle: {
    fontSize: 16,
    color: colors.gray[800],
    marginBottom: 8, // Spacing for input fields below
  },
  checklistItemDescription: { // If you add descriptions for checklist items
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  requiredText: {
    fontSize: 12,
    color: colors.danger,
  },
  evidenceContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  noEvidenceContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    marginBottom: 16,
  },
  noEvidenceText: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 8,
    textAlign: 'center',
  },
  timelineContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
    position: 'relative',
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.primary,
    marginTop: 4,
    marginRight: 12,
  },
  timelineContent: {
    flex: 1,
  },
  timelineEvent: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 4,
  },
  timelineDate: {
    fontSize: 12,
    color: colors.gray[500],
  },
  actionContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    shadowColor: colors.gray[600], // Add shadow for floating effect
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  imageModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageModalCloseButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
    padding: 8,
  },
  fullImage: {
    width: '100%',
    height: '80%',
  },
});
