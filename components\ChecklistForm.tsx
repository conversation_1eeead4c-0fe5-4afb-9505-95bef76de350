
import React, { useState } from 'react';
import { View, Text, Button, ScrollView, Image, StyleSheet, Switch, TouchableOpacity } from 'react-native';
// import { Picker } from '@react-native-picker/picker';
import DropdownPicker from './DropdownPicker';
import * as ImagePicker1 from 'expo-image-picker';
import { KeyboardAvoidingView, Platform } from 'react-native';

import Input from './Input';
import { Trash2 } from 'lucide-react-native';
import ImagePicker from './ImagePicker';
import colors from '@/constants/colors';

type ChecklistItem = {
  id: string;
  label: string;
  type: 'boolean' | 'select' | 'text' | 'image';
  requireImages?: boolean;
  options?: string[];
  value?: any; // Add this field for saving user value

};

type ChecklistData = {
  title: string;
  instructions: string;
  items: ChecklistItem[];
  [key: string]: any;
};

type Props = {
  checklist: ChecklistData;
  onSave: (updatedChecklist: ChecklistData) => void;
  requireImages: false;
  addEvidenceImage?: any
};

const ChecklistForm: React.FC<Props> = ({ checklist, onSave, requireImages = false, addEvidenceImage }) => {
  const [values, setValues] = useState<Record<string, any>>({});
  const [images, setImages] = useState<string[]>([]);
  const [notes, setNotes] = useState('');

  // console.log({checklist},"component")
  const handleChange = (itemId: string, value: any) => {
    setValues((prev) => ({ ...prev, [itemId]: value }));
  };
  const handlePickImage = async (itemId: string) => {
    const permission = await ImagePicker1.requestMediaLibraryPermissionsAsync();
    if (!permission.granted) {
      alert('Permission to access media library is required!');
      return;
    }

    const result = await ImagePicker1.launchImageLibraryAsync({
      mediaTypes: ImagePicker1.MediaTypeOptions.Images,
      quality: 0.5,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      handleChange(itemId, result.assets[0].uri);
    }
  };

  const addImage = (uri: string) => {
    setImages([...images, uri]);
    if (addEvidenceImage) addEvidenceImage(uri);
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };
  const handleSave = () => {
    // Merge values into the items
    const updatedItems = checklist.items.map((item) => ({
      ...item,
      value: values[item.id] ?? item.value ?? null,
    }));

    // Return full checklist object with updated items
    const updatedChecklist = {
      ...checklist,
      items: updatedItems,
      updatedAt: new Date(), // Optional: update timestamp
    };

    // images, notes);
    onSave(updatedChecklist, images, notes);
  };

  return (
    <>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
       <ScrollView contentContainerStyle={styles.scrollContent} style={styles.container}>
        {checklist && (
          <>
            <Text style={styles.title}>{checklist.title}</Text>
            <Text style={styles.instructions}>{checklist.instructions}</Text>

            {(checklist?.items||[]).map((item) => (
              <View key={item.id} style={styles.card}>
                <Text style={styles.cardTitle}>{item.label}</Text>

                {item.type === 'boolean' && (
                  <View style={styles.switchRow}>
                    <Text style={styles.switchLabel}>{values[item.id] ? 'Yes' : 'No'}</Text>
                    <Switch
                      value={!!values[item.id]}
                      onValueChange={(val) => handleChange(item.id, val)}
                    />
                  </View>
                )}

                {item.type === 'select' && item.options && (
                  <DropdownPicker
                    label=""
                    options={item.options.map((opt) => ({ label: opt, value: opt }))}
                    onSelect={(val) => handleChange(item.id, val)}
                    selectedValue={values[item.id]}
                  />
                )}

                {item.type === 'text' && (
                  <Input
                    style={styles.input}
                    value={values[item.id] || ''}
                    onChangeText={(val) => handleChange(item.id, val)}
                    placeholder="Type here..."
                  />
                )}

                {item.type === 'image' && (
                  <View style={styles.imageSection}>
                    {values[item.id] && (
                      <Image source={{ uri: values[item.id] }} style={styles.imagePreview} />
                    )}
                    <TouchableOpacity
                      onPress={() => handlePickImage(item.id)}
                      style={styles.imagePickerButton}
                    >
                      <Text style={styles.imagePickerText}>
                        {values[item.id] ? 'Change Image' : 'Pick Image'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            ))}

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                Evidence Images{' '}
                {requireImages && <Text style={styles.requiredText}>(Required)</Text>}
              </Text>

              <View style={styles.imagesContainer}>
                {images.map((image, index) => (
                  <View key={index} style={styles.imageWrapper}>
                    <ImagePicker
                      image={image}
                      onImageSelected={() => {}}
                      size={100}
                      editable={false}
                    />
                    <TouchableOpacity
                      style={styles.removeImageButton}
                      onPress={() => removeImage(index)}
                    >
                      <Trash2 size={16} color={colors.white} />
                    </TouchableOpacity>
                  </View>
                ))}

                <ImagePicker
                  image=""
                  onImageSelected={addImage}
                  placeholder="Add Image"
                  size={200}
                  isPreview={false}
                />
              </View>
            </View>

            <Text style={styles.sectionTitle}>Notes</Text>
            <Input
              style={styles.notesInput}
              value={notes}
              onChangeText={setNotes}
              placeholder="Add any additional notes here..."
              multiline
              numberOfLines={4}
            />
          </>
        )}
      </ScrollView>

        <View style={styles.footer}>
          <Button color={colors.primary} title="Save" onPress={handleSave} />
        </View>
      </KeyboardAvoidingView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 120, // space for footer
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: -2 },
    shadowRadius: 6,
    elevation: 10,
  },

  // Optional: minor improvements
  card: {
    backgroundColor: '#fefefe',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: colors.gray[800],
  },

  imagePickerButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  imagePickerText: {
    color: '#fff',
    fontWeight: '600',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 12,
  },
  instructions: {
    fontSize: 14,
    fontStyle: 'italic',
    color: colors.gray[600],
    marginBottom: 20,
  },

  // card: {
  //   backgroundColor: '#f9f9f9',
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 16,
  //   shadowColor: '#000',
  //   shadowOpacity: 0.05,
  //   shadowOffset: { width: 0, height: 2 },
  //   shadowRadius: 4,
  //   elevation: 2,
  // },
  // cardTitle: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   marginBottom: 12,
  //   color: colors.gray[800],
  // },
  input: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    color: colors.gray[800],
    backgroundColor: '#fff',
    width: '100%'
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  switchLabel: {
    fontSize: 14,
    color: colors.gray[600],
  },

  imageSection: {
    marginTop: 8,
    alignItems: 'flex-start',
  },
  imagePreview: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginBottom: 10,
  },
  // imagePickerButton: {
  //   paddingVertical: 10,
  //   paddingHorizontal: 14,
  //   backgroundColor: colors.primary,
  //   borderRadius: 6,
  // },
  // imagePickerText: {
  //   color: '#fff',
  //   fontSize: 14,
  //   fontWeight: '500',
  // },

  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 12,
  },
  requiredText: {
    fontSize: 12,
    color: colors.danger,
    marginLeft: 4,
  },

  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 8,
  },
  imageWrapper: {
    position: 'relative',
  },
  removeImageButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: colors.danger,
    borderRadius: 12,
    padding: 4,
    zIndex: 10,
  },

  notesInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.gray[800],
    minHeight: 100,
    textAlignVertical: 'top',
    backgroundColor: '#fff',
    width: '100%'
  },

  saveButtonContainer: {
    marginTop: 24,
    paddingVertical: 12,
  },
});

export default ChecklistForm;
