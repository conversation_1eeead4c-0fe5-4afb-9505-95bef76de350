import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { Sun, Cloud, CloudRain, CloudLightning } from 'lucide-react-native';

interface WeatherCardProps {
  date: Date;
  temperature: number;
  condition: string;
  humidity?: number;
  windSpeed?: number;
}

export const WeatherCard: React.FC<WeatherCardProps> = ({ 
  date, 
  temperature, 
  condition, 
  humidity, 
  windSpeed 
}) => {
  const getWeatherIcon = (condition: string, size = 24) => {
    switch (condition) {
      case 'sunny':
        return <Sun size={size} color={colors.warning} />;
      case 'cloudy':
        return <Cloud size={size} color={colors.gray[500]} />;
      case 'rainy':
        return <CloudRain size={size} color={colors.info} />;
      case 'stormy':
        return <CloudLightning size={size} color={colors.warning} />;
      default:
        return <Sun size={size} color={colors.warning} />;
    }
  };
  
  const formatDay = (date: Date) => {
    const today = new Date();
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    }
    
    // Get day name (<PERSON>, <PERSON><PERSON>, etc.)
    return date.toLocaleDateString([], { weekday: 'short' });
  };
  
  return (
    <View style={styles.card}>
      <Text style={styles.day}>{formatDay(date)}</Text>
      <View style={styles.iconContainer}>
        {getWeatherIcon(condition, 32)}
      </View>
      <Text style={styles.temperature}>{temperature}°</Text>
      {humidity !== undefined && (
        <Text style={styles.detail}>Humidity: {humidity}%</Text>
      )}
      {windSpeed !== undefined && (
        <Text style={styles.detail}>Wind: {windSpeed} km/h</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginRight: 12,
    width: 100,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  day: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  iconContainer: {
    marginBottom: 8,
  },
  temperature: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 4,
  },
  detail: {
    fontSize: 12,
    color: colors.gray[600],
    marginTop: 4,
  },
});

export default WeatherCard;