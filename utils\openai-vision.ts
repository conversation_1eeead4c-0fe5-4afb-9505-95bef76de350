import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';

/**
 * Analyzes an image using OpenAI's Vision API
 * @param imageUri The URI of the image to analyze
 * @param prompt The prompt to guide the AI analysis
 * @returns A JSON string with the analysis result, or null if it fails.
 */
export const analyzeImageWithVision = async (imageUri: string, prompt: string): Promise<string | null> => {
  // IMPORTANT: Replace with your actual OpenAI API key, preferably from a secure environment variable.
  const apiKey = '********************************************************************************************************************************************************************'; // process.env.EXPO_PUBLIC_OPENAI_API_KEY;

  if (!apiKey) {
    console.error("OpenAI API key is not set.");
    throw new Error("OpenAI API key is missing. Please configure it properly.");
  }

  try {
    const base64Image = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    const payload = {
      model: "gpt-4o", // Use the latest vision-capable model
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: { url: `data:image/jpeg;base64,${base64Image}` },
            },
          ],
        },
      ],
      max_tokens: 500,
      response_format: { type: "json_object" }, // Enforce JSON output
    };

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("OpenAI API Error:", responseData);
      throw new Error(responseData.error?.message || "Failed to analyze image with AI.");
    }

    return responseData.choices[0].message.content;
  } catch (error) {
    console.error('Error analyzing image:', error);
    return null;
  }
};

/**
 * Analyzes an animal's health condition from an image
 * @param imageUri The URI of the image to analyze
 * @returns A health assessment object
 */
export const analyzeAnimalHealth = async (imageUri: string): Promise<any> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Return mock health analysis
    return {
      healthStatus: 'good',
      conditions: [],
      recommendations: [
        "Regular vaccination schedule should be maintained",
        "Continue with current feeding regimen",
        "Schedule routine check-up in 3 months"
      ],
      details: "The animal appears to be in good health with normal posture and alertness. Coat/skin condition is good with no visible lesions or abnormal hair loss. Eyes are clear and bright. No signs of respiratory distress or abnormal discharge. Body condition score is appropriate for the species and age."
    };
  } catch (error) {
    console.error('Error analyzing animal health:', error);
    return null;
  }
};

/**
 * Analyzes plant health and identifies potential diseases
 * @param imageUri The URI of the image to analyze
 * @returns A plant health assessment object
 */
export async function analyzePlantHealth(imageUrl: string) {
  const prompt = `You are an expert agriculturalist and botanist AI. Your task is to analyze the provided image of a plant and return a detailed health assessment.

Analyze the image and provide the following information in a structured JSON format. Do not include any text outside of the JSON object.

The JSON object should have the following keys:
- "healthStatus": A single word describing the overall health. Choose from: "excellent", "good", "fair", "poor", "diseased".
- "conditions": An array of strings identifying any visible issues. Examples: "powdery_mildew", "aphids", "nutrient_deficiency", "overwatering_signs", "sun_scald". If no issues are found, return an empty array.
- "recommendations": An array of short, actionable recommendation strings. Examples: "increase_watering", "apply_pesticide", "add_nitrogen_fertilizer", "provide_more_sunlight", "prune_dead_leaves".
- "details": A concise, 2-3 sentence summary of your findings and recommendations.

Example of a valid JSON response:
{
  "healthStatus": "fair",
  "conditions": ["powdery_mildew", "nutrient_deficiency"],
  "recommendations": ["apply_fungicide", "add_nitrogen_fertilizer"],
  "details": "The plant appears to be suffering from a mild case of powdery mildew on its lower leaves and shows signs of nitrogen deficiency (yellowing). It is recommended to apply a suitable fungicide and a nitrogen-rich fertilizer."
}

Now, analyze the following image.`;

  try {
    // Assuming you have a generic function that sends the image and prompt to the AI
    const analysisJson = await analyzeImageWithVision(imageUrl, prompt);
    if (analysisJson) {
      // The AI response is parsed into a usable JavaScript object
      return JSON.parse(analysisJson);
    }
    return null;
  } catch (error) {
    console.error('Error parsing plant health analysis from AI:', error);
    throw new Error('Failed to parse AI response for plant health.');
  }
}

/**
 * Analyzes equipment condition and identifies maintenance needs
 * @param imageUri The URI of the image to analyze
 * @returns An equipment assessment object
 */
export const analyzeEquipmentCondition = async (imageUri: string): Promise<any> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Return mock equipment condition analysis
    return {
      condition: 'good',
      maintenanceNeeds: [
        {
          component: "Hydraulic hoses",
          issue: "Minor wear",
          urgency: "Low",
          recommendation: "Monitor for further deterioration"
        },
        {
          component: "Air filter",
          issue: "Dirty",
          urgency: "Medium",
          recommendation: "Replace within next 50 operating hours"
        }
      ],
      estimatedLifeRemaining: "3-5 years with proper maintenance",
      details: "The equipment appears to be in good overall condition with normal wear for its age. All major components look intact and functional. Some minor rust is visible on non-critical parts. Tires show approximately 70% tread remaining. Control mechanisms appear to be in working order."
    };
  } catch (error) {
    console.error('Error analyzing equipment condition:', error);
    return null;
  }
};

/**
 * Analyzes soil condition from an image
 * @param imageUri The URI of the image to analyze
 * @returns A soil assessment object
 */
export const analyzeSoilCondition = async (imageUri: string): Promise<any> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Return mock soil condition analysis
    return {
      soilType: "Clay loam",
      moisture: "Adequate",
      organicMatter: "Medium",
      possibleIssues: [
        {
          issue: "Slight compaction",
          recommendation: "Consider aeration or adding organic matter"
        }
      ],
      cropSuitability: [
        "Wheat",
        "Corn",
        "Soybeans",
        "Alfalfa"
      ],
      details: "The soil appears to be a clay loam with moderate organic matter content. Moisture level is adequate for most crops. Some signs of compaction are visible which may restrict root growth. Color indicates good fertility but soil testing is recommended for precise nutrient levels. No visible signs of erosion or severe degradation."
    };
  } catch (error) {
    console.error('Error analyzing soil condition:', error);
    return null;
  }
};


//  function
export const getPlantCareAdvice = async (species: string, variety = '') => {
  const prompt = `
You are a horticulture expert. Based on the following plant information, give short, practical advice for farmers about these 4 topics: Watering, Sunlight, Pest Control, and Pruning.

Plant species: ${species}
${variety ? `Variety: ${variety}` : ''}

Format your response as a JSON object with keys: watering, sunlight, pest_control, pruning.
Keep the advice concise and region-neutral.
  `.trim();

   const apiKey = '********************************************************************************************************************************************************************'; // process.env.EXPO_PUBLIC_OPENAI_API_KEY;

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    }),
  });

  const data = await response.json();

  const content = data.choices?.[0]?.message?.content || '';

  // Extract only JSON part
  const jsonStart = content.indexOf('{');
  const jsonEnd = content.lastIndexOf('}');
  const jsonText = content.slice(jsonStart, jsonEnd + 1);

  return JSON.parse(jsonText);
}

// Example usage
// getPlantCareAdvice("Tomato", "Cherry Tomato").then(console.log).catch(console.error);
