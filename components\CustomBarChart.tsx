import React from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView } from 'react-native';

const CustomBarChart = ({ labels, income, expense, barColors }) => {
  const chartHeight = 200;
  const maxY = Math.max(...income, ...expense, 1000); // Safe default

  const barWidth = 20;
  const groupSpacing = 40;
  const chartWidth = labels.length * (barWidth * 2 + groupSpacing);

  const getBarHeight = (value) => (value / maxY) * chartHeight;

  const yAxisTicks = [0, maxY * 0.25, maxY * 0.5, maxY * 0.75, maxY];

  return (
    <View style={styles.container}>
      <View style={styles.yAxis}>
        {yAxisTicks.reverse().map((tick, i) => (
          <Text key={i} style={styles.yAxisLabel}>
            Rs {Math.round(tick)}
          </Text>
        ))}
      </View>

      <ScrollView horizontal contentContainerStyle={{ paddingBottom: 50 }}>
        <View style={[styles.chartArea, { height: chartHeight }]}>
          {/* Y-Axis grid lines */}
          {yAxisTicks.map((_, i) => (
            <View key={i} style={styles.gridLine} />
          ))}

          {/* Bars */}
          <View style={styles.barGroupsContainer}>
            {labels.map((label, i) => (
              <View key={i} style={styles.barGroup}>
                <View style={styles.barContainer}>
                  <View
                    style={[
                      styles.bar,
                      { height: getBarHeight(expense[i]), backgroundColor: barColors[1] },
                    ]}
                  />
                  <View
                    style={[
                      styles.bar,
                      { height: getBarHeight(income[i]), backgroundColor: barColors[0] },
                    ]}
                  />
                </View>
                <Text style={styles.xAxisLabel}>{label}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default CustomBarChart;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 10,
  },
  yAxis: {
    width: 50,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginRight: 4,
   
  },
  yAxisLabel: {
    fontSize: 10,
    color: '#555',
     transform:"rotate(-45deg)",
     marginBottom:30
  },
  chartArea: {
    position: 'relative',
    flexDirection: 'column',
  },
  gridLine: {
    position: 'absolute',
    width: '100%',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    top: '0%',
  },
  barGroupsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  barGroup: {
    alignItems: 'center',
    marginHorizontal: 2,
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: 200,
  },
  bar: {
    width: 10,
    marginHorizontal: 1,
    borderRadius: 2,
  },
  xAxisLabel: {
    marginTop: 15,
    fontSize: 10,
    color: '#333',
    transform: [{ rotate: '-45deg' }],
  },
});
