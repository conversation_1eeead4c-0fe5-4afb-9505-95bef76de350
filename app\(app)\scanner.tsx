import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  SafeAreaView,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { X, CheckCircle, AlertCircle } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';
import Button from '@/components/Button';
import { parseUniversalLink } from '@/utils/qrcode';

export default function ScannerScreen() {
  const { t } = useTranslation();
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<string>('back');
  const [scanned, setScanned] = useState(false);
  const [scanning, setScanning] = useState(false);
  const [scanResult, setScanResult] = useState<{
    itemType: string;
    itemId: string;
  } | null>(null);
  
  useEffect(() => {
    if (!permission) {
      requestPermission();
    }
  }, [permission, requestPermission]);
  
  const handleBarCodeScanned = ({ data }) => {
    if (scanned || scanning) return;
    
    setScanning(true);
    
    try {
      // Parse the QR code data
      const result = parseUniversalLink(data);
      if (result) {
        setScanned(true);
        setScanResult(result);
        
        // You can also auto-navigate if preferred
        // const { itemType, itemId } = result;
        // router.push(`/${itemType}/${itemId}`);
      } else {
        Alert.alert(
          t('scanner.invalidQR'),
          t('scanner.invalidQRMessage'),
          [{ text: t('scanner.tryAgain'), onPress: () => setScanning(false) }]
        );
      }
    } catch (error) {
      console.error('QR code parsing error:', error);
      Alert.alert(
        t('scanner.invalidQR'),
        t('scanner.invalidQRMessage'),
        [{ text: t('scanner.tryAgain'), onPress: () => setScanning(false) }]
      );
    }
  };
  
  const handleViewDetails = () => {
    if (!scanResult) return;
    
    const { itemType, itemId } = scanResult;
    
    // Navigate to the appropriate details page
    router.push(`/${itemType}/${itemId}`);
  };
  
  const handleScanAnother = () => {
    setScanned(false);
    setScanResult(null);
    setScanning(false);
  };
  
  const handleClose = () => {
    router.back();
  };
  
  if (!permission) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen 
          options={{ 
            title: t('scanner.title'),
            headerLeft: () => (
              <TouchableOpacity onPress={handleClose}>
                <X size={24} color={colors.gray[800]} />
              </TouchableOpacity>
            ),
          }} 
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }
  
  if (!permission.granted) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen 
          options={{ 
            title: t('scanner.title'),
            headerLeft: () => (
              <TouchableOpacity onPress={handleClose}>
                <X size={24} color={colors.gray[800]} />
              </TouchableOpacity>
            ),
          }} 
        />
        <View style={styles.permissionContainer}>
          <AlertCircle size={60} color={colors.warning} style={styles.permissionIcon} />
          <Text style={styles.permissionText}>{t('scanner.permissionNeeded')}</Text>
          <Button
            title={t('scanner.grantPermission')}
            onPress={requestPermission}
            style={styles.permissionButton}
          />
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: t('scanner.title'),
          headerLeft: () => (
            <TouchableOpacity onPress={handleClose}>
              <X size={24} color={colors.gray[800]} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      {!scanned ? (
        <View style={styles.cameraContainer}>
          <CameraView
            style={styles.camera}
            facing={facing}
            barcodeScannerSettings={{
              barcodeTypes: ['qr'],
            }}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          >
            <View style={styles.overlay}>
              <View style={styles.scannerFrame} />
              
              <Text style={styles.scannerText}>
                {scanning ? t('scanner.processing') : t('scanner.alignQRCode')}
              </Text>
            </View>
          </CameraView>
        </View>
      ) : (
        <View style={styles.resultContainer}>
          <View style={styles.resultHeader}>
            <CheckCircle size={60} color={colors.success} style={styles.resultIcon} />
            <Text style={styles.resultTitle}>{t('scanner.scanSuccess')}</Text>
          </View>
          
          <View style={styles.resultDetails}>
            <View style={styles.resultItem}>
              <Text style={styles.resultItemLabel}>{t('scanner.itemType')}</Text>
              <Text style={styles.resultItemValue}>{scanResult?.itemType}</Text>
            </View>
            
            <View style={styles.resultItem}>
              <Text style={styles.resultItemLabel}>{t('scanner.itemId')}</Text>
              <Text style={styles.resultItemValue}>{scanResult?.itemId}</Text>
            </View>
          </View>
          
          <View style={styles.resultActions}>
            <Button
              title={t('scanner.viewDetails')}
              onPress={handleViewDetails}
              style={styles.viewDetailsButton}
            />
            
            <Button
              title={t('scanner.scanAnother')}
              onPress={handleScanAnother}
              style={styles.scanAnotherButton}
              textStyle={styles.scanAnotherButtonText}
              variant="outline"
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionIcon: {
    marginBottom: 20,
  },
  permissionText: {
    fontSize: 16,
    color: colors.gray[700],
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    width: '100%',
    maxWidth: 300,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: colors.white,
    backgroundColor: 'transparent',
    marginBottom: 20,
  },
  scannerText: {
    color: colors.white,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  resultContainer: {
    flex: 1,
    padding: 20,
  },
  resultHeader: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  resultIcon: {
    marginBottom: 16,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    textAlign: 'center',
  },
  resultDetails: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
  },
  resultItem: {
    marginBottom: 16,
  },
  resultItemLabel: {
    fontSize: 14,
    color: colors.gray[500],
    marginBottom: 4,
  },
  resultItemValue: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  resultActions: {
    gap: 12,
  },
  viewDetailsButton: {
    marginBottom: 12,
  },
  scanAnotherButton: {
    borderColor: colors.primary,
  },
  scanAnotherButtonText: {
    color: colors.primary,
  },
});

