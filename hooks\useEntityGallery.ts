import { useState, useEffect, useCallback } from 'react';
import { GalleryService, GalleryPhoto } from '@/services/galleryService';
import { useFarmStore } from '@/store/farm-store';
import { Alert } from 'react-native';
import { useTranslation } from '@/i18n/useTranslation';

interface UseEntityGalleryProps {
  entityId: string;
  entityType: string;
  autoLoad?: boolean;
}

export const useEntityGallery = ({ 
  entityId, 
  entityType, 
  autoLoad = true 
}: UseEntityGalleryProps) => {
  const { t } = useTranslation();
  const { currentFarm } = useFarmStore();
  
  const [photos, setPhotos] = useState<GalleryPhoto[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load images for the entity
  const loadImages = useCallback(async () => {
    // Don't load if entityId is empty or invalid
    if (!currentFarm?.id || !entityId || entityId === '') return;
    
    try {
      setLoading(true);
      setError(null);
      
      const images = await GalleryService.loadEntityImages(
        currentFarm.id,
        entityId,
        entityType
      );
      
      setPhotos(images);
    } catch (err) {
      console.error('Error loading images:', err);
      setError(t('gallery.loadError'));
    } finally {
      setLoading(false);
    }
  }, [currentFarm?.id, entityId, entityType, t]);

  // Add new image
  const addImage = useCallback(async (
    imageUri: string,
    metadata?: { takenBy?: string; description?: string }
  ) => {
    if (!currentFarm?.id || !entityId || entityId === '') return null;
    
    try {
      setUploading(true);
      setError(null);
      
      const newPhoto = await GalleryService.addImage(
        currentFarm.id,
        imageUri,
        entityId,
        entityType,
        metadata
      );
      
      setPhotos(prev => [newPhoto, ...prev]);
      return newPhoto;
    } catch (err) {
      console.error('Error adding image:', err);
      setError(t('gallery.addError'));
      throw err;
    } finally {
      setUploading(false);
    }
  }, [currentFarm?.id, entityId, entityType, t]);

  // Delete image
  const deleteImage = useCallback(async (photoId: string) => {
    if (!currentFarm?.id || !entityId || entityId === '') return;
    
    try {
      await GalleryService.deleteImage(currentFarm.id, photoId);
      setPhotos(prev => prev.filter(photo => photo.id !== photoId));
    } catch (err) {
      console.error('Error deleting image:', err);
      setError(t('gallery.deleteError'));
      throw err;
    }
  }, [currentFarm?.id, t]);

  // Update image metadata
  const updateImageMetadata = useCallback(async (
    photoId: string,
    metadata: { description?: string; takenBy?: string }
  ) => {
    if (!currentFarm?.id || !entityId || entityId === '') return;
    
    try {
      await GalleryService.updateImageMetadata(currentFarm.id, photoId, metadata);
      
      setPhotos(prev => prev.map(photo => 
        photo.id === photoId 
          ? { ...photo, ...metadata }
          : photo
      ));
    } catch (err) {
      console.error('Error updating image metadata:', err);
      setError(t('gallery.updateError'));
      throw err;
    }
  }, [currentFarm?.id, t]);

  // Auto-load images when component mounts or dependencies change
  useEffect(() => {
    if (autoLoad && entityId && entityId !== '') {
      loadImages();
    }
  }, [loadImages, autoLoad, entityId]);

  return {
    photos,
    loading,
    uploading,
    error,
    loadImages,
    addImage,
    deleteImage,
    updateImageMetadata,
    refresh: loadImages,
  };
};
