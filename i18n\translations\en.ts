import { Transaction } from "firebase/firestore";

export const en = {
  login: {
    welcomeBack: "Welcome Back",
    signInToContinue: "Sign in to continue",
    email: "Email",
    enterEmail: "Enter your email",
    password: "Password",
    enterPassword: "Enter your password",
    forgotPassword: "Forgot Password?",
    signIn: "Sign In",
    signingIn: "Signing In...",
    dontHaveAccount: "Don't have an account?",
    signUp: "Sign Up",
    emailRequired: "Email is required",
    invalidEmail: "Please enter a valid email",
    passwordRequired: "Password is required",
    passwordTooShort: "Password must be at least 6 characters",
    demoAccounts: "Demo Accounts",
    adminDemo: "Admin Demo",
    farmerDemo: "Farmer Demo",
    workerDemo: "Worker Demo",
    tagline: "Smart Farming Solutions",
    emailVerificationRequired: "Please verify your email before logging in",
    verificationEmailSent: "Verification email sent. Please check your inbox."
  },
  "entity_types": {
    "Animal": "Animal",
    "Crop": "Crop",
    "Field": "Field",
    "Garden": "Garden",
    "Plant": "Plant",
    "Equipment": "Equipment"
  },
  "categories": {
    "feed": "Feed",
    "treatment": "Treatment",
    "equipment_maintenance": "Equipment Maintenance",
    "medicine": "Medicine",
    "transport": "Transport",
    "seeds": "Seeds",
    "fertilizer": "Fertilizer",
    "labor": "Labor",
    "harvest": "Harvest",
    "sales": "Sales",
    "other": "Other"
  },

  forgotPassword: {
    forgotPassword: "Forgot Password",
    enterEmail: "Enter your email address to reset your password",
    email: "Email",
    enterYourEmail: "Enter your email",
    resetPassword: "Reset Password",
    sending: "Sending...",
    rememberPassword: "Remember your password? Sign In",
    emailRequired: "Email is required",
    invalidEmail: "Please enter a valid email",
    checkYourEmail: "Check Your Email",
    resetLinkSent: "We have sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.",
    backToLogin: "Back to Login"
  },
  scanner: {
    title: "QR Scanner",
    alignQRCode: "Align QR code within the frame",
    processing: "Processing QR code...",
    scanSuccess: "QR Code Scanned Successfully",
    itemType: "Item Type",
    itemId: "Item ID",
    viewDetails: "View Item Details",
    scanAnother: "Scan Another Code",
    invalidQR: "Invalid QR Code",
    invalidQRMessage: "The scanned QR code does not contain valid farm item information.",
    tryAgain: "Try Again",
    noQRFound: "No QR Code Found",
    noQRFoundMessage: "No QR code was detected in the selected image.",
    itemNotFound: "Item Not Found",
    itemNotFoundMessage: "The {itemType} with ID {itemId} was not found in your farm.",
    permissionNeeded: "We need camera permission to scan QR codes",
    grantPermission: "Grant Permission",
    openScanner: "Open Scanner",
    uploadFromGallery: "Upload from Gallery",
    recentScans: "Recent Scans",
    noRecentScans: "No recent scans",
    scanDescription: "Scan QR codes to quickly access information about plants, animals, equipment, and more on your farm.",
    infoText: "QR codes help you track and manage your farm assets efficiently. Each plant, animal, and equipment has a unique QR code.",
    flipCamera: "Flip Camera",
    manualEntry: "Manual Entry",
    scanningTips: "Make sure the QR code is well-lit and centered in the frame.",
    scanComplete: "Scan Complete",
    scanFailed: "Scan Failed",
    retryScanning: "Retry Scanning",
    scanHistory: "Scan History",
    scanDate: "Scan Date",
    scannedBy: "Scanned By",
    scanLocation: "Location",
    scanDetails: "Scan Details",
    toggleFlash: "Toggle Flash",
    cancel: "Cancel",
    scanningFor: "Scanning for farm assets...",
    scanningInstructions: "Point camera at a QR code on any farm asset",
    torchOn: "Torch On",
    torchOff: "Torch Off",
  },
  qrCode: {
    QRCode: "QR Code",
    save: "Save",
    share: "Share",
    viewQRCode: "View QR Code",
    saveSuccess: "QR code saved to your device.",
    shareTitle: "Share QR Code",
    shareMessage: "Scan this QR code to view details about {itemType}: {itemName}",
    shareSuccess: "QR code shared successfully.",

    shareFailed: "Failed to share QR code.",

    quickAccess: "Scan this QR code to quickly access this garden's information.",

    information: "Scan this QR code to quickly access information about."
  },
  common: {
    // "cancel": "Can
    // cel",
    "Optional": "Optional",
    notes: "Notes",
    "add": "Add",
    details: "Details",
    save: "Save",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    view: "View",
    loading: "Loading...",
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Info",
    confirm: "Confirm",
    back: "Back",
    next: "Next",
    done: "Done",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    all: "All",
    none: "None",
    yes: "Yes",
    no: "No",
    ok: "OK",
    close: "Close",
    // details: "Details",
    selectUnit: "Select Unit",
    notSet: "Not set",
    seeAll: "See All",
    quickActions: "Quick Actions",
    "unitMarlaPK": "Marla (PK)",
    "unitKanalPK": "Kanal (PK)",
    "unitAcrePK": "Acre (PK)",
    "unitSqFt": "Square Foot (ft²)",
    "unitSqYd": "Square Yard (yd²)",
    "unitAcreUS": "Acre (US)",
    "unitSqMile": "Square Mile",
    "notSpecified": "Not specified",
    "notAssigned": "Not assigned",
    "unknown": "Unknown",
    markInactive: "Mark As Inactive",
    fillAllFields: "Please fill all required fields",
    areaUnit: {
      "Marla": "Marla",
      "Square Foot": "Square Foot",
      "Acre": "Acre",
      "Square Yard": "Square Yard",
      "Kanal": "Kanal"

    }
  },
  tabs: {
    home: "Home",
    tasks: "Tasks",
    scanner: "Scanner",
    reports: "Reports",
    profile: "Profile",
    map: "Map"
  },
  profile: {
    title: "Profile",
    editProfile: "Edit Profile",
    language: "Language",
    notifications: "Notifications",
    security: "Security",
    accountSettings: "Account Settings",
    helpSupport: "Help & Support",
    logout: "Logout",
    totalTasks: "Total Tasks",
    tasksDone: "Tasks Done",
    fields: "Fields",
    recentActivity: "Recent Activity",
    noRecentActivity: "No recent activity",
    selectLanguage: "Select Language",
    close: "Close",
    changePassword: "Change Password",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmNewPassword: "Confirm New Password",
    passwordRequirements: "Password Requirements:",
    atLeast8Chars: "At least 8 characters",
    containsNumber: "Contains a number",
    containsSpecialChar: "Contains a special character",
    passwordChanged: "Password changed successfully",
    passwordError: "Failed to change password",
    currentPasswordRequired: "Current password is required",
    newPasswordRequired: "New password is required",
    passwordsDoNotMatch: "New passwords do not match",
    farmAdministrator: "Farm Administrator",
    farmManager: "Farm Manager",
    farmCaretaker: "Farm Caretaker"
  },
  add: {
    plant: "Add Plant",
    animal: "Add Animal",
    field: "Add Field",
    garden: "Add Garden",
    equipment: "Add Equipment",
    yield: "Add Yield",
    task: "Add Task"
  },
  tasks: {
    title: "Tasks",
    all: "All",
    dueTime: "Due Time",
    pending: "Pending",
    inProgress: "In Progress",
    completed: "Completed",
    overdue: "Overdue",
    cancelled: "Cancelled",
    assigned: "Assigned",
    created: "Created",
    highPriority: "High Priority",
    mediumPriority: "Medium Priority",
    lowPriority: "Low Priority",
    dueDate: "Due Date",
    assignedTo: "Assigned To",
    description: "Description",
    status: "Status",
    priority: "Priority",
    createTask: "Create Task",
    editTask: "Edit Task",
    deleteTask: "Delete Task",
    markAsCompleted: "Mark as Completed",
    markAsInProgress: "Mark as In Progress",
    taskDetails: "Task Details",
    noTasks: "No tasks found",
    searchTasks: "Search tasks",
    filterTasks: "Filter tasks",
    sortTasks: "Sort tasks",
    taskCreated: "Task created successfully",
    taskUpdated: "Task updated successfully",
    taskDeleted: "Task deleted successfully",
    confirmDelete: "Are you sure you want to delete this task?",
    taskCompletedSuccess: "Task completed successfully",
    "tabs": {
      "today": "Today",
      "overdue": "Overdue",
      "pending": "Pending",
      "completed": "Completed"
    }
  },
  reports: {
    title: "Reports",
    daily: "Daily",
    weekly: "Weekly",
    monthly: "Monthly",
    yearly: "Yearly",
    custom: "Custom",
    generateReport: "Generate Report",
    reportType: "Report Type",
    dateRange: "Date Range",
    startDate: "Start Date",
    endDate: "End Date",
    applyFilters: "Apply Filters",
    downloadReport: "Download Report",
    shareReport: "Share Report",
    noReports: "No reports found",
    dailyOperations: "Daily Operations",
    weeklySummary: "Weekly Summary",
    inventoryStatus: "Inventory Status",
    staffPerformance: "Staff Performance",
    farmReports: "Farm Reports"
  },
  home: {
    welcome: "Welcome",
    todayTasks: "Today's Tasks",
    weatherForecast: "Weather Forecast",
    recentActivities: "Recent Activities",
    quickActions: "Quick Actions",
    viewAllTasks: "View All Tasks",
    viewAllFields: "View All Fields",
    addNewTask: "Add New Task",
    scanQRCode: "Scan QR Code",
    farmOverview: "Farm Overview",
    temperature: "Temperature",
    humidity: "Humidity",
    rainfall: "Rainfall",
    windSpeed: "Wind Speed",
    soilMoisture: "Soil Moisture",
    upcomingTasks: "Upcoming Tasks",
    noUpcomingTasks: "No upcoming tasks",
    viewMore: "View More",
    goodMorning: "Good Morning",
    goodAfternoon: "Good Afternoon",
    goodEvening: "Good Evening",
    yourTasks: "Your Tasks",
    noPendingTasks: "No pending tasks. Great job!",
    fieldStatus: "Field Status",
    noRecentActivity: "No recent activity"
  },
  "breeds": {
    "Sahiwal": "Sahiwal",
    "Holstein Friesian": "Holstein Friesian",
    "Jersey": "Jersey",
    "Red Sindhi": "Red Sindhi",
    "Ayrshire": "Ayrshire",
    "Brown Swiss": "Brown Swiss",
    "Guernsey": "Guernsey",
    "Tharparkar": "Tharparkar",
    "Gir": "Gir",
    "Kankrej": "Kankrej",
    "Droughtmaster": "Droughtmaster",
    "Relore": "Nelore",
    "Brahman": "Brahman",
    "Angus": "Angus",
    "Hereford": "Hereford",
    "Charolais": "Charolais",
    "Limousin": "Limousin",
    "Simmental": "Simmental",
    "Montbeliarde": "Montbéliarde",
    "Bazadaise": "Bazadaise",

    "Beetal": "Beetal",
    "Boer": "Boer",
    "Jamunapari": "Jamunapari",
    "Black Bengal": "Black Bengal",
    "Barbari": "Barbari",
    "Kachhi": "Kachhi",
    "Osmanabadi": "Osmanabadi",
    "Sirohi": "Sirohi",
    "Malabari": "Malabari",
    "Teddy": "Teddy",
    "Kachchhi": "Kachchhi",
    "Nachi": "Nachi",
    "Makhi Chella": "Makhi Chella",
    "Pateri": "Pateri",
    "Rajhanpuri": "Rajhanpuri",
    "Dhanni": "Dhanni",
    "Kaghani": "Kaghani",
    "Kiko": "Kiko",
    "Toggenburg": "Toggenburg",
    "Angora": "Angora",

    "Rhode Island_red": "Rhode Island Red",
    "Leghorn": "Leghorn",
    "Aseel": "Aseel",
    "Broiler": "Broiler",
    "Silkie": "Silkie",
    "Fayoumi": "Fayoumi",
    "Sussex": "Sussex",
    "Plymouth Rock": "Plymouth Rock",
    "Cornish": "Cornish",
    "Cochin": "Cochin",
    "Hubbard": "Hubbard",
    "Langshan": "Langshan",
    "Orloff": "Orloff",
    "Brahma": "Brahma",
    "Minorca": "Minorca",
    "Ancona": "Ancona",
    "Marans": "Marans",
    "Isa Brown": "ISA Brown",
    "Lohmann_Brown": "Lohmann Brown",
    "Golden Comet": "Golden Comet",

    "Rohu": "Rohu",
    "Tilapia": "Tilapia",
    "Catla": "Catla",
    "Pangasius": "Pangasius",
    "Common Carp": "Common Carp",
    "Mrigal": "Mrigal",
    "Silver Carp": "Silver Carp",
    "Grass Carp": "Grass Carp",
    "Mahseer": "Mahseer",
    "Trout": "Trout"
  },

  entity: {
    farm: {
      "noFieldsAdded": "No fields added yet.",
      "noGardensAdded": "No gardens added yet.",
      "noAnimalsAdded": "No animals added yet.",
      "noTasksAdded": "No tasks added yet.",
      "noEquipmentAdded": "No equipment added yet.",
      noPlantsAdded: "No plants added yet.",
      addFields: "Add Field",
      viewAllFields: "View All Fields",
      viewAll: "View All",
      team: "Team",
      quickActions: "Quick Actions",
      name: "Farm",
      fields: "Fields",
      plants: "Plants",
      animals: "Animals",
      equipment: "Equipment",
      tasks: "Tasks",
      gardens: "Gardens",
      reports: "Reports",
      addEquipment: "Add Equipment",
      // addPlant:"Add Plant",
      // addAnimal:"Add Animal",
      // addGarden:"Add Garden"
      farmName: "Farm Name",
      farmAddress: "Farm Address",
      farmContact: "Farm Contact",
      farmEmail: "Farm Email",
      farmPhone: "Farm Phone",
      overview: "Overview",
      Fields: "Fields",
      Team: "Team",
      Gardens: "Gardens",
      Animals: "Animals",
      Equipment: "Equipment",
      Plants: "Plants",
      Yields: "Yields",
      Tasks: "Tasks",
      Reports: "Reports",
      Profile: "Profile",
      Map: "Map",
      addTask: "Add Task",
      addReport: "Add Report",
      addEquipments: "Add Equipment",
      addPlants: "Add Plant",
      addYields: "Add Yield",
      addAnimals: "Add Animal",
      addGardens: "Add Garden",
      addAnimal: "Add Animal",
      addField: "Add Field",
      addPlant: "Add Plant",
      addGarden: "Add Garden",
      addFarm: "Add Farm",
      editFarm: "Edit Farm",
      editField: "Edit Field",
      editTask: "Edit Task",
      editReport: "Edit Report",
      editEquipment: "Edit Equipment",
      editPlant: "Edit Plant",
      editYield: "Edit Yield",
      editAnimals: "Edit Animal",
      editGardens: "Edit Garden",
      deleteFarm: "Delete Farm",
      deleteField: "Delete Field",
      deleteTask: "Delete Task",
      deleteReport: "Delete Report",
      deleteEquipment: "Delete Equipment",
      deletePlant: "Delete Plant",
      deleteYield: "Delete Yield",
      deleteAnimal: "Delete Animal",
      deleteGarden: "Delete Garden",
      viewFarm: "View Farm",
      viewField: "View Field",
      viewTask: "View Task",
      viewReport: "View Report",
      viewEquipment: "View Equipment",
      viewPlant: "View Plant",
      viewYield: "View Yield",
      viewAnimal: "View Animal",
      viewGarden: "View Garden",
      viewProfile: "View Profile",
      upcomingTasks: "Upcoming Tasks",
      viewAllTasks: "View All Tasks",
      viewAllGardens: "View All Gardens",
    },
    plant: {
      "overview": "Overview",
      "tasks": "Tasks",
      "checklists": "Checklists",
      "history": "History",
      "gallery": "Gallery",
      "qrcode": "QR Code",
      "itemId": "Item ID",
      "species": "Species",
      "variety": "Variety",
      "plantedDate": "Planted Date",
      "expectedHarvest": "Expected Harvest",
      "status": "Status",
      "health": "Health",
      "inactiveDetails": "Inactive Details",
      "reason": "Reason",
      "date": "Date",
      "notes": "Notes",
      "image": "Image",
      "notSpecified": "Not specified",
      "field": "Field",
      "garden": "Garden",
      // "notes": "Notes",
      "quickActions": "Quick Actions",
      "healthCheck": "Health Check",
      "createTask": "Create Task",
      "addPhoto": "Add Photo",
      "editPlant": "Edit Plant",
      "manageFinance": "Manage Finance",
      "markInactive": "Mark Inactive",
      "noTasks": "No tasks found for this plant",
      "tasksTabTitle": "Tasks",
      "qrCodeTabTitle": "QR Code",
      "photos": {
        "emptyText": "No photos added yet",
        "addButton": "Add Photo"
      },
      "histories": {
        "empty": "No history records found",
        "types": {
          "checklist_completed": "Health Check Completed",
          "status_change": "Status Changed",
          "note_added": "Note Added",

        },
        "noRecords": "No history records found",
        "by": "By: {{name}}"
      },
      "galleries": {
        "title": "Gallery",
        "addPhoto": "Add Photo"
      },
      "qrCodeInfo": {
        "scan": "Scan this QR code to quickly access this plant's details.",
        "print": "You can print this QR code and place it near the plant for easy identification."
      },
      "checklist": {
        "title": "Daily Plant Health Check",
        "moisture": {
          "title": "Check soil moisture",
          "description": "Ensure soil is appropriately moist but not waterlogged"
        },
        "pests": {
          "title": "Inspect for pests",
          "description": "Look for insects, eggs, or damage on leaves and stems"
        },
        "disease": {
          "title": "Check for disease",
          "description": "Look for discoloration, spots, or unusual growth"
        },
        "prune": {
          "title": "Prune if necessary",
          "description": "Remove dead or damaged leaves and branches"
        },
        "lastCompleted": "Last completed: {{time}}",
        "by": "By: {{name}}"
      },
      "noAnalysis": "No health analysis available",
      "noAnalysisHint": "Run an analysis to evaluate this plant's condition.",
      "analyze": "Analyze Health",
      "analyzing": "Analyzing...",
      "status_Excellent": "Excellent Health",
      "status_Good": "Good Health",
      "status_Fair": "Fair Health",
      "status_Poor": "Poor Health",
      "conditionsTitle": "Detected Issues",
      "recommendationsTitle": "Recommendations",
      "detailsTitle": "Detailed Notes",
      "scheduleCheckup": "Schedule Checkup",
      "checkupTitle": "Health Checkup for {{name}}",
      "recordTreatment": "Record Treatment",
      "treatmentSoon": "Plant treatment tracking is coming soon.",
      // "createTask": "Create Task",
      // "createTask": "Create Task",
      "inactiveMessage": "This plant is inactive",
      details: "Plant Details",
      add: "Add Plant",
      edit: "Edit Plant",
      name: "Plant Name",
      expectedHarvestDate: "Expected Harvest Date",
      location: "Location",
      delete: "Delete Plant",
      confirmDelete: "Are you sure you want to delete this plant?",
      plural: "Plants",
      stats: "Plant Stats",
      Seedling: "Seedling",
      Growing: "Growing",
      Flowering: "Flowering",
      Fruiting: "Fruiting",
      noFieldsAdded: "No plants added yet",
      // Validation messages
      nameRequired: "Plant name is required",
      speciesRequired: "Plant species is required",
      varietyRequired: "Plant variety is required",
      plantedDateRequired: "Planted date is required",
      locationRequired: "Please select a location (field or garden)",
      images: "Images",
      addPhoto: "Add Photo"
    },
    animal: {
      qrCode: "QR Code",
      "enterUniqueId": "Enter unique ID (e.g., ANM-001)",
      "itemIdValidation": "Item ID must be 3–20 characters and can only contain letters, numbers, hyphens, and underscores",
      "itemIdNote": "This ID will be used to generate a QR code for this animal",
      "fieldLocationOptional": "Field Location (Optional)",
      details: "Animal Details",
      add: "Add Animal",
      edit: "Edit Animal",
      name: "Animal Name",
      species: "Species",
      breed: "Breed",
      birthDate: "Birth Date",
      gender: "Gender",
      status: "Status",
      purpose: "Purpose",
      identificationNumber: "Identification Number",
      location: "Location",
      notes: "Notes",
      image: "Image",
      delete: "Delete Animal",
      confirmDelete: "Are you sure you want to delete this animal?",
      plural: "Animals",
      stats: "Animal Stats",
      healthy: "Healthy",
      sick: "Sick",
      pregnant: "Pregnant",
      "inactiveDetailsTitle": "Inactive Details",
      "inactiveReason": "Reason",
      "inactiveDate": "Date",
      "inactiveNotes": "Notes",
      "inactiveImage": "Image",
      "notSpecified": "Not specified",
      "gender": "Gender",
      "gender_male": "Male",
      "gender_female": "Female",
      "birthDate": "Birth Date",
      "idNumber": "ID Number",
      "field": "Field",
      "location": "Location",
      "purpose": "Purpose",
      "purpose_dairy": "Dairy",
      "purpose_meat": "Meat",
      "purpose_breeding": "Breeding",
      "purpose_labor": "Labor",
      "checklist": "Checklist",
      "health": "Health",
      "quickActions": "Quick Actions",
      "markInactive": "Mark Inactive",
      "qrCodeTitle": "QR Code",
      "viewQRCode": "View QR Code",
      "qrCodeDescription": "Scan this QR code to quickly access this animal's information.",
      "checklistArray": {
        "dailyFeeding": "Daily feeding",
        "checkWater": "Check water supply",
        "healthInspection": "Visual health inspection",
        "checkParasites": "Check for parasites",
        "cleanArea": "Clean living area",
        "medication": "Administer medication (if needed)",
        "checkTemperature": "Check temperature"
      },
      "tasksCompleted": "{{completed}} of {{total}} tasks completed",
      "saveChecklist": "Save Checklist",
      "savedMessage": "Checklist saved successfully",
      "noAnalysis": "No health analysis available",
      "noAnalysisHint": "Use AI to analyze this animal's health based on its photos",
      "analyze": "Analyze Health",
      "analyzing": "Analyzing...",
      "status_excellent": "Excellent Health",
      "status_good": "Good Health",
      "status_fair": "Fair Health",
      "status_poor": "Poor Health",
      "fever": "Fever",
      "cough": "Cough",
      "limping": "Limping",
      "conditionsTitle": "Conditions",
      "recommendationsTitle": "Recommendations",

      "increase_hydration": "Increase hydration",
      "monitor_temperature": "Monitor temperature",
      "provide_shelter": "Provide shelter",
      "detailsTitle": "Details",
      "scheduleCheckup": "Schedule Checkup",
      "recordTreatment": "Record Treatment",
      "vetCheckupTitle": "Veterinary checkup for {{name}}",
      "treatmentSoon": "Treatment recording will be available in the next update.",
      "addExpense": "Add Expense",
      "addMilkRecord": "Add Milk Record",
      "addCleanliness": "Add Cleanliness",
      "morningMilk": "Morning Milk",
      "eveningMilk": "Evening Milk",
      "milkDate": "Milk Date",
      "milkNotes": "Milk Notes",
      "milkQuality": "Milk Quality",
      "addMilkRecordFor": "Add Milk Record For",
      "addExpenseFor": "Add Expense For",
      "costType": "Cost Type",
      "expenseAmount": "Expense Amount",
      "enterExpenseAmount": "Enter Expense Amount",
      "expenseDate": "Expense Date",
      "selectExpenseDate": "Select Expense Date",
      "expenseNotes": "Expense Notes",
      "enterExpenseNotes": "Enter Expense Notes",
      "bathGiven": "Bath Given",
      "shelterCleaned": "Shelter Cleaned",
      "groomingDone": "Grooming Done",
      "cleanlinessDate": "Cleanliness Date",
      "cleanlinessRemarks": "Cleanliness Remarks",
      "cleanlinessTitle": "Cleanliness",
      "addCleanlinessFor": "Add Cleanliness For",
      "enterLiters": "Enter liters",
      "enterNotes": "Enter notes",
      noNotes: "No notes added yet.",
      // Validation messages
      nameRequired: "Animal name is required",
      speciesRequired: "Animal species is required",
      breedRequired: "Animal breed is required",
      images: "Images",
      addPhoto: "Add Photo"
    },
    field: {
      "Active": "Active",
      "latitude": "Latitude",
      "longitude": "Longitude",
      "latitudePlaceholder": "Enter latitude",
      "longitudePlaceholder": "Enter longitude",
      "address": "Address",
      "addressPlaceholder": "Enter address",
      "selectOnMap": "Select on Map",
      location: "Location",
      "statusActive": "Active",
      "statusFallow": "Fallow",
      "statusUnder Maintenance": "Under Maintenance",
      "typeCropland": "Cropland",
      "typeGarden": "Garden",
      "typeOrchard": "Orchard",
      "typeLivestock": "Livestock",
      "namePlaceholder": "Enter name",
      details: "Field Details",
      add: "Add Field",
      edit: "Edit Field",
      name: "Field Name",
      type: "Type",
      size: "Size",
      sizeUnit: "Size Unit",
      status: "Status",
      cropType: "Crop Type",
      plantedDate: "Planted Date",
      harvestDate: "Harvest Date",
      health: "Health",
      // location: "Location",
      image: "Image",
      delete: "Delete Field",
      confirmDelete: "Are you sure you want to delete this field?",
      plural: "Fields",
      noFieldsAdded: "No fields added yet",
      "createField": "Create Field",
      harvestDate: "Harvest Date",
      season: "Season",
      selectSeason: "Select Season",
      cropType: "Crop Type",
      enterCropType: "Enter crop type",
      yieldQuantity: "Yield Quantity",
      enterQuantity: "Enter quantity",
      unit: "Unit",
      notesOptional: "Notes (Optional)",
      enterNotes: "Enter any notes...",
      imagesOptional: "Images (Optional)",
      recordHarvest: "Record Harvest",
      recording: "Recording...",
      // Validation messages
      nameRequired: "Field name is required",
      sizeRequired: "Field size is required",
      typeRequired: "Please select field type",
      statusRequired: "Please select field status",
      cropTypeRequired: "Crop type is required",
      plantedDateRequired: "Planted date is required",
      quantityRequired: "Yield quantity is required",
      validQuantityRequired: "Please enter a valid quantity",
      seasonRequired: "Please select a season",
      soilTypeRequired: "Please select soil type",
      images: "Images",
      addPhoto: "Add Photo"
    },
    garden: {
      "enterGardenName": "Enter garden name",
      "selectSoilType": "Select Soil Type",
      "locationDescription": "Location Description",
      "createGarden": "Create Garden",
      "latitude": "Latitude",
      "longitude": "Longitude",
      "latitudePlaceholder": "Enter latitude",
      "longitudePlaceholder": "Enter longitude",
      "address": "Address",
      "addressPlaceholder": "Enter address",
      "selectOnMap": "Select on Map",
      "typeVegetable": "Vegetable",
      "typeMixed": "Mixed",
      "typeFruit": "Fruit",
      "typeHerb": "Herb",
      "typeFlower": "Flower",
      Installed: "installed",
      "unitHectares": "Hectares",
      "unitAcres": "Acres",
      "unitSquareFoot": "Square Foot",
      "sq_m": "Square Meter",
      "statusActive": "Active",
      "statusInactive": "Inactive",
      "statusPlanning": "Planning",
      "selectSoilType": "Soil Type",
      "soilTypeOther": "Other",
      "soilTypeSandyClay": "Sandy Clay",
      "soilTypeSiltyClay": "Silty Clay",
      "soilTypeClayLoam": "Clay Loam",
      "soilTypeSandyLoam": "Sandy Loam",
      "soilTypeClay": "Clay",
      "soilTypeSandy": "Sandy",
      "soilTypeLoam": "Loam",
      "soilTypeSilt": "Silt",
      "soilTypePeat": "Peat",
      "soilTypeChalk": "Chalk",
      // "soilTypeSandyLoam": "Sandy Loam",
      // "soilTypeClayLoam": "Clay Loam",
      // "soilTypeSiltyClay": "Silty Clay",
      // "soilTypeSandyClay": "Sandy Clay",
      // "soilTypeOther": "Other",
      "enterIrrigationDetails": "Enter irrigation details",
      details: "Garden Details",
      add: "Add Garden",
      edit: "Edit Garden",
      name: "Garden Name",
      type: "Type",
      size: "Size",
      sizeUnit: "Size Unit",
      status: "Status",
      soilType: "Soil Type",
      irrigationSystem: "Irrigation System",
      location: "Location",
      image: "Image",
      delete: "Delete Garden",
      confirmDelete: "Are you sure you want to delete this garden?",
      plural: "Gardens",
      stats: "Garden Stats",
      active: "Active",
      inactive: "Inactive",
      planning: "Planning",
      sq_m: "Sq_m",
      garden: "Garden",
      plants: "Plants",
      gallery: "Gallery",
      photos: "Photos",
      "irrigationSystemDrip": "Drip",
      "irrigationSystemSprinkler": "Sprinkler",
      "irrigationSystemManual": "Manual",
      "irrigationSystemAutomated": "Automated",
      "irrigationSystemFlood": "Flood",
      "irrigationSystemOther": "Other",
      "Herb": "Herb",
      "Fruit": "Fruit",
      "Mixed": "Mixed",
      "Vegetable": "Vegetable",
      "Flower": "Flower",
      // Validation messages
      nameRequired: "Garden name is required",
      sizeRequired: "Garden size is required",
      typeRequired: "Please select garden type",
      statusRequired: "Please select garden status",
      images: "Images",
      addPhoto: "Add Photo"
    },
    equipment: {
      "itemId": "Item ID (for QR code)",
      "enterUniqueId": "Enter unique ID (e.g., EQP-1234)",
      "idFormatNote": "Item ID must follow the format PREFIX-XXXX (e.g., EQP-1234)",
      "idUsageNote": "This ID will be used to generate a QR code for this equipment",
      "name": "Name",
      "enterEquipmentNamePlaceholder": "Enter equipment name",
      "equipmentType": "Equipment Type",
      "selectEquipmentType": "Select equipment type",
      "manufacturer": "Manufacturer (Optional)",
      "enterManufacturerName": "Enter manufacturer name",
      "model": "Model (Optional)",
      "enterModelNumberOrName": "Enter model number or name",
      "purchaseDate": "Purchase Date (Optional)",
      "selectADate": "Select a date",
      "purchasePrice": "Purchase Price (Optional)",
      "enterPurchasePrice": "Enter purchase price",
      "status": "Status",
      "selectStatus": "Select status",
      "lastMaintenanceDate": "Last Maintenance Date (Optional)",
      "nextMaintenanceDate": "Next Maintenance Date (Optional)",
      "location": "Location (Optional)",
      "notes": "Notes (Optional)",
      "enterNotes": "Enter any additional notes",
      "addEquipment": "Add Equipment",
      "adding": "Adding...",
      "noFarmsAvailable": "No farms available. Please create a farm first.",
      details: "Equipment Details",
      add: "Add Equipment",
      edit: "Edit Equipment",
      // name: "Equipment Name",
      type: "Type",
      // manufacturer: "Manufacturer",
      // model: "Model",
      // purchaseDate: "Purchase Date",
      // purchasePrice: "Purchase Price",
      // status: "Status",
      // lastMaintenanceDate: "Last Maintenance Date",
      // nextMaintenanceDate: "Next Maintenance Date",
      // location: "Location",
      // notes: "Notes",
      "enterNotes": "Enter any additional notes",
      "statusRetired": "Retired",
      "statusRepair": "Repair",
      "statusMaintenance": "Maintenance",
      "statusOperational": "Operational",
      "statusOther": "Other",
      "typeTool": "Tool",
      "typeIrrigation": "Irrigation",
      "typeSprayer": "Sprayer",
      "typePlow": "Plow",
      "typeHarvester": "Harvester",
      "typeTractor": "Tractor",
      "otherType": "Other Type",
      "enterStorageLocation": "Enter storage location",
      image: "Image",
      delete: "Delete Equipment",
      confirmDelete: "Are you sure you want to delete this equipment?",
      plural: "Equipment",
      stats: "Equipment Stats",
      operational: "Operational",
      maintenance: "Maintenance",

      repair: "Repair",
      "equipmentTypes": {
        "tractor": "Tractor",
        "plow": "Plow",
        "sprayer": "Sprayer",
        other: "Other",
        "jeep": "Jeep",
        "motorbike": "Motorbike",
        "tractor": "Tractor",
        "harvester": "Harvester",
        "planter": "Planter",
        "sprayer": "Sprayer",
        "cultivator": "Cultivator",
        "car": "Car",
        "jeep": "Jeep",
        "motorbike": "Motorbike",
        "bicycle": "Bicycle",
        "other": "Other (Custom)"
      },
      "equipmentStatus": {
        "active": "Active",
        "maintenance": "Under Maintenance",
        "inactive": "Inactive",
        operational: "Operational",
        working: 'Working',

      },
      "make": "Make",
      "checklistItems": {
        "checkOil": "Check oil level",
        "inspectHydraulic": "Inspect hydraulic system",
        "checkTire": "Check tire pressure",
        "inspectBelts": "Inspect belts and hoses",
        "checkBattery": "Check battery condition",
        "inspectAirFilter": "Inspect air filter",
        "checkLights": "Check lights and signals"
      },
      "noAnalysisTitle": "No condition analysis available",
      "noAnalysisDescription": "Use AI to analyze this equipment's condition based on its photos",
      "analyzeButton": "Analyze Condition",
      "analyzing": "Analyzing...",
      "conditions": {
        "excellent": "Excellent Condition",
        "good": "Good Condition",
        "fair": "Fair Condition",
        "poor": "Poor Condition"
      },
      "urgency": {
        "high": "High Urgency",
        "medium": "Medium Urgency",
        "low": "Low Urgency"
      },
      "maintenanceNeeds": "Maintenance Needs",
      "estimatedLife": "Estimated Life Remaining",
      // "details": "Details",
      "scheduleMaintenance": "Schedule Maintenance",
      "maintenanceTitlePrefix": "Maintenance for",
      "quickActions": {
        "title": "Quick Actions",
        "edit": "Edit",
        "inactive": "Mark Inactive"
      },
      "photos": {
        "title": "Photos",
        "add": "Add Photo",
        "adding": "Adding...",
        "addPlaceholder": "Add Photo",
        "noPhotos": "No additional photos"
      },
      "actions": {
        "scheduleMaintenance": "Schedule Maintenance"
      },
      "tabs": {
        "details": "Details",
        "checklist": "Checklist",
        "condition": "Condition",
        "history": "History"
      }
    },
    yield: {
      details: "Yield Details",
      add: "Add Yield",
      edit: "Edit Yield",
      name: "Yield Name",
      cropType: "Crop Type",
      harvestDate: "Harvest Date",
      quantity: "Quantity",
      unit: "Unit",
      quality: "Quality",
      location: "Location",
      notes: "Notes",
      image: "Image",
      delete: "Delete Yield",
      confirmDelete: "Are you sure you want to delete this yield?",
      plural: "Yields",
      stats: "Yield Stats",
      excellent: "Excellent",
      good: "Good",
      fair: "Fair"
    },
    "checklist": {
      // "title": "Checklists",
      "add": "Add",
      "noChecklistsTitle": "No Checklists Found",
      "noChecklistsDesc": "You haven't added any checklists yet.",
      "selectTitle": "Select a Checklist",
      "noAvailableChecklists": "No available checklists to add.",
      "title": "Checklist Items",
      "required": "Required",
      "enterTitle": "Enter item title",
      "addCustomItem": "Add Custom Item",
      "evidenceImages": "Evidence Images",
      "addImage": "Add Image",
      "notes": "Notes",
      "notesPlaceholder": "Add any additional notes here...",
      // "title": "Checklists",
      // "add": "Add",
      // "noChecklistsTitle": "No Checklists Found",
      // "noChecklistsDesc": "You haven't added any checklists yet.",
      // "selectTitle": "Select a Checklist",
      // "noAvailableChecklists": "No available checklists to add.",
      "details": "Checklist Details",
      "items": "items",
      "itemsCompleted": "items completed",
      "todayBadge": "Today",
      "defaultTitle": "Untitled Checklist",
      "viewDetails": "View Details"
    }
  },
  task: {
     "checklistTemplete": "Checklist Template",
    "selectTemplete": "Select Template",
    "attachImage": "Attach Image",
    emptyFieldTasks: "No tasks for this field",
    you: "You",
    required: "Required",
    timeline: "Timeline",
    created: "Created",
    "completeTask": "Complete Task",
    details: "Task Details",
    add: "Add Task",
    edit: "Edit Task",
    title: "Title",
    description: "Description",
    "titlePlaceholder": "Enter task title",
    "descriptionPlaceholder": "Enter task description",
    // status: "Status",
    // priority: "Priority",
    dueDate: "Due Date",
    assignedTo: "Assigned To",
    assignedBy: "Assigned By",
    category: "Category",
    relatedTo: "Related To",
    checklist: "Checklist",
    evidence: "Evidence",
    evidenceRequired: "Evidence Required",
    delete: "Delete Task",
    confirmDelete: "Are you sure you want to delete this task?",
    markAsCompleted: "Mark as Completed",
    markAsInProgress: "Mark as In Progress",
    frequency: "Frequency",
    repeatUntil: "Repeat Until",
    recurring: "Recurring",
    createSuccess: "Task created successfully",
    updateSuccess: "Task updated successfully",
    deleteSuccess: "Task deleted successfully",
    completeSuccess: "Task completed successfully",
    markAsCancelled: "Mark as Cancelled",
    unassigned: "Unassigned",
    assignTo: "Assign To",
    // Validation messages
    titleRequired: "Task title is required",
    assigneeRequired: "Please assign the task to someone",
    dueDateRequired: "Due date is required",
    createdSuccessfully: "Task created successfully",
    createError: "Failed to create task. Please try again.",
    assignToPlaceholder: "Select assignee...",
    priorityPlaceholder: "Select priority...",
    clearImages: "Clear All Images",
    noEvidenceProvided: "No evidence provided",
    evidenceRequired: "Evidence required for this task",
    "entityTypes": {
      "field": "Field",
      "garden": "Garden",
      "plant": "Plant",
      "animal": "Animal",
      "equipment": "Equipment"
    },
    priority: {
      Low: "Low",
      Medium: "Medium",
      High: "High",
      Urgent: "Urgent",
      low: "Low",
      medium: "Medium",
      high: "High",
      urgent: "Urgent"
    },
    status: {
      pending: "Pending",
      inProgress: "In Progress",
      completed: "Completed",
      overdue: "Overdue",
      cancelled: "Cancelled"
    },
    unknown: "Unknown",
    evidenceNote: "Evidence Required",
    creating: 'Creating...',
    create: 'Create Task',
    frequencies: {
      Once: 'Once',
      Daily: 'Daily',
      Weekly: 'Weekly',
      Monthly: 'Monthly',
    },
    categories: {
      general: 'General',
      irrigation: 'Irrigation',
      fertilization: 'Fertilization',
      harvest: 'Harvest',
      maintenance: 'Maintenance',
      feeding: 'Feeding',
      health_check: 'Health Check',
      cleaning: 'Cleaning',
    },
  },
  user: {
    loading: "Loading...",
    // farmUser: "",
    // invite: "Invite User",
    // list: "Users",
    // name: "Name",
    // email: "Email",
    // phone: "Phone",
    // // role: "Role",
    // status: "Status",
    // actions: "Actions",
    // inviteUser: "Invite User",
    // removeUser: "Remove User",
    // confirmRemove: "Are you sure you want to remove this user?",
    // inviteSuccess: "User invited successfully",
    // removeSuccess: "User removed successfully",
    // inviteError: "Failed to invite user",
    // removeError: "Failed to remove user",
    // owner: "Owner",
    // manager: "Manager",
    // caretaker: "Caretaker",
    // active: "Active",
    // inactive: "Inactive",
    // pending: "Pending",
    // plural: "Users",
    // manageUsers: "Manage Users",
    // sendInvitation: "Send Invitation",
    // inviteNote: "Note: The user will receive an email with a link to accept the invitation.",
    // role: {
    //   owner: "Owner",
    //   manager: "Manager",
    //   caretaker: "Caretaker",
    //   active: "Active",
    //   inactive: "Inactive",
    //   label: "Label",
    //   manageDescription: " Manage Users",
    // },
    verificationEmailSent: "A verification email has been sent to the user.",
    // 
    "farmUsers": "Farm Users",
    "inviteNew": "Invite New User",
    "inviteDescription": "Invite a user to your farm",
    "invite": "Invite User",
    "list": "Users",
    "name": "Name",
    "email": "Email",
    "phone": "Phone",
    "status": "Status",
    "actions": "Actions",
    "inviteUser": "Invite User",
    "removeUser": "Remove User",
    "confirmRemove": "Are you sure you want to remove this user?",
    "inviteSuccess": "User invited successfully",
    "removeSuccess": "User removed successfully",
    "inviteError": "Failed to invite user",
    "removeError": "Failed to remove user",
    "owner": "Owner",
    "manager": "Manager",
    "caretaker": "Caretaker",
    "active": "Active",
    "inactive": "Inactive",
    "pending": "Pending",
    "plural": "Users",
    "manageUsers": "Manage Users",
    "userManagement": "User Management",
    "userManagementDescription": "Manage the users of your farm",
    "userManagementInvite": "Invite a User",
    "userManagementRemove": "Remove a User",
    "userManagementConfirmRemove": "Are you sure you want to remove this user?",
    "userManagementInviteSuccess": "User successfully invited",
    "fullName": "Full Name",
    "enterFullName": "Enter full name",
    "enterEmail": "Enter email",
    "enterPhone": "Enter phone number",
    "enterRole": "Enter role",
    "enterStatus": "Enter status",
    "enterActions": "Enter actions",
    "you": "You",
    "role": {
      "owner": "Owner",
      "manager": "Manager",
      "caretaker": "Caretaker",
      "active": "Active",
      "inactive": "Inactive",
      "managerDescription": "Make user a manager",
      "admin": "Admin",
      "label": "Role"
    },
    "sendInvitation": "Send Invitation",
    "inviteNote": "To invite a user, enter their email and name, then send the invitation.",
    // Validation messages
    nameRequired: "Full name is required",
    emailRequired: "Email address is required",
    phoneRequired: "Phone number is required",
    passwordRequired: "Password is required",
    roleRequired: "Please select a role",
    farmRequired: "Please select at least one farm",
    validEmailRequired: "Please enter a valid email address",
    validPhoneRequired: "Please enter a valid phone number",
    passwordMinLength: "Password must be at least 6 characters",
    updateSuccess: "User updated successfully"
  },
  farm: {
    "sizeLabel": "{{size}} {{unit}}",
    "noSize": "No size specified",
    invitingTo: "Inviting To",
    details: "Farm Details",
    add: "Add Farm",
    edit: "Edit Farm",
    name: "Farm Name",
    description: "Description",
    location: "Location",
    size: "Size",
    sizeUnit: "Size Unit",
    type: "Type",
    status: "Status",
    owner: "Owner",
    image: "Image",
    delete: "Delete Farm",
    confirmDelete: "Are you sure you want to delete this farm?",
    createSuccess: "Farm created successfully",
    updateSuccess: "Farm updated successfully",
    deleteSuccess: "Farm deleted successfully",
    selectFarm: "Select Farm",
    noFarms: "No Farms Found",
    createFarm: "Create Farm",
    createFarmPrompt: "You don't have any farms yet. Create your first farm to get started.",
    noFarmsCreateFirst: "No farms available. Please create a farm first.",
    addNew: "Add New Farm",
    management: "Farm Management",
    enterLocation: "Enter location",
    enterDescription: "Enter description",
    enterName: "Enter name",
    addPhoto: "Add Photo",
    create: "Create",
    createNew: "Create New",
    unit: "Unit",
    farmType: "Farm Type",
    addField: "Add Field",
    viewAllFields: "View All Fields",
    viewAll: "View All",
    farmStatus: "Farm Status",
    team: "Team",
    farmStatusOptions: {
      active: "Active",
      inactive: "Inactive"
    },
    farmtypes: {
      Crop: "Crop Farm",
      Livestock: "Livestock Farm",
      Mixed: "Mixed Farm",
      Orchard: "Orchard Farm"
    },
    farmSizeOptions: {
      acres: "Acres",
      hectares: "Hectares"
    },
    fields: "Fields",
    field: "Field",
    fieldDetails: "Field Details",
    fieldName: "Field Name",
    fieldDescription: "Field Description",
    fieldSize: "Field Size",
    fieldSizeUnit: "Field Size Unit",
    fieldStatus: "Field Status",
    fieldStatusOptions: {
      active: "Active",
      inactive: "Inactive"
    },
    fieldType: "Field Type",
    fieldTypeOptions: {
      Cropland: "Cropland",
      Garden: "Garden",
      Orchard: "Orchard",
      Livestock: "Livestock"
    },
    fieldCrop: "Field Crop",
    fieldCropOptions: {
      wheat: "Wheat",
      corn: "Corn",
      rice: "Rice",
      barley: "Barley",
      soybean: "Soybean",
      cotton: "Cotton",
      potato: "Potato",
      tomato: "Tomato"
    },
    fieldCropDetails: "Field Crop Details",
    fieldCropName: "Field Crop Name",
    fieldCropDescription: "Field Crop Description",
    fieldCropSize: "Field Crop Size",
    fieldCropSizeUnit: "Field Crop Size Unit",
    fieldCropStatus: "Field Crop Status",
    fieldCropStatusOptions: {
      active: "Active",
      inactive: "Inactive"
    },
    fieldCropType: "Field Crop Type",
    fieldCropTypeOptions: {
      wheat: "Wheat",
      corn: "Corn",
      rice: "Rice",
      barley: "Barley",
      soybean: "Soybean",
      cotton: "Cotton",
      potato: "Potato",
      tomato: "Tomato"
    },
    fieldCropVariety: "Field Crop Variety",
    fieldCropVarietyOptions: {
      wheat: "Wheat",
      corn: "Corn",
      rice: "Rice",
      barley: "Barley",
      soybean: "Soybean",
      cotton: "Cotton",
      potato: "Potato",
      tomato: "Tomato"
    },
    fieldCropYield: "Field Crop Yield",
    fieldCropYieldUnit: "Field Crop Yield Unit",
    fieldCropHarvestDate: "Field Crop Harvest Date",
    fieldCropHarvestDateRange: "Field Crop Harvest Date Range",
    fieldCropHarvestDateRangeOptions: {
      start: "Start",
      end: "End"
    },
    fieldCropHarvestTime: "Field Crop Harvest Time",
    fieldCropHarvestTimeRange: "Field Crop Harvest Time Range",
    fieldCropHarvestTimeRangeOptions: {
      start: "Start",
      end: "End"
    },
    farmtypes: {
      // mixed: "Mixed Farm",
      crop: "Crop Farm",
      livestock: "Livestock Farm",
      mixed: "Mixed Farm",
      orchard: "Orchard Farm"
    },
    farmUnits: {
      acres: "Acres",
      hectares: "Hectares"
    },
    // Validation messages
    nameRequired: "Farm name is required",
    validSizeRequired: "Please enter a valid farm size",
    typeRequired: "Please select farm type",
    locationRequired: "Farm location is required",
    creating: "Creating...",
    createSuccess: "Farm created successfully",
    images: "Images",
    addPhoto: "Add Photo"
  },
  map: {
    title: "Farm Map",
    fields: "Fields",
    gardens: "Gardens",
    animals: "Animals",
    equipment: "Equipment",
    plants: "Plants",
    noItems: "No {type} found",
    addItem: "Add {type}",
    searchPlaceholder: "Search {type}...",
    interactiveMap: "Interactive map coming soon",
    filterItems: "Filter items",
    sortItems: "Sort items",
    viewDetails: "View Details",
    status: "Status",
    location: "Location",
    size: "Size",
    type: "Type",
    search_fields: "Search Fields",
    search_gardens: "Search Gardens",
    search_animals: "Search Animals",
    search_equipments: "Search Equipments"
  },
  field: {
    "cropType": "Crop Type",
    "enterCropType": "Enter crop type",
    "soilType": "Soil Type",
    "selectSoilType": "Select soil type",
    "plantedDate": "Planted Date",
    "expectedHarvestDate": "Expected Harvest Date",
    "selectDate": "Select date",
    "notes": "Notes",
    "enterNotes": "Enter notes",
    "cropImage": "Crop Image",
    cropHistory: "Crop History",
    previous: "Previous",
    crop: "Crop",
    planted: "Planted",
    harvested: "Harvested",
    "no_active_crop": "No active crop for this field",
    "add_crop": "Add Crop",
    "importBoundary": "Import Field Boundary",
    "uploadKMZ": "Upload KMZ File",
    "voiceInstructions": "Export your field from Google Earth as a KMZ file, then upload it here to automatically set the field boundary.",
    "invalidFileType": "Invalid file type. Please select a KMZ file.",
    "noKMLFound": "No KML data found in the uploaded file.",
    "noPolygonFound": "No polygon boundary found in the KMZ file.",
    "noFeaturesFound": "No geographic features found in the KMZ file.",
    "importError": "Error importing KMZ file. Please try again.",
    "boundaryImported": "Field boundary successfully imported!",
    "editBoundary": "Edit Boundary on Map",
    addCrop: "Add Crop",
    cropAdded: "Crop added successfully",
    cropExists: "Crop already exists for this field",
    cropType: "Crop Type",
    plantedDate: "Planted Date",
    expectedHarvestDate: "Expected Harvest Date",
    notes: "Notes",
    image: "Image",
    soilType: "Soil Type",
    health: "Health",
    active: "Active",
    harvested: "Harvested",
    cropHistory: "Crop History",
    failed: "Failed",
    noCropsAdded: "No crops added yet",
    add: "Add Crop",
    edit: "Edit Crop",
    editField:'Edit Field',
    delete: "Delete Crop",
    confirmDelete: "Are you sure you want to delete this crop?",
    plural: "Crops",
    stats: "Crop Stats",
    growing: "Growing",
    flowering: "Flowering",
    fruiting: "Fruiting",
    selectDate: "Select Date",
    enterCropType: "Enter crop type",
    selectSoilType: "Select soil type",
    noCrops: "No crops added yet",
    harvest: "Harvest Crop",
    harvestSuccess: "Crop harvested successfully",
    harvestError: "Failed to harvest crop",
    harvestDate: "Harvest Date",
    actualHarvestDate: "Actual Harvest Date",
    harvestNotes: "Harvest Notes",
    harvestImage: "Harvest Image",
    season: "Season",
    yieldQuantity: "Yield Quantity",
    yieldUnit: "Yield Unit",
    quality: "Quality",
    enterQuantity: "Enter yield quantity",
    selectUnit: "Select yield unit",
    selectQuality: "Select yield quality",
    selectSeason: "Select season",
    noHarvests: "No harvests recorded yet",
    harvestHistory: "Harvest History",
    viewHarvest: "View Harvest",
    noHarvestData: "No harvest data available",
    harvestDetails: "Harvest Details",
    editHarvest: "Edit Harvest",
    deleteHarvest: "Delete Harvest",
    confirmDeleteHarvest: "Are you sure you want to delete this harvest?",
    harvestDeleted: "Harvest deleted successfully",
    harvestNotDeleted: "Failed to delete harvest",
    noHarvestsFound: "No harvests found",
    viewAllHarvests: "View All Harvests",
    enterNotes: "Enter notes",
    // harvestDate: "Harvest Date",
    // actualHarvestDate: "Actual Harvest Date",
    // harvestNotes: "Harvest Notes",
    // harvestImage: "Harvest Image",
    // season: "Season",
    // yieldQuantity: "Yield Quantity",
    // yieldUnit: "Yield Unit",
    // quality: "Quality"
    "typeCropland": "Cropland",
    "typeGarden": "Garden",
    "typeOrchard": "Orchard",
    "typeLivestock": "Livestock",
    "unitAcres": "Acres",
    "unitHectares": "Hectares",
    "statusActive": "Active",
    "statusFallow": "Fallow",
    "statusMaintenance": "Maintenance",
    "details": "Details",
    "currentCrop": "Current Crop",
    "fieldHistory": "Field History",
    "fieldtasks": "Field Tasks",
    // "cropType": "Crop Type",
    "size": "Size",
    "location": "Location",
    "quickActions": "Quick Actions",
    // "edit": "Edit",
    "markInactive": "Mark Inactive",
    "markAsHarvested": "Mark as Harvested",
    "cropland": "Cropland",
    "garden": "Garden",
    "orchard": "Orchard",
    "livestock": "Livestock",
    // "active": "Active",
    "fallow": "Fallow",
    "maintenance": "Maintenance",
    previous: "Previous",
    crops: "Crops",
    "notesOptional": "Notes (Optional)",
    "ImagesOptional": "Images (Optional)",
    "unit": "Unit",
    "recordHarvest": "Record Harvest",
    "recordingHarvest": "Recording Harvest..."
  },
  crop: {
    "planted": "Planted",
    title: "Crop",
    add: "Add Crop",
    edit: "Edit Crop",
    name: "Crop Name",
    description: "Description",
    amount: "Amount",
    currency: "Currency",
    date: "Date",
    forCrop: "For Crop",
    addReceipt: "Add Receipt",
    selectDate: "Select Date",
    descriptionPlaceholder: "Enter transaction description",
    descriptionHelper: "This description will be used to store the transaction details",
    add: "Add Crop",
    edit: "Edit Crop",
    delete: "Delete Crop",
    confirmDelete: "Are you sure you want to delete this crop?",
    plural: "Crops",
    stats: "Crop Stats",
    growing: "Growing",
    flowering: "Flowering",
    fruiting: "Fruiting",
    noCropsAdded: "No crops added yet",
    viewAllCrops: "View All Crops",
    cropDetails: "Crop Details",
    cropName: "Crop Name",
    cropDescription: "Crop Description",
    cropAmount: "Crop Amount",
    cropCurrency: "Crop Currency",
    cropDate: "Crop Date",
    cropForCrop: "Crop For Crop",
    cropInField: "Crop In Field",
    type: "Crop Type",
    variety: "Crop Variety",
    plantedDate: "Planted Date",
    expectedHarvestDate: "Expected Harvest Date",
    actualHarvestDate: "Actual Harvest Date",
    notes: "Notes",
    image: "Image",
    field: "Field",
    expectedHarvest: "Expected Harvest",
    markHarvest: "Mark as Harvested",
    tasks: "Tasks",
    addTask: "Add Task",
    noTasks: "No tasks found",
    noImages: "No images added yet",
    addImages: "Add Images",
    viewFinancials: "View Financials",
    viewFinancialsDescription: "View the financials for this crop",
    addTransactionDescription: "Add a transaction for this crop",
    addTransaction: "Add Transaction",
    gallery: "Gallery",
    financials: "Financials",
    viewAllTransactions: "View All Transactions",
    details: "Crop Details",
    soilType: "Soil Type",
    health: "Health",
    active: "Active",
    harvested: "Harvested",
    failed: "Failed",
    markHarvested: "Mark as Harvested",
    addImage: "Add Image",
    harvest: "Harvest Crop",
    // noCropsAdded: "No crops added yet",
    // add: "Add Crop",
    // edit: "Edit Crop",
    // delete: "Delete Crop",
    // confirmDelete: "Are you sure you want to delete this crop?",
    // plural: "Crops",
    // stats: "Crop Stats",

  },
  financials: {
    title: "Financials",
    add: "Add Financials",
    edit: "Edit Financials",
    name: "Financials Name",
    description: "Description",
    amount: "Amount",
    currency: "Currency",
    date: "Date",
    forCrop: "For Crop",
    addReceipt: "Add Receipt",
    selectDate: "Select Date",
    descriptionPlaceholder: "Enter transaction description",
    descriptionHelper: "This description will be used to store the transaction details",
    add: "Add Financials",
    edit: "Edit Financials",
    delete: "Delete Financials",
    confirmDelete: "Are you sure you want to delete these financials?",
    plural: "Financials",
    stats: "Financials Stats",
    growing: "Growing",
    flowering: "Flowering",
    fruiting: "Fruiting",
    noFinancialsAdded: "No financials added yet",
    viewAllFinancials: "View All Financials",
    financialsDetails: "Financials Details",
    financialsName: "Financials Name",
    financialsDescription: "Financials Description",
    financialsAmount: "Financials Amount",
    financialsCurrency: "Financials Currency",
    financialsDate: "Financials Date",
    financialsForCrop: "Financials For Crop",
    financialsInField: "Financials In Field",
    tabs: {
      overview: "Overview",
      transactions: "Transcations",
      analysis: "Analysis",

    },
    totalCosts: "Total Costs",
    totalRevenue: "Total Revenue",
    profit: 'Profit',
    costBreakdown: "Cost Breakdown",
    noCostData: "No Cost Data Available",
    "noTransactions": "No transactions available",
    "analysisText": "No analysis available",
    transactions: "Transcations",
    analysis: "Analysis",

  },
  transaction: {
    transaction: "Transaction",
    transactions: "Transactions",
    addTransaction: "Add Transaction",
    editTransaction: "Edit Transaction",
    deleteTransaction: "Delete Transaction",
    confirmDeleteTransaction: "Are you sure you want to delete this transaction?",
    transactionDeleted: "Transaction deleted successfully",
    transactionNotDeleted: "Failed to delete transaction",
    noTransactions: "No transactions found",
    viewAllTransactions: "View All Transactions",
    transactionDetails: "Transaction Details",
    transactionDate: "Transaction Date",
    amount: "Amount",
    currency: "Currency",
    description: "Description",
    receipt: "Receipt",
    tags: "Tags",
    highCost: "High Cost",
    forCrop: "For Crop",
    inField: "In Field",
    selectCurrency: "Select Currency",
    fillAllFields: "Please fill in all fields",
    transactionAdded: "Transaction added successfully",
    transactionNotAdded: "Failed to add transaction",
    addRecepit: "Add Receipt",


    title: "Transaction",
    addTtile: "Add Transaction",
    add: "Add Transaction",
    edit: "Edit Transaction",
    name: "Transaction Name",
    date: "Date",
    addReceipt: "Add Receipt",
    selectDate: "Select Date",
    descriptionPlaceholder: "Enter transaction description",
    descriptionHelper: "This description will be used to store the transaction details",

    delete: "Delete Transaction",
    confirmDelete: "Are you sure you want to delete this transaction?",
    plural: "Transactions",
    stats: "Transaction Stats",
    growing: "Growing",
    flowering: "Flowering",
    fruiting: "Fruiting",
    noTransactionsAdded: "No transactions added yet",
    transactionName: "Transaction Name",
    transactionDescription: "Transaction Description",
    transactionAmount: "Transaction Amount",
    transactionCurrency: "Transaction Currency",
    transactionForCrop: "Transaction For Crop",
    transactionInField: "Transaction In Field"
  },
  finance: {
    title: "Finance",
    manageFinances: "Manage Finance",
    analyzeTransactions: "Analyze Transactions",
    addTransaction: "Add Transaction",
    viewTransactions: "View Transactions",
    analyze: "Analyze",
    add: "Add Finance",
    view: "View",
    transactions: "Transactions",
    analysis: "Analysis",
    insights: "Insights",
    recommendations: "Recommendations",
    // addTransaction: "Add Transaction",
    // viewTransactions: "View Transactions",
    // analyzeTransactions: "Analyze Transactions",
    transactionAnalysis: "Transaction Analysis",
    transactionInsights: "Transaction Insights",
    transactionRecommendations: "Transaction Recommendations",
    descriptionPlaceholder: "Enter transaction description",
    descriptionHelper: "This description will be used to store the transaction details",
    amount: "Amount",
    currency: "Currency",
    date: "Date",
    addReceipt: "Add Receipt",
    selectDate: "Select Date",
    selectCurrency: "Select Currency",
    fillAllFields: "Please fill in all fields",
    transactionAdded: "Transaction added successfully",
    transactionNotAdded: "Failed to add transaction",
    addRecepit: "Add Receipt",
    tagsPlaceholder: "Enter tags (comma separated)",
    addImage: "Add Image",
    analyzeTransction: "Analyze Transaction",
    //  title: "Finance",
    // manageFinances: "Manage Finances",
    // analyzeTransactions: "Analyze Transactions",
    // addTransaction: "Add Transaction",
    // viewTransactions: "View Transactions",
    // analyze: "Analyze",
    // add: "Add",
    // view: "View",
    // transactions: "Transactions",
    // analysis: "Analysis",
    // insights: "Insights",
    // recommendations: "Recommendations",
    // descriptionPlaceholder: "Enter transaction description",
    // descriptionHelper: "This description will be used to store the transaction details",
    // amount: "Amount",
    // currency: "Currency",
    // date: "Date",
    // addReceipt: "Add Receipt",
    // selectDate: "Select Date",
    // selectCurrency: "Select Currency",
    // fillAllFields: "Please fill in all fields",
    // transactionAdded: "Transaction added successfully",
    // transactionNotAdded: "Failed to add transaction",
    // addRecepit: "Add Receipt",
    // tagsPlaceholder: "Enter tags (comma separated)",
    // addImage: "Add Image",
    analyzeTransaction: "Analyze Transaction",
    description: "Description",
    receipt: "Receipt",
    tags: "Tags",
    highCost: "High Cost",
    forCrop: "For Crop",
    inField: "In Field",
    category: "Category",
    "categories": {
      "feed": "Feed",
      "treatment": "Treatment",
      "medicine": "Medicine",
      "vaccination": "Vaccination",
      "breeding": "Breeding",
      "animal_transport": "Animal Transport",
      "animal_sales": "Animal Sales",
      "animal_byproducts": "Animal Byproducts",
      "seeds": "Seeds",
      "fertilizer": "Fertilizer",
      "pesticides": "Pesticides",
      "irrigation": "Irrigation",
      "crop_transport": "Crop Transport",
      "crop_sales": "Crop Sales",
      "soil_testing": "Soil Testing",
      "field_preparation": "Field Preparation",
      "harvest": "Harvest",
      "plant_care": "Plant Care",
      "gardening_tools": "Gardening Tools",
      "plant_seeds": "Plant Seeds",
      "plant_nutrition": "Plant Nutrition",
      "equipment_maintenance": "Equipment Maintenance",
      "equipment_repair": "Equipment Repair",
      "fuel": "Fuel",
      "equipment_rental_income": "Equipment Rental Income",
      "equipment_sales": "Equipment Sales",
      "labor": "Labor",
      "transport": "Transport",
      "consulting": "Consulting",
      "other": "Other"
    },
    // categories: {
    //   seedCost: "Seed Cost",
    //   irrigation: "Irrigation",
    //   pesticide: "Pesticide",
    //   fertilizer: "Fertilizer",
    //   labor: "Labor",
    //   equipment: "Equipment",
    //   harvestSale: "Harvest Sale",
    //   other: "Other"
    // },
    "add_record": "Add Record",
    "view_records": "View Records",
    "entity_type": "Entity Type",
    "entity_id": "Entity",
    "transaction_type": "Transaction Type",
    "expense": "Expense",
    "income": "Income",
    "category": "Category",
    "amount": "Amount",
    "currency": "Currency",
    "date": "Date",
    "notes": "Notes",
    "placeholders": {
      "enter_notes": "Enter notes here",
      "select_currency": "Select Currency"
    },
    "common": {
      "save": "Save",
      "filter": "Filter"
    },
    "currencyplaceholder": "Select Currency",
    "categoryplaceholder": "Select Category",
    "entity_namePlaceholder": "Enter Entity Name",
    "entity_typePlaceholder": "Select Entity Type",
    "filter_by_entity_type": "Filter by Entity Type",
    "filter_by_entity": "Filter by Entity",
    "filter_by_transaction_type": "Filter by Transaction Type",
    "filter_by_category": "Filter by Category",
    "filter_by_currency": "Filter by Currency",
    "filter_by_date": "Filter by Date",
    "entity_Name": "Entity Name",


  },
  "checklist": {
    "title": "Checklist Items",
    "required": "Required",
    "enterTitle": "Enter item title",
    "addCustomItem": "Add Custom Item",
    "evidenceImages": "Evidence Images",
    "addImage": "Add Image",
    "notes": "Notes",
    "notesPlaceholder": "Add any additional notes here..."
  },
  form: {
    Sheep: "Sheep",
    "Brahman": "Brahman",
    "Angus": "Angus",
    "Holstein": "Holstein",
    "Jersey": "Jersey",
    "Guernsey": "Guernsey",
    "Ayrshire": "Ayrshire",
    "Brown Swiss": "Brown Swiss",
    "Milking Shorthorn": "Milking Shorthorn",
    "Red Poll": "Red Poll",
    "Devon": "Devon",
    "Dexter": "Dexter",
    itemId: "Item ID (e.g., PLT-001)",
    itemIdPlaceholder: "Enter unique item ID",
    itemIdHelp: "This ID will be used to generate a QR code for this plant",
    plantName: "Plant Name",
    plantNamePlaceholder: "Enter plant name",
    species: "Species",
    speciesPlaceholder: "Enter plant species",
    variety: "Variety (Optional)",
    varietyPlaceholder: "Enter plant variety",
    plantedDate: "Planted Date",
    expectedHarvestDate: "Expected Harvest Date (Optional)",
    expectedHarvestDatePlaceholder: "Select a date",
    season: "Season",
    selectDatePlaceholder: "Select a date",
    status: "Status",
    selectStatus: "Select Status",
    healthStatus: "Health Status",
    location: "Location",
    selectGarden: "Select Garden",
    selectField: "Select Field",
    or: "OR",
    notes: "Notes (Optional)",
    notesPlaceholder: "Enter any additional notes",
    cancel: "Cancel",
    addPlant: "Add Plant",
    adding: "Adding...",
    Seedling: "Seedling",
    Growing: "Growing",
    Flowering: "Flowering",
    Fruiting: "Fruiting",
    Harvested: "Harvested",
    Dormant: "Dormant",
    Excellent: " Excellent",
    Good: "Good",
    Fair: "Fair",
    Poor: "Poor",

    "Animal Name (Optional)": "Animal Name (Optional)",
    "Enter animal name": "Enter animal name",
    "Species": "Species",
    "Select Species": "Select Species",
    "Breed (Optional)": "Breed (Optional)",
    "Select Breed": "Select Breed",
    "Birth Date (Optional)": "Birth Date (Optional)",
    "Select a date": "Select a date",
    "Gender": "Gender",
    "Select Gender": "Select Gender",
    "Health Status": "Health Status",
    "Select Health Status": "Select Health Status",
    "Purpose": "Purpose",
    "Select Purpose": "Select Purpose",
    "Identification Number (Optional)": "Identification Number (Optional)",
    "Enter animal ID number": "Enter animal ID number",
    "Field Location (Optional)": "Field Location (Optional)",
    "Select Field": "Select Field",
    "Notes (Optional)": "Notes (Optional)",
    "Enter any additional notes": "Enter any additional notes",
    "Cancel": "Cancel",
    "Add Animal": "Add Animal",
    "Adding...": "Adding...",
    "No Fields Available": "No Fields Available",
    "Please create a field first.": "Please create a field first.",
    "Create Field": "Create Field",
    "Cow": "Cow",
    "Goat": "Goat",
    "Sheep": "Sheep",
    "Chicken": "Chicken",
    "Deer": "Deer",
    "Horse": "Horse",
    "Donkey": "Donkey",
    "Mule": "Mule",
    "Buffalo": "Buffalo",
    "Rabbit": "Rabbit",
    "Male": "Male",
    "Female": "Female",
    "Unknown": "Unknown",
    "Healthy": "Healthy",
    "Sick": "Sick",
    "Pregnant": "Pregnant",
    "Nursing": "Nursing",
    "Quarantined": "Quarantined",
    "Dairy": "Dairy",
    "Meat": "Meat",
    "Wool": "Wool",
    "Eggs": "Eggs",
    "Breeding": "Breeding",
    "Work": "Work",
    "Pet": "Pet",
    "Fish": "Fish",
    "Poultry": "Poultry",
    "itemIdForQR": "Item ID (for QR code)",
    "Domestic Turkey": "Domestic Turkey",
    "Jamnapari": "Jamnapari",
    "Unknown": "Unknown",
    "Holstein Friesian": "Holstein Friesian",
    "Boer": "Boer",
    "Sahiwal": "Sahiwal",
    "Jersey": "Jersey"
  }
};
