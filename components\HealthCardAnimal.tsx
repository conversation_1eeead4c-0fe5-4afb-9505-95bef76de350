import { normalizeDate } from '@/utils/dateUtils';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
// import { format } from 'date-fns';

interface HealthCheck {
  date: number;
  nextCheckDate: number;
  temperature: number;
  weight: number;
  respiration: string;
  hydration: string;
  coat: string;
  gait: string;
  appetite: string;
  fecal: string;
  eyes: string;
  ears: string;
  abnormalities: boolean;
  notes: string;
}

export const HealthCardAnimal = ({ check }: { check: HealthCheck }) => {
  return (
    <View style={[styles.card, check.abnormalities && styles.abnormalCard]}>
      <Text style={styles.date}>
        Checked: {normalizeDate(new Date(check.date))}
      </Text>
      <Text style={styles.subDate}>
        Next Check: {normalizeDate(new Date(check.nextCheckDate))}
      </Text>

      <View style={styles.row}>
        <Text style={styles.label}>Temperature:</Text>
        <Text>{check.temperature}°F</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Weight:</Text>
        <Text>{check.weight} kg</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Respiration:</Text>
        <Text>{check.respiration}</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Appetite:</Text>
        <Text>{check.appetite}</Text>
      </View>

      {/* Optional Notes */}
      {check.notes?.trim() !== '' && (
        <View style={styles.row}>
          <Text style={styles.label}>Notes:</Text>
          <Text>{check.notes}</Text>
        </View>
      )}

      {check.abnormalities && (
        <Text style={styles.warning}>⚠️ Abnormalities detected</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    padding: 14,
    marginVertical: 8,
    borderRadius: 10,
    elevation: 2,
  },
  abnormalCard: {
    borderLeftWidth: 5,
    borderLeftColor: 'red',
    backgroundColor: '#ffe5e5',
  },
  date: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  subDate: {
    fontSize: 14,
    color: '#555',
    marginBottom: 10,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  label: {
    fontWeight: '600',
  },
  warning: {
    color: 'red',
    fontWeight: 'bold',
    marginTop: 10,
  },
});
