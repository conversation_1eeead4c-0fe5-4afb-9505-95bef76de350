import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { DollarSign } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';

interface FinanceHandlerButtonProps {
  onPress: () => void;
  entityType: 'plant' | 'garden';
}

export default function FinanceHandlerButton({ onPress, entityType }: FinanceHandlerButtonProps) {
  const { t } = useTranslation();
  
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <DollarSign size={20} color={colors.white} />
      </View>
      <Text style={styles.text}>{t('finance.handleFinance')}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    backgroundColor: colors.primary,
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[800],
  }
});