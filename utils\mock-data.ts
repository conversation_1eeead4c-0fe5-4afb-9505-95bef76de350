import { Farm, Field, Task, InventoryItem, Garden, Plant, Animal, Equipment, Yield } from '@/types';

// Mock data for farms
export const mockFarms: Partial<Farm>[] = [
  {
    name: 'Green Valley Farm',
    location: 'Lahore, Punjab',
    size: 120,
    sizeUnit: 'acres',
    description: 'A large mixed-use farm with crops and livestock',
    type: 'mixed',
    status: 'active',
  },
  {
    name: 'Riverside Orchards',
    location: 'Multan, Punjab',
    size: 45,
    sizeUnit: 'acres',
    description: 'Fruit orchard specializing in mangoes and citrus',
    type: 'orchard',
    status: 'active',
  },
  {
    name: 'Mountain View Ranch',
    location: 'Islamabad, Federal Territory',
    size: 200,
    sizeUnit: 'acres',
    description: 'Livestock ranch with some crop production',
    type: 'livestock',
    status: 'active',
  }
];

// Mock data for fields/zones
export const mockFields: Partial<Field>[] = [
  {
    name: 'North Field',
    type: 'cropland',
    size: 25,
    sizeUnit: 'acres',
    status: 'active',
    cropType: 'Wheat',
    plantedDate: new Date(2023, 9, 15).toISOString(),
    harvestDate: new Date(2024, 3, 10).toISOString(),
    health: 85,
    location: 'Northern section of the farm',
    image: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80',
  },
  {
    name: 'South Field',
    type: 'cropland',
    size: 30,
    sizeUnit: 'acres',
    status: 'active',
    cropType: 'Rice',
    plantedDate: new Date(2023, 5, 1).toISOString(),
    harvestDate: new Date(2023, 10, 15).toISOString(),
    health: 92,
    location: 'Southern section of the farm',
    image: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80',
  },
  {
    name: 'East Pasture',
    type: 'animal',
    size: 40,
    sizeUnit: 'acres',
    status: 'active',
    health: 78,
    location: 'Eastern section of the farm',
    image: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80',
  },
  {
    name: 'West Orchard',
    type: 'cropland',
    size: 15,
    sizeUnit: 'acres',
    status: 'active',
    cropType: 'Mango',
    plantedDate: new Date(2020, 2, 10).toISOString(),
    health: 88,
    location: 'Western section of the farm',
    image: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80',
  }
];

// Mock data for tasks
export const mockTasks: Partial<Task>[] = [
  {
    title: 'Irrigate North Field',
    description: 'Set up irrigation system for the wheat crop in North Field',
    status: 'pending',
    priority: 'high',
    dueDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    category: 'irrigation',
  },
  {
    title: 'Apply Fertilizer',
    description: 'Apply nitrogen fertilizer to the rice crop in South Field',
    status: 'pending',
    priority: 'medium',
    dueDate: new Date(Date.now() + 172800000).toISOString(), // Day after tomorrow
    category: 'fertilization',
  },
  {
    title: 'Repair Fence',
    description: 'Fix the broken fence in the East Pasture',
    status: 'pending',
    priority: 'low',
    dueDate: new Date(Date.now() + 259200000).toISOString(), // 3 days from now
    category: 'maintenance',
  },
  {
    title: 'Harvest Mangoes',
    description: 'Harvest ripe mangoes from the West Orchard',
    status: 'completed',
    priority: 'high',
    dueDate: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    completedAt: new Date().toISOString(),
    category: 'harvest',
  }
];

// Mock data for inventory
export const mockInventory: Partial<InventoryItem>[] = [
  {
    name: 'Wheat Seeds',
    category: 'resource',
    quantity: 500,
    unit: 'kg',
    status: 'active',
  },
  {
    name: 'Nitrogen Fertilizer',
    category: 'resource',
    quantity: 200,
    unit: 'kg',
    status: 'active',
  },
  {
    name: 'Pesticide',
    category: 'resource',
    quantity: 50,
    unit: 'liters',
    status: 'active',
  },
  {
    name: 'Irrigation Pipes',
    category: 'equipment',
    quantity: 100,
    unit: 'meters',
    status: 'active',
  }
];

// Mock data for gardens
export const mockGardens: Partial<Garden>[] = [
  {
    name: 'Vegetable Garden',
    type: 'garden',
    size: 0.5,
    sizeUnit: 'acres',
    status: 'active',
    soilType: 'Loam',
    irrigationSystem: 'Drip',
    image: 'https://images.unsplash.com/photo-1466692476868-aef1dfb1e735?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dmVnZXRhYmxlJTIwZ2FyZGVufGVufDB8fDB8fA%3D%3D&w=1000&q=80',
  },
  {
    name: 'Herb Garden',
    type: 'garden',
    size: 0.25,
    sizeUnit: 'acres',
    status: 'active',
    soilType: 'Sandy Loam',
    irrigationSystem: 'Sprinkler',
    image: 'https://images.unsplash.com/photo-1466692476868-aef1dfb1e735?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dmVnZXRhYmxlJTIwZ2FyZGVufGVufDB8fDB8fA%3D%3D&w=1000&q=80',
  }
];

// Mock data for plants
export const mockPlants: Partial<Plant>[] = [
  {
    name: 'Tomato Plants',
    species: 'Solanum lycopersicum',
    variety: 'Roma',
    plantedDate: new Date(2023, 2, 15).toISOString(),
    expectedHarvestDate: new Date(2023, 5, 15).toISOString(),
    status: 'growing',
    health: 'good',
    notes: 'Growing well, need regular watering',
    image: 'https://images.unsplash.com/photo-1592841200221-a6898f307baa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dG9tYXRvJTIwcGxhbnR8ZW58MHx8MHx8&w=1000&q=80',
  },
  {
    name: 'Mint',
    species: 'Mentha',
    variety: 'Spearmint',
    plantedDate: new Date(2023, 1, 10).toISOString(),
    status: 'growing',
    health: 'excellent',
    notes: 'Spreading quickly, may need to be contained',
    image: 'https://images.unsplash.com/photo-1592841200221-a6898f307baa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dG9tYXRvJTIwcGxhbnR8ZW58MHx8MHx8&w=1000&q=80',
  }
];

// Mock data for animals
export const mockAnimals: Partial<Animal>[] = [
  {
    name: 'Daisy',
    species: 'Cow',
    breed: 'Holstein',
    birthDate: new Date(2020, 3, 12).toISOString(),
    gender: 'female',
    status: 'healthy',
    purpose: 'dairy',
    identificationNumber: 'COW-001',
    notes: 'Good milk producer',
    image: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8Y293fGVufDB8fDB8fA%3D%3D&w=1000&q=80',
  },
  {
    name: 'Rex',
    species: 'Goat',
    breed: 'Boer',
    birthDate: new Date(2021, 5, 20).toISOString(),
    gender: 'male',
    status: 'healthy',
    purpose: 'meat',
    identificationNumber: 'GOAT-001',
    notes: 'Growing well',
    image: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8Y293fGVufDB8fDB8fA%3D%3D&w=1000&q=80',
  }
];

// Mock data for equipment
export const mockEquipment: Partial<Equipment>[] = [
  {
    name: 'Tractor',
    type: 'vehicle',
    manufacturer: 'John Deere',
    model: '5075E',
    purchaseDate: new Date(2019, 1, 15).toISOString(),
    purchasePrice: 3500000,
    status: 'operational',
    lastMaintenanceDate: new Date(2023, 1, 10).toISOString(),
    nextMaintenanceDate: new Date(2023, 7, 10).toISOString(),
    notes: 'Regular maintenance required',
    image: 'https://images.unsplash.com/photo-1605002123082-f87f91b3d3b6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dHJhY3RvcnxlbnwwfHwwfHw%3D&w=1000&q=80',
  },
  {
    name: 'Plow',
    type: 'tool',
    manufacturer: 'AgriPro',
    model: 'AP-200',
    purchaseDate: new Date(2020, 3, 5).toISOString(),
    purchasePrice: 150000,
    status: 'operational',
    notes: 'Works well with the tractor',
    image: 'https://images.unsplash.com/photo-1605002123082-f87f91b3d3b6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dHJhY3RvcnxlbnwwfHwwfHw%3D&w=1000&q=80',
  }
];

// Mock data for yields
export const mockYields: Partial<Yield>[] = [
  {
    name: 'Wheat Harvest 2023',
    cropType: 'Wheat',
    harvestDate: new Date(2023, 3, 15).toISOString(),
    quantity: 5000,
    unit: 'kg',
    quality: 'good',
    notes: 'Good yield despite less rainfall',
    image: 'https://images.unsplash.com/photo-1574323347407-f5e1c5a1ec21?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8d2hlYXQlMjBoYXJ2ZXN0fGVufDB8fDB8fA%3D%3D&w=1000&q=80',
  },
  {
    name: 'Rice Harvest 2023',
    cropType: 'Rice',
    harvestDate: new Date(2023, 10, 20).toISOString(),
    quantity: 4500,
    unit: 'kg',
    quality: 'excellent',
    notes: 'Excellent quality due to good irrigation',
    image: 'https://images.unsplash.com/photo-1574323347407-f5e1c5a1ec21?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8d2hlYXQlMjBoYXJ2ZXN0fGVufDB8fDB8fA%3D%3D&w=1000&q=80',
  }
];

// Function to generate a random user ID
export const generateUserId = () => {
  return 'user_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random farm ID
export const generateFarmId = () => {
  return 'farm_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random field ID
export const generateFieldId = () => {
  return 'field_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random task ID
export const generateTaskId = () => {
  return 'task_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random inventory item ID
export const generateInventoryItemId = () => {
  return 'item_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random garden ID
export const generateGardenId = () => {
  return 'garden_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random plant ID
export const generatePlantId = () => {
  return 'plant_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random animal ID
export const generateAnimalId = () => {
  return 'animal_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random equipment ID
export const generateEquipmentId = () => {
  return 'equip_' + Math.random().toString(36).substr(2, 9);
};

// Function to generate a random yield ID
export const generateYieldId = () => {
  return 'yield_' + Math.random().toString(36).substr(2, 9);
};