import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from 'react-native';
import { colors } from '@/constants/colors';
import { analyzeTransactionWithAI, suggestTransactionCorrections } from '@/utils/transaction-analyzer';
import { TransactionCategory } from '@/types';
import { DollarSign, AlertTriangle, Check } from 'lucide-react-native';

interface FinancialDataAnalyzerProps {
  onAnalysisComplete: (analysis: {
    description: string;
    amount: number;
    category: TransactionCategory;
    isExpense: boolean;
    suggestedPlantId?: string;
    suggestedFieldId?: string;
    estimatedProfit?: number;
  }) => void;
}

export default function FinancialDataAnalyzer({ onAnalysisComplete }: FinancialDataAnalyzerProps) {
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [hasAppliedCorrections, setHasAppliedCorrections] = useState(false);

  const handleAnalyze = () => {
    if (!description) {
      Alert.alert('Error', 'Please enter a transaction description');
      return;
    }

    if (!amount || isNaN(Number(amount))) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    setIsAnalyzing(true);
    
    // Simulate API delay
    setTimeout(() => {
      const numericAmount = parseFloat(amount);
      const analysis = analyzeTransactionWithAI(description, numericAmount);
      setAnalysisResult({
        ...analysis,
        description,
        amount: numericAmount
      });
      setIsAnalyzing(false);
    }, 1000);
  };

  const handleApplyCorrections = () => {
    if (!analysisResult) return;

    const corrections = suggestTransactionCorrections(
      description,
      analysisResult.amount,
      analysisResult.category
    );

    if (corrections.correctedAmount !== undefined) {
      setAmount(corrections.correctedAmount.toString());
      setAnalysisResult({
        ...analysisResult,
        amount: corrections.correctedAmount,
        anomalyDetected: false,
        anomalyReason: undefined
      });
    }

    if (corrections.correctedCategory) {
      setAnalysisResult({
        ...analysisResult,
        category: corrections.correctedCategory,
        anomalyDetected: false,
        anomalyReason: undefined
      });
    }

    setHasAppliedCorrections(true);
  };

  const handleConfirm = () => {
    if (!analysisResult) return;
    
    onAnalysisComplete({
      description: analysisResult.description,
      amount: analysisResult.amount,
      category: analysisResult.category,
      isExpense: analysisResult.isExpense,
      suggestedPlantId: analysisResult.suggestedPlantId,
      suggestedFieldId: analysisResult.suggestedFieldId,
      estimatedProfit: analysisResult.estimatedProfit
    });
    
    // Reset form
    setDescription('');
    setAmount('');
    setAnalysisResult(null);
    setHasAppliedCorrections(false);
  };

  // Helper function to format category name
  const formatCategoryName = (category: string): string => {
    return category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Financial Data Analyzer</Text>
      
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Transaction Description</Text>
        <TextInput
          style={styles.input}
          value={description}
          onChangeText={setDescription}
          placeholder="E.g., Purchased tomato seeds, Sold 10kg tomatoes"
          multiline
        />
      </View>
      
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Amount</Text>
        <View style={styles.amountInputContainer}>
          <DollarSign size={20} color={colors.gray[500]} />
          <TextInput
            style={styles.amountInput}
            value={amount}
            onChangeText={setAmount}
            placeholder="0.00"
            keyboardType="numeric"
          />
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.analyzeButton}
        onPress={handleAnalyze}
        disabled={isAnalyzing}
      >
        {isAnalyzing ? (
          <ActivityIndicator color={colors.white} />
        ) : (
          <Text style={styles.buttonText}>Analyze Transaction</Text>
        )}
      </TouchableOpacity>
      
      {analysisResult && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Analysis Result</Text>
          
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Transaction Type:</Text>
            <Text style={styles.resultValue}>
              {analysisResult.isExpense ? 'Expense' : 'Income'}
            </Text>
          </View>
          
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Category:</Text>
            <Text style={styles.resultValue}>
              {formatCategoryName(analysisResult.category)}
            </Text>
          </View>
          
          {analysisResult.suggestedPlantId && (
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Related Plant:</Text>
              <Text style={styles.resultValue}>{analysisResult.suggestedPlantId}</Text>
            </View>
          )}
          
          {analysisResult.suggestedFieldId && (
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Related Field:</Text>
              <Text style={styles.resultValue}>{analysisResult.suggestedFieldId}</Text>
            </View>
          )}
          
          {analysisResult.estimatedProfit !== undefined && (
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Estimated Profit:</Text>
              <Text style={[styles.resultValue, styles.profitText]}>
                ${analysisResult.estimatedProfit.toFixed(2)}
              </Text>
            </View>
          )}
          
          {analysisResult.anomalyDetected && (
            <View style={styles.anomalyContainer}>
              <AlertTriangle size={20} color={colors.danger} />
              <Text style={styles.anomalyText}>{analysisResult.anomalyReason}</Text>
              
              <TouchableOpacity 
                style={styles.correctButton}
                onPress={handleApplyCorrections}
                disabled={hasAppliedCorrections}
              >
                <Text style={styles.correctButtonText}>
                  {hasAppliedCorrections ? 'Corrected' : 'Apply Correction'}
                </Text>
                {hasAppliedCorrections && <Check size={16} color={colors.success} />}
              </TouchableOpacity>
            </View>
          )}
          
          <TouchableOpacity 
            style={[
              styles.confirmButton,
              analysisResult.anomalyDetected && !hasAppliedCorrections && styles.disabledButton
            ]}
            onPress={handleConfirm}
            disabled={analysisResult.anomalyDetected && !hasAppliedCorrections}
          >
            <Text style={styles.buttonText}>Confirm & Save</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 6,
    padding: 12,
    fontSize: 16,
    color: colors.gray[800],
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 6,
    paddingHorizontal: 12,
  },
  amountInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    color: colors.gray[800],
  },
  analyzeButton: {
    backgroundColor: colors.primary,
    borderRadius: 6,
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  resultContainer: {
    marginTop: 24,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: 16,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingVertical: 4,
  },
  resultLabel: {
    fontSize: 14,
    color: colors.gray[600],
  },
  resultValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  profitText: {
    color: colors.success,
  },
  anomalyContainer: {
    backgroundColor: colors.danger + '15',
    borderRadius: 6,
    padding: 12,
    marginVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  anomalyText: {
    color: colors.danger,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  correctButton: {
    backgroundColor: colors.white,
    borderRadius: 4,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  correctButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  confirmButton: {
    backgroundColor: colors.success,
    borderRadius: 6,
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  disabledButton: {
    backgroundColor: colors.gray[400],
  },
});