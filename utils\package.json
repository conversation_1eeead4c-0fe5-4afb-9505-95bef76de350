{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.5", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/native": "^7.0.0", "@tmcw/togeojson": "^7.1.1", "expo": "~52.0.36", "expo-auth-session": "^6.0.3", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.0.1", "expo-camera": "~16.0.18", "expo-constants": "~17.0.7", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.0", "expo-image": "~2.0.6", "expo-image-manipulator": "^13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "^14.0.1", "expo-linking": "~7.0.3", "expo-location": "~18.0.7", "expo-media-library": "~17.0.6", "expo-router": "~4.0.17", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.6", "expo-updates": "~0.27.4", "expo-web-browser": "^14.0.2", "fflate": "^0.8.2", "firebase": "^11.6.0", "i18n-js": "^4.5.1", "lucide-react-native": "^0.475.0", "nanoid": "^5.1.5", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-chart-kit": "^6.12.0", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.16.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.19.13", "xmldom": "^0.6.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~18.3.12", "@types/uuid": "^10.0.0", "@types/xmldom": "^0.1.34", "typescript": "~5.8.2"}, "private": true}