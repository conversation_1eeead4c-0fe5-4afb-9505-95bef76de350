import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
  TextInput,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import MultipleImagePicker from '@/components/MultipleImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import {
  Droplets,
  ChevronDown,
  Ruler,
  TreeDeciduous,
  MapPin,
  Map,
  Navigation,
} from 'lucide-react-native';
import { analyzeImageWithVision } from '@/utils/openai-vision';
import * as Location from 'expo-location';
import MapView, { Marker } from 'react-native-maps';
import { reverseGeocodeAsync } from 'expo-location';
import { v4 as uuidv4 } from 'uuid';
import DropdownPicker from '@/components/DropdownPicker';
import { useTranslation } from '@/i18n/useTranslation';
import MaterialCommunityIcons from '@expo/vector-icons/build/MaterialCommunityIcons';
import { useLookupStore } from '@/store/lookup-store';
import Toast from 'react-native-toast-message';

// Soil type options
const SOIL_TYPES = [
  'Clay',
  'Sandy',
  'Loam',
  'Silt',
  'Peat',
  'Chalk',
  'Sandy Loam',
  'Clay Loam',
  'Silty Clay',
  'Sandy Clay',
  'Other'
];

export default function CreateGardenScreen() {
  const {
    addGarden,
    currentFarm,
    getGarden,
    updateGarden,
    farms,
    fetchFarms,
    setCurrentFarm,
    getGardenUpdated,
    // isLoading
  } = useFarmStore();
  const { user } = useAuthStore();
  const { getLookupsByCategory, getLookupsByCategoryParssedData } = useLookupStore();
  const { id } = useLocalSearchParams();
  const isEditMode = id ? true : false;

  const [name, setName] = useState('');
  const [type, setType] = useState<'vegetable' | 'flower' | 'herb' | 'fruit' | 'mixed'>('vegetable');
  const [size, setSize] = useState('');
  const [sizeUnit, setSizeUnit] = useState<'sq_m' | 'sq_ft' | 'acres' | 'ha'>('sq_m');
  const [status, setStatus] = useState<'active' | 'inactive' | 'planning'>('active');
  const [soilType, setSoilType] = useState('');
  const [irrigationSystem, setIrrigationSystem] = useState('');
  const [latitude, setLatitude] = useState('');
  const [longitude, setLongitude] = useState('');
  const [address, setAddress] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showMapModal, setShowMapModal] = useState(false);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    name: '',
    size: '',
    type: '',
    images: ''
  });

  const [showTypeDropdown, setShowTypeDropdown] = useState(false);
  const [showSizeUnitDropdown, setShowSizeUnitDropdown] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showSoilTypeDropdown, setShowSoilTypeDropdown] = useState(false);

  const { t, locale, setLocale, isRTL } = useTranslation();


  //  const gardenTypeArray = [
  //   {
  //     label: t('entity.garden.typeVegetable'),
  //     value: 'vegetable',
  //     icon: <MaterialCommunityIcons name="food-apple" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.typeFlower'),
  //     value: 'flower',
  //     icon: <MaterialCommunityIcons name="flower" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.typeHerb'),
  //     value: 'herb',
  //     icon: <MaterialCommunityIcons name="leaf" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.typeFruit'),
  //     value: 'fruit',
  //     icon: <MaterialCommunityIcons name="fruit-cherries" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.typeMixed'),
  //     value: 'mixed',
  //     icon: <MaterialCommunityIcons name="blur" size={24} color="black" />,
  //   },
  // ];
  //  const soilTypeArray = [
  //   {
  //     label: t('entity.garden.soilTypeClay'),
  //     value: 'clay',
  //     icon: <MaterialCommunityIcons name="terrain" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeSandy'),
  //     value: 'sandy',
  //     icon: <MaterialCommunityIcons name="weather-sunny" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeLoam'),
  //     value: 'loam',
  //     icon: <MaterialCommunityIcons name="grain" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeSilt'),
  //     value: 'silt',
  //     icon: <MaterialCommunityIcons name="waves" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypePeat'),
  //     value: 'peat',
  //     icon: <MaterialCommunityIcons name="leaf" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeChalk'),
  //     value: 'chalk',
  //     icon: <MaterialCommunityIcons name="white-balance-sunny" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeSandyLoam'),
  //     value: 'sandyLoam',
  //     icon: <MaterialCommunityIcons name="grain" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeClayLoam'),
  //     value: 'clayLoam',
  //     icon: <MaterialCommunityIcons name="terrain" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeSiltyClay'),
  //     value: 'siltyClay',
  //     icon: <MaterialCommunityIcons name="waves" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeSandyClay'),
  //     value: 'sandyClay',
  //     icon: <MaterialCommunityIcons name="weather-sunny" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.soilTypeOther'),
  //     value: 'other',
  //     icon: <MaterialCommunityIcons name="help-circle-outline" size={24} color="black" />,
  //   },
  // ];
  // const irrigationSystemArray = [
  //   { label: 'Drip', value: 'drip' },
  //   { label: 'Sprinkler', value: 'sprinkler' },
  //   { label: 'Manual', value: 'manual' },
  //   { label: 'Automated', value: 'automated' },
  //   { label: 'Flood', value: 'flood' },
  //   { label: 'Other', value: 'other' },
  // ];
  // const sizeUnitArray = [
  //   {
  //     label: t('entity.garden.sq_m'),
  //     value: 'sq_m',
  //     icon: <MaterialCommunityIcons name="square-medium" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.unitSquareFoot'),
  //     value: 'sq_ft',
  //     icon: <MaterialCommunityIcons name="square-foot" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.unitAcres'),
  //     value: 'acres',
  //     icon: <MaterialCommunityIcons name="terrain" size={24} color="black" />,
  //   },
  //   {
  //     label: t('entity.garden.unitHectares'),
  //     value: 'ha',
  //     icon: <MaterialCommunityIcons name="map" size={24} color="black" />,
  //   },
  // ];
  // const statusArray = [
  //   {
  //     label: t('entity.garden.statusActive'),
  //     value: 'active',
  //     icon: <MaterialCommunityIcons name="check-circle-outline" size={24} color="green" />,
  //   },
  //   {
  //     label: t('entity.garden.statusInactive'),
  //     value: 'inactive',
  //     icon: <MaterialCommunityIcons name="close-circle-outline" size={24} color="gray" />,
  //   },
  //   {
  //     label: t('entity.garden.statusPlanning'),
  //     value: 'planning',
  //     icon: <MaterialCommunityIcons name="calendar-clock" size={24} color="orange" />,
  //   },
  // ];


  const [gardenTypeArray, setGardenTypeArray] = useState([] as any)
  const [soilTypeArray, setSoilTypeArray] = useState([] as any)
  const [irrigationSystemArray, setIrrigationSystemArray] = useState([] as any)
  const [sizeUnitArray, setSizeUnitArray] = useState([] as any)
  const [statusArray, setStatusArray] = useState([] as any)
  // const [statusArray,setStatusArray]=useState([] as any)
  useEffect(() => {
    // const gardenTypeArray = getLookupsByCategory('')
    // const soilTypeArray = getLookupsByCategory('')
    // const irrigationSystemArray = getLookupsByCategory('')
    // const sizeUnitArray = getLookupsByCategory('')


    // const res1 = gardenTypeArray.map((item) => ({ lable: item?.title, value: item?.id,icon:"" }))
    // console.log({ gardenTypeArray }, { soilTypeArray }, { irrigationSystemArray }, { res1 })
    // soilTypeArray.map((item) => ({ lable: item?.title, value: item?.id,icon:"" }))
    // irrigationSystemArray.map((item) => ({ lable: item?.title, value: item?.id,icon:'' }))
    // sizeUnitArray.map((item) => ({ lable: item?.title, value: item?.id ,icon:''}))
    // console.log({ res1 })
    setGardenTypeArray(getLookupsByCategoryParssedData('gardenType', 'entity.garden.type'))
    setSoilTypeArray(getLookupsByCategoryParssedData('soilType', 'entity.garden.soilType'))
    setIrrigationSystemArray(getLookupsByCategoryParssedData('irrigationSystemStatus', 'entity.garden.irrigationSystem'))
    setSizeUnitArray(getLookupsByCategoryParssedData('areaUnit', 'common.areaUnit.'))
    setStatusArray(getLookupsByCategoryParssedData('status', 'entity.field.status'))

  }, [])

  // const gardenType = gardenTypeArray.find((item) =>
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  const [initialRegion, setInitialRegion] = useState({
    latitude: 30.3753, // Default to center of Pakistan
    longitude: 69.3451,
    // latitudeDelta: 0.0922,
    // longitudeDelta: 0.0421,
    latitudeDelta: 0.005,
    longitudeDelta: 0.005,
  });

  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required to select garden location');
        return;
      }

      try {
        const location = await Location.getCurrentPositionAsync({});
        const { latitude, longitude } = location.coords;
        // console.log({ latitude }, { longitude })
        setInitialRegion(prev => ({
          ...prev,
          latitude,
          longitude,
          latitudeDelta: 0.009,
          longitudeDelta: 0.009,
        }));
      } catch (error) {
        console.error('Error getting location:', error);
      }
    })();
  }, []);
  const [garden, setGarden] = useState({} as any)
  const loadGardenData = async (gardenIdString: string) => {
    const res = await getGardenUpdated(currentFarm?.id, gardenIdString);
    setGarden(res)
    // console.log({ res })
    // return res
  }

  useEffect(() => {
    if (isEditMode && id) {
      const gardenIdString = Array.isArray(id) ? id[0] : id as string;
        setIsLoading(true);
      loadGardenData(gardenIdString)

    }
  }, [isEditMode, id, farms, getGarden]);
  useEffect(() => {
    // console.log({ garden })
    if (garden) {

      setName(garden.name || '');
      setType(garden.gardenType || 'vegetable');
      setSize(garden.size ? garden.size.toString() : '');
      setSizeUnit(garden.sizeUnit || 'sqft');
      setStatus(garden.status || 'active');
      setSoilType(garden.soilType || '');
      setIrrigationSystem(garden.irrigationSystem || '');

      // Set location if available
      if (garden.location) {
        if (garden.location.latitude) setLatitude(garden.location.latitude.toString());
        if (garden.location.longitude) setLongitude(garden.location.longitude.toString());
        if (garden.location.address) setAddress(garden.location.address);
      }

      // Set image if available
      if (garden.image) {
        setImageUri(garden.image);
      }

      // Set farm if available
      if (garden.farmId) {
        const gardenFarm = farms.find(f => f.id === garden.farmId);
        if (gardenFarm) {
          setCurrentFarm(gardenFarm);
        }
      }
              
    }
    setIsLoading(false);
  }, [garden])

  const validateFields = () => {
    const errors = {
      name: '',
      size: '',
      type: '',
      images: ''
    };

    let hasErrors = false;

    if (!name.trim()) {
      errors.name = t('garden.nameRequired');
      hasErrors = true;
    }

    if (!size || isNaN(Number(size)) || Number(size) <= 0) {
      errors.size = t('garden.sizeRequired');
      hasErrors = true;
    }

    if (!type) {
      errors.type = t('garden.typeRequired');
      hasErrors = true;
    }

    // Status is optional - no validation needed
    // Location is optional - no validation needed
    // Images are optional - no validation needed

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleSubmit = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    try {
      setIsLoading(true);

      // Upload images if selected
      let imageUrls: string[] = [];
      if (images.length > 0) {
        for (const imageUri of images) {
          if (imageUri && !imageUri.startsWith('http')) {
            const uploadedUrl = await uploadImageAsync(imageUri, 'gardens');
            imageUrls.push(uploadedUrl);
          } else if (imageUri) {
            imageUrls.push(imageUri);
          }
        }
      }

      // Use first image as main image
      const mainImage = imageUrls.length > 0 ? imageUrls[0] : '';

      // Create garden data object
      const gardenData: any = {
        id: isEditMode ? id as string : uuidv4(),
        name,
        type,
        size: Number(size),
        sizeUnit,
        status,
        farmId: currentFarm?.id || '',
        image: mainImage,
        images: imageUrls,
        updatedAt: new Date(),
      };

      // Only add createdAt for new gardens
      if (!isEditMode) {
        gardenData.createdAt = new Date();
        gardenData.createdBy = user?.id || '';
      }

      // Only add location if we have coordinates
      if (latitude && longitude) {
        gardenData.location = {
          latitude: parseFloat(latitude),
          longitude: parseFloat(longitude),
          address: address || ''
        };
      }

      // Only add these fields if they have values
      if (soilType) gardenData.soilType = soilType;
      if (irrigationSystem) gardenData.irrigationSystem = irrigationSystem;

      if (isEditMode) {
        await updateGarden(gardenData.id, gardenData);
        Alert.alert('Success', 'Garden updated successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        // Add photos array with the uploaded image for new gardens
        gardenData.photos = [{
          url: imageUrl,
          timestamp: new Date(),
          takenBy: user?.id || ''
        }];

        await addGarden(gardenData);
        Alert.alert('Success', 'Garden created successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} garden:`, error);
      Alert.alert('Error', `Failed to ${isEditMode ? 'update' : 'create'} garden. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectLocation = () => {
    if (selectedLocation) {
      setLatitude(selectedLocation.latitude.toString());
      setLongitude(selectedLocation.longitude.toString());
    }
    setShowMapModal(false);
  };

  const handleMapPress = async (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    setSelectedLocation({
      latitude, longitude,
      latitudeDelta: 0.005,
      longitudeDelta: 0.005,
    });

    try {
      const response = await reverseGeocodeAsync({ latitude, longitude });
      if (response && response[0]) {
        const location = response[0];
        const addressComponents = [
          location.name,
          location.street,
          location.district,
          location.city,
          location.region,
          location.country
        ].filter(Boolean);

        const formattedAddress = addressComponents.join(', ');
        setAddress(formattedAddress);
      }
    } catch (error) {
      console.error('Error getting address:', error);
    }

    setLatitude(latitude.toString());
    setLongitude(longitude.toString());
  };

  // Get size unit display text
  const getSizeUnitText = (unit: string) => {
    switch (unit) {
      case 'sq_m': return 'm²';
      case 'sq_ft': return 'ft²';
      case 'acres': return 'acres';
      case 'ha': return 'ha';
      default: return unit;
    }
  };

  // Handle image analysis results
  const handleAnalysisComplete = (analysis: any) => {
    if (!analysis) return;

    // Only apply results if it's a garden
    if (analysis.category === 'garden') {
      // Set garden name if empty
      if (!name && analysis.type) {
        setName(analysis.type.charAt(0).toUpperCase() + analysis.type.slice(1));
      }

      // Set garden type based on analysis
      if (analysis.type) {
        const gardenType = analysis.type.toLowerCase();
        if (gardenType.includes('vegetable')) {
          setType('vegetable');
        } else if (gardenType.includes('flower')) {
          setType('flower');
        } else if (gardenType.includes('herb')) {
          setType('herb');
        } else if (gardenType.includes('fruit')) {
          setType('fruit');
        } else {
          setType('mixed');
        }
      }

      // Set soil type if detected in details
      if (analysis.details) {
        const details = analysis.details.toLowerCase();

        for (const soil of SOIL_TYPES) {
          if (details.includes(soil.toLowerCase())) {
            setSoilType(soil);
            break;
          }
        }

        // Set irrigation system if mentioned
        if (details.includes('irrigation') || details.includes('watering')) {
          const irrigationMatch = details.match(/(drip|sprinkler|manual|automated|flood) irrigation/i);
          if (irrigationMatch) {
            setIrrigationSystem(irrigationMatch[0]);
          } else if (details.includes('irrigation')) {
            setIrrigationSystem('Irrigation system present');
          }
        }

        // Set status based on details
        if (details.includes('planning') || details.includes('planned')) {
          setStatus('planning');
        } else if (details.includes('inactive') || details.includes('not in use')) {
          setStatus('inactive');
        } else {
          setStatus('active');
        }
      }
    }
  };




  return (
    <>
      <Stack.Screen
        options={{
          title: isEditMode ? t('entity.garden.edit') : t('add.garden'),
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <MultipleImagePicker
            label={t('garden.images')}
            images={images}
            onImagesChange={(newImages) => {
              setImages(newImages);
              if (fieldErrors.images) {
                setFieldErrors(prev => ({ ...prev, images: '' }));
              }
            }}
            maxImages={3}
            placeholder={t('garden.addPhoto')}
            error={fieldErrors.images}
          />

          <View style={styles.formContainer}>
            <Input
              label={t('entity.garden.name')}
              placeholder={t('entity.garden.enterGardenName')}
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (fieldErrors.name) {
                  setFieldErrors(prev => ({ ...prev, name: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              leftIcon={<TreeDeciduous size={20} color={colors.gray[500]} />}
              required={true}
              error={fieldErrors.name}
            />

            {/* <Text style={styles.label}>Garden Type</Text>
            <TouchableOpacity 
              style={styles.dropdownContainer}
              onPress={() => setShowTypeDropdown(!showTypeDropdown)}
            >
              <View style={styles.dropdownHeader}>
                <Text style={styles.dropdownText}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
                <ChevronDown size={20} color={colors.gray[500]} />
              </View>
              
              {showTypeDropdown && (
                <View style={styles.dropdownOptions}>
                  <TouchableOpacity 
                    style={styles.dropdownOption}
                    onPress={() => {
                      setType('vegetable');
                      setShowTypeDropdown(false);
                    }}
                  >
                    <Text style={styles.dropdownOptionText}>Vegetable</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.dropdownOption}
                    onPress={() => {
                      setType('flower');
                      setShowTypeDropdown(false);
                    }}
                  >
                    <Text style={styles.dropdownOptionText}>Flower</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.dropdownOption}
                    onPress={() => {
                      setType('herb');
                      setShowTypeDropdown(false);
                    }}
                  >
                    <Text style={styles.dropdownOptionText}>Herb</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.dropdownOption}
                    onPress={() => {
                      setType('fruit');
                      setShowTypeDropdown(false);
                    }}
                  >
                    <Text style={styles.dropdownOptionText}>Fruit</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.dropdownOption}
                    onPress={() => {
                      setType('mixed');
                      setShowTypeDropdown(false);
                    }}
                  >
                    <Text style={styles.dropdownOptionText}>Mixed</Text>
                  </TouchableOpacity>
                </View>
              )}
            </TouchableOpacity> */}
            {/* <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>{t('entity.garden.type')}</Text> */}
            <DropdownPicker
              label={t('entity.garden.type')}
              options={gardenTypeArray}
              onSelect={(val) => {
                setType(val as any);
                if (fieldErrors.type) {
                  setFieldErrors(prev => ({ ...prev, type: '' }));
                }
              }}
              selectedValue={type}
              isMultiple={false}
              required={true}
              error={fieldErrors.type}
            />

            <View style={styles.rowContainer}>
              <View style={[styles.sizeContainer]}>
                <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>{t('entity.garden.size')}</Text>
                <View style={styles.sizeInputContainer}>
                  <Input
                    placeholder="0.0"
                    value={size}
                    onChangeText={(text) => {
                      setSize(text);
                      if (fieldErrors.size) {
                        setFieldErrors(prev => ({ ...prev, size: '' }));
                      }
                    }}
                    keyboardType="numeric"
                    containerStyle={styles.sizeInput}
                    leftIcon={<Ruler size={20} color={colors.gray[500]} />}
                    required={true}
                    error={fieldErrors.size}
                  />
                </View>
              </View>

              <View style={styles.unitContainer}>
                {/* <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>{t('entity.garden.sizeUnit')}</Text> */}
                <DropdownPicker
                  label={t('entity.garden.sizeUnit')}
                  options={sizeUnitArray}
                  onSelect={(val) => setSizeUnit(val as any)}
                  selectedValue={sizeUnit}
                  isMultiple={false}
                />
              </View>
            </View>

            <DropdownPicker
              label={t('entity.garden.status')}
              options={statusArray}
              onSelect={(val) => setStatus(val as any)}
              selectedValue={status}
              isMultiple={false}
            />
            <DropdownPicker
              label={t('entity.garden.soilType')}
              options={soilTypeArray}
              onSelect={(val) => setSoilType(val as any)}
              selectedValue={soilType}
              isMultiple={false}
            />

            {/*  */}
            <DropdownPicker
              label={t('entity.garden.irrigationSystem')}
              options={irrigationSystemArray}
              onSelect={(val) => setIrrigationSystem(val as any)}
              selectedValue={irrigationSystem}
              isMultiple={false}
            />
            {/* <Input
              label={t('entity.garden.irrigationSystem')}
              placeholder={t('entity.garden.enterIrrigationDetails')}
              value={irrigationSystem}
              onChangeText={setIrrigationSystem}
              containerStyle={styles.inputContainer}
              leftIcon={<Droplets size={20} color={colors.gray[500]} />}
            /> */}

            <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>{t('entity.garden.location')}</Text>
            <View style={styles.locationSection}>
              <View style={styles.coordinatesContainer}>
                <Input
                  label={t('entity.garden.latitude')}
                  placeholder={t('entity.garden.latitude')}
                  value={latitude}
                  onChangeText={setLatitude}
                  keyboardType="numeric"
                  containerStyle={[styles.coordinateInput, { marginRight: 8 }]}
                />

                <Input
                  label={t('entity.garden.longitude')}
                  placeholder={t('entity.garden.latitude')}
                  value={longitude}
                  onChangeText={setLongitude}
                  keyboardType="numeric"
                  containerStyle={styles.coordinateInput}
                />
              </View>

              <Input
                label={t('entity.garden.address')}
                placeholder={t('entity.garden.locationDescription')}
                value={address}
                onChangeText={setAddress}
                containerStyle={styles.inputContainer}
                leftIcon={<MapPin size={20} color={colors.gray[500]} />}
              />

              <TouchableOpacity
                style={styles.mapButton}
                onPress={() => setShowMapModal(true)}
              >
                <Map size={20} color={colors.white} style={styles.mapButtonIcon} />
                <Text style={styles.mapButtonText}>{t('entity.garden.selectOnMap')}</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('common.cancel')}
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? "Creating..." : t('entity.garden.createGarden')}
              onPress={handleSubmit}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
            />
          </View>
        </ScrollView>

        {/* Map Location Selection Modal */}
        <Modal
          visible={showMapModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowMapModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.mapModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Location</Text>
                <View style={styles.modalHeaderButtons}>
                  <TouchableOpacity
                    style={styles.modalHeaderButton}
                    onPress={handleSelectLocation}
                  >
                    <Text style={styles.modalHeaderButtonText}>Confirm</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalHeaderButton, styles.modalCloseButton]}
                    onPress={() => setShowMapModal(false)}
                  >
                    <Text style={styles.modalCloseText}>Cancel</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.mapContainer}>
                <MapView
                  style={styles.map}
                  initialRegion={initialRegion}
                  onPress={handleMapPress}
                  mapType="hybrid"
                  showsMyLocationButton={true}
                >
                  {selectedLocation ? (
                    <Marker
                      coordinate={{
                        latitude: selectedLocation.latitude,
                        longitude: selectedLocation.longitude,
                      }}
                      pinColor={colors.primary}
                    />
                  ) : (
                    <Marker
                      coordinate={{
                        latitude: initialRegion?.latitude,
                        longitude: initialRegion?.longitude,
                      }}
                      pinColor={colors.primary}
                    />
                  )}
                </MapView>

                <TouchableOpacity
                  style={styles.currentLocationButton}
                  onPress={async () => {
                    try {
                      const location = await Location.getCurrentPositionAsync({});
                      const { latitude, longitude } = location.coords;
                      setInitialRegion(prev => ({
                        ...prev,
                        latitude,
                        longitude,
                        latitudeDelta: 0.009,
                        longitudeDelta: 0.009,
                      }));
                    } catch (error) {
                      console.error('Error getting current location:', error);
                    }
                  }}
                >
                  <MapPin size={24} color={colors.primary} />
                </TouchableOpacity>
              </View>

              <View style={styles.selectedLocationInfo}>
                <Text style={styles.selectedLocationText}>
                  {selectedLocation
                    ? `Selected: ${selectedLocation.latitude.toFixed(6)}, ${selectedLocation.longitude.toFixed(6)}`
                    : 'Tap on the map to select location'}
                </Text>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dropdownText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  dropdownOptions: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    zIndex: 10,
    maxHeight: 200,
  },
  dropdownOption: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  rowContainer: {
    flexDirection: 'row',
    // marginBottom: 2,
  },
  sizeContainer: {
    flex: 2,
    marginRight: 8,
  },
  unitContainer: {
    flex: 1,
  },
  sizeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sizeInput: {
    flex: 1,
  },
  unitDropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
  },
  unitDropdownText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  unitDropdownOptions: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: colors.gray[300],
    zIndex: 10,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  unitDropdownOption: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  unitDropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  locationSection: {
    // marginBottom: 8,
  },
  coordinatesContainer: {
    flexDirection: 'row',
    // marginBottom: 16,
  },
  coordinateInput: {
    flex: 1,
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    marginTop: 8,
  },
  mapButtonIcon: {
    marginRight: 8,
  },
  mapButtonText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  mapModalContent: {
    flex: 1,
    backgroundColor: colors.white,
    marginTop: 50,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalHeaderButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalHeaderButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: colors.primary,
  },
  modalHeaderButtonText: {
    color: colors.white,
    fontWeight: '500',
  },
  modalCloseButton: {
    backgroundColor: colors.gray[200],
  },
  modalCloseText: {
    color: colors.gray[700],
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  currentLocationButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: colors.white,
    padding: 12,
    borderRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectedLocationInfo: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  selectedLocationText: {
    color: colors.gray[700],
    textAlign: 'center',
  },
});








