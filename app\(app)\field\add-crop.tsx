import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import DatePicker from '@/components/DatePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import DropdownPicker from '@/components/DropdownPicker';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { v4 as uuidv4 } from 'uuid';
import { useTranslation } from '@/i18n/useTranslation';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';

// Soil type options
// const SOIL_TYPES = [
//   { label: 'Clay', value: 'clay', icon: <MaterialIcons name="landscape" size={24} color="black" /> },
//   { label: 'Sandy', value: 'sandy', icon: <MaterialIcons name="beach-access" size={24} color="black" /> },
//   { label: 'Loam', value: 'loam', icon: <MaterialIcons name="grass" size={24} color="black" /> },
//   { label: 'Silt', value: 'silt', icon: <MaterialIcons name="waves" size={24} color="black" /> },
//   { label: 'Peat', value: 'peat', icon: <MaterialIcons name="compost" size={24} color="black" /> },
//   { label: 'Chalk', value: 'chalk', icon: <MaterialIcons name="terrain" size={24} color="black" /> },
//   { label: 'Sandy Loam', value: 'sandyLoam', icon: <MaterialIcons name="filter-sand" size={24} color="black" /> },
//   { label: 'Clay Loam', value: 'clayLoam', icon: <MaterialIcons name="landscape" size={24} color="black" /> },
//   { label: 'Silty Clay', value: 'siltyClam', icon: <MaterialIcons name="waves" size={24} color="black" /> },
//   { label: 'Sandy Clay', value: 'sandyClay', icon: <MaterialIcons name="beach-access" size={24} color="black" /> },
//   { label: 'Other', value: 'other', icon: <MaterialIcons name="more-horiz" size={24} color="black" /> },
// ];


export default function AddCropScreen() {
  const { id } = useLocalSearchParams();
  const fieldIdString = Array.isArray(id) ? id[0] : id as string;

  const { t, isRTL } = useTranslation();
  const { getField, addCropToField, fetchFields, getFieldCrops } = useFarmStore();

  const [cropType, setCropType] = useState('');
  const [soilType, setSoilType] = useState('');
  const [plantedDate, setPlantedDate] = useState<Date | null>(new Date());
  const [expectedHarvestDate, setExpectedHarvestDate] = useState<Date | null>(null);
  const [notes, setNotes] = useState('');
  const [imageUri, setImageUri] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    cropType: '',
    plantedDate: '',
    soilType: ''
  });

  // Field validation errors
  // const [fieldErrors, setFieldErrors] = useState({
  //   cropType: '',
  //   plantedDate: ''
  // });
  const [field, setField] = useState<any>(null);
  // const {}
  const SOIL_TYPES = [
  {
    label: t('entity.garden.soilTypeClay'),
    value: 'clay',
    icon: <MaterialCommunityIcons name="terrain" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeSandy'),
    value: 'sandy',
    icon: <MaterialCommunityIcons name="weather-sunny" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeLoam'),
    value: 'loam',
    icon: <MaterialCommunityIcons name="grain" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeSilt'),
    value: 'silt',
    icon: <MaterialCommunityIcons name="waves" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypePeat'),
    value: 'peat',
    icon: <MaterialCommunityIcons name="leaf" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeChalk'),
    value: 'chalk',
    icon: <MaterialCommunityIcons name="white-balance-sunny" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeSandyLoam'),
    value: 'sandyLoam',
    icon: <MaterialCommunityIcons name="grain" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeClayLoam'),
    value: 'clayLoam',
    icon: <MaterialCommunityIcons name="terrain" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeSiltyClay'),
    value: 'siltyClay',
    icon: <MaterialCommunityIcons name="waves" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeSandyClay'),
    value: 'sandyClay',
    icon: <MaterialCommunityIcons name="weather-sunny" size={24} color="black" />,
  },
  {
    label: t('entity.garden.soilTypeOther'),
    value: 'other',
    icon: <MaterialCommunityIcons name="help-circle-outline" size={24} color="black" />,
  },
];
  // Load field data when component mounts
  useEffect(() => {
    if (fieldIdString) {
      const fieldData = getField(fieldIdString);
      setField(fieldData);

      // If field has a soil type, pre-populate it
      if (fieldData?.soilType) {
        setSoilType(fieldData.soilType);
      }
    }
  }, [fieldIdString, getField]);

  const validateFields = () => {
    const errors = {
      cropType: '',
      plantedDate: '',
      soilType: ''
    };

    let hasErrors = false;

    if (!cropType.trim()) {
      errors.cropType = t('field.cropTypeRequired');
      hasErrors = true;
    }

    if (!plantedDate) {
      errors.plantedDate = t('field.plantedDateRequired');
      hasErrors = true;
    }

    if (!soilType) {
      errors.soilType = t('field.soilTypeRequired');
      hasErrors = true;
    }

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleSubmit = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    try {
      setIsLoading(true);

      // Upload image if selected
      let imageUrl = '';
      if (imageUri && !imageUri.startsWith('http')) {
        imageUrl = await uploadImageAsync(imageUri, 'crops');
      }

      const cropData = {
        id: uuidv4(),
        fieldId: fieldIdString,
        cropType,
        status: 'active',
        plantedDate: plantedDate.toISOString(),
        expectedHarvestDate: expectedHarvestDate?.toISOString(),
        notes,
        images: imageUrl ? [imageUrl] : [],
        soilType,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await addCropToField(fieldIdString, cropData);

      // Ensure we refresh the field data to show the new crop
      if (field?.farmId) {
        await fetchFields(field.farmId);
      }

      // Specifically refresh crops for this field
      await getFieldCrops(fieldIdString);

      Toast.show({
        type: 'overlay',
        text1: 'Success',
        text2: 'Crop added successfully.',
        autoHide: true,
        visibilityTime: 3000,
      });

      // Navigate back to the field details page
      // router.push(`/field/${fieldIdString}`);
      router.replace(`/field/${fieldIdString}`);
    } catch (error: any) {
      Toast.show({
        type: 'overlay',
        text1: 'Error',
        text2: 'Error: ' + error.message,
        autoHide: true,
        visibilityTime: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: t('field.add_crop') }} />

      <ScrollView style={styles.scrollView}>
        <Input
          label={t('field.cropType')}
          placeholder={t('field.enterCropType')}
          value={cropType}
          onChangeText={(text) => {
            setCropType(text);
            if (fieldErrors.cropType) {
              setFieldErrors(prev => ({ ...prev, cropType: '' }));
            }
          }}
          required={true}
          error={fieldErrors.cropType}
        />

        {/* <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>{t('field.soilType')}</Text> */}
        <DropdownPicker
          label={t('field.soilType')}
          options={SOIL_TYPES}
          onSelect={(val) => {
            setSoilType(val as string);
            if (fieldErrors.soilType) {
              setFieldErrors(prev => ({ ...prev, soilType: '' }));
            }
          }}
          selectedValue={soilType}
          isMultiple={false}
          required={true}
          error={fieldErrors.soilType}
        />

        <DatePicker
          label={t('field.plantedDate')}
          value={plantedDate}
          onChange={(date) => {
            setPlantedDate(date);
            if (fieldErrors.plantedDate) {
              setFieldErrors(prev => ({ ...prev, plantedDate: '' }));
            }
          }}
          placeholder={t('field.selectDate')}
          required={true}
        />
        {fieldErrors.plantedDate && (
          <Text style={styles.errorText}>{fieldErrors.plantedDate}</Text>
        )}

        <DatePicker
          label={t('field.expectedHarvestDate')}
          value={expectedHarvestDate}
          onChange={setExpectedHarvestDate}
          placeholder={t('field.selectDate')}
          startOffset={0}
          endOffset={24}
        />

        <Input
          label={t('field.notes')}
          placeholder={t('field.enterNotes')}
          value={notes}
          onChangeText={setNotes}
          multiline
          numberOfLines={4}
        />

        <ImagePicker
          label={t('field.cropImage')}
          onImageSelected={setImageUri}
          imageUri={imageUri}
        />

        <Button
          title={t('common.save')}
          onPress={handleSubmit}
          loading={isLoading}
          style={styles.button}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    padding: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: colors.text,
  },
  button: {
    marginTop: 24,
    marginBottom: 40,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
  },
});
