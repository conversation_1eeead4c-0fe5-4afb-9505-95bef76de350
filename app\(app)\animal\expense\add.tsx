import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Text } from 'react-native';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
// import { Text, TextInput } from '@/components/Themed';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { useFarmStore } from '@/store/farm-store';
// import { useTranslation } from '@/i18n';
import DatePicker from '@/components/DatePicker';
// import { Picker } from '@react-native-picker/picker';
import { useAuthStore } from '@/store/auth-store';
import Toast from 'react-native-toast-message';
import { useTranslation } from '@/i18n/useTranslation';
// import { isRTL } from '@/i18n';

export default function AddAnimalExpenseScreen() {
  const { t ,isRTL} = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { addAnimalCost, getAnimal } = useFarmStore();
  const { language } = useAuthStore();

  const [costType, setCostType] = useState('feed');
  const [amount, setAmount] = useState('');
  const [date, setDate] = useState(new Date());
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const animal = getAnimal(id);

  const costTypes = [
    { label: t('animal.costTypes.feed'), value: 'feed' },
    { label: t('animal.costTypes.medicine'), value: 'medicine' },
    { label: t('animal.costTypes.vaccination'), value: 'vaccination' },
    { label: t('animal.costTypes.veterinary'), value: 'veterinary' },
    { label: t('animal.costTypes.equipment'), value: 'equipment' },
    { label: t('animal.costTypes.other'), value: 'other' },
  ];

  const handleSubmit = async () => {
    if (!costType) {
      Alert.alert(t('error'), t('animal.expense.errorNoCostType'));
      return;
    }

    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      Alert.alert(t('error'), t('animal.expense.errorInvalidAmount'));
      return;
    }

    try {
      setIsSubmitting(true);

      await addAnimalCost(id, {
        cost_type: costType,
        amount: Number(amount),
        date: date.toISOString(),
        notes: notes.trim() || undefined,
      });

      Toast.show({
        type: 'success',
        text1: t('success'),
        text2: t('animal.expense.addSuccess'),
      });

      router.back();
    } catch (error) {
      console.error('Error adding animal expense:', error);
      Alert.alert(t('error'), t('animal.expense.addError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Stack.Screen options={{ title: t('entity.animal.addTitle') }} />

      <View style={[styles.header,isRTL && {flexDirection:'row-reverse'}]}>
        <Text style={styles.title}>{t('entity.animal.addExpenseFor', { name: animal?.name || id })}</Text>
      </View>

      <View style={[styles.form]}>
        <Text style={[styles.label,isRTL && {textAlign:'right',marginRight:8}]}>{t('entity.animal.costType')}</Text>
        <View style={styles.pickerContainer}>
          {/* <Picker
            selectedValue={costType}
            onValueChange={(value) => setCostType(value)}
            style={styles.picker}
          >
            {costTypes.map((type) => (
              <Picker.Item key={type.value} label={type.label} value={type.value} />
            ))}
          </Picker> */}
        </View>

        <Text style={[styles.label,isRTL && {textAlign:'right',marginRight:8}]}>{t('entity.animal.expenseAmount')}</Text>
        <Input
          style={styles.input}
          value={amount}
          onChangeText={setAmount}
          placeholder={t('entity.animal.enterExpenseAmount')}
          keyboardType="numeric"
        />

        <Text style={[styles.label,isRTL && {textAlign:'right',marginRight:8}]}>{t('entity.animal.expenseDate')}</Text>
        <DatePicker
          // 
          placeholder={t('entity.animal.selectExpenseDate')}
          date={date}
          onDateChange={setDate}
          maximumDate={new Date()}
        />

        <Text style={[styles.label,isRTL && {textAlign:'right',marginRight:8}]}>{t('entity.animal.expenseNotes')}</Text>
        <Input
          style={[styles.input, styles.textArea]}
          value={notes}
          onChangeText={setNotes}
          placeholder={t('entity.animal.enterExpenseNotes')}
          multiline
          numberOfLines={4}
        />

        <Button
          title={isSubmitting ? t('common.submitting') : t('common.save')}
          onPress={handleSubmit}
          disabled={isSubmitting}
          style={styles.button}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  form: {
    gap: 12,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
  },
  input: {
    width: '100%',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
  },
  picker: {
    width: '100%',
  },
  button: {
    marginTop: 16,
  },
});