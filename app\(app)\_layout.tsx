import React from 'react';
import { Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';

export default function AppLayout() {
  const { t } = useTranslation();

  return (
    <Stack

      screenOptions={{
        headerStyle: {
          backgroundColor: colors.white,
          direction: 'ltr',
        },
        headerTintColor: colors.gray[800],
        headerTitleStyle: {
          fontWeight: '600',
          headerTitleAlign: 'left',
          // Don't change text alignment for headers
        },
        headerShadowVisible: false,
        contentStyle: {
          backgroundColor: colors.gray[50],
        },
        // Don't apply RTL to header buttons
      }}
      options={{
        headerTitleAlign: 'left',
        headerStyle: { direction: 'ltr' },
        headerTitleStyle: { writingDirection: 'ltr' },
      }}
    >
      <Stack.Screen
        name="(tabs)"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="plant/[id]"
        options={{
          title: t('entity.plant.details', 'Plant Details'),
        }}
      />
      <Stack.Screen
        name="plant/create"
        options={{
          title: t('entity.plant.add', 'Add Plant'),
        }}
      />
      <Stack.Screen
        name="plant/edit/[id]"
        options={{
          title: t('entity.plant.edit', 'Edit Plant'),
        }}
      />
      <Stack.Screen
        name="animal/[id]"
        options={{
          title: t('entity.animal.details', 'Animal Details'),
        }}
      />
      <Stack.Screen
        name="animal/create"
        options={{
          title: t('entity.animal.add', 'Add Animal'),
        }}
      />
      <Stack.Screen
        name="field/[id]"
        options={{
          title: t('entity.field.details', 'Field Details'),
        }}
      />
      <Stack.Screen
        name="field/create"
        options={{
          title: t('entity.field.add', 'Add Field'),
        }}
      />
      <Stack.Screen
        name="garden/[id]"
        options={{
          title: t('entity.garden.details', 'Garden Details'),
        }}
      />
      <Stack.Screen
        name="garden/create"
        options={{
          title: t('entity.garden.add', 'Add Garden'),
        }}
      />
      <Stack.Screen
        name="equipment/[id]"
        options={{
          title: t('entity.equipment.details', 'Equipment Details'),
        }}
      />
      <Stack.Screen
        name="equipment/create"
        options={{
          title: t('entity.equipment.add', 'Add Equipment'),
        }}
      />
      <Stack.Screen
        name="yield/[id]"
        options={{
          title: t('entity.yield.details', 'Yield Details'),
        }}
      />
      <Stack.Screen
        name="yield/create"
        options={{
          title: t('entity.yield.add', 'Add Yield'),
        }}
      />
      <Stack.Screen
        name="task/[id]"
        options={{
          title: t('task.details', 'Task Details'),
        }}
      />
      <Stack.Screen
        name="task/create"
        options={{
          title: t('task.add', 'Add Task'),
        }}
      />
      <Stack.Screen
        name="user/invite"
        options={{
          title: t('user.invite', 'Invite User'),
        }}
      />
      <Stack.Screen
        name="user/list"
        options={{
          title: t('user.list', 'Users'),
        }}
      />
      <Stack.Screen
        name="scanner"
        options={{
          title: t('scanner.title', 'QR Scanner'),
          animation: 'slide_from_bottom',
          presentation: 'modal',
        }}
      />
    </Stack>
  );
}
