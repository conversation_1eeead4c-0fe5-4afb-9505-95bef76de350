import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Image,
  SafeAreaView,
} from 'react-native';
import { router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import {
  FileText,
  Plus,
  BarChart3,
  Calendar,
  Clock,
  Users,
  Warehouse,
  ChevronRight,
  Download,
  Share2,
  Search,
} from 'lucide-react-native';
import { collection, getDocs } from 'firebase/firestore';
import { firestore } from '@/firebase/config'; // Assuming your firestore instance is exported from here
import Input from '@/components/Input';

// Report templates
const reportTemplates = [
  {
    id: 'daily-operations',
    title: 'Daily Operations',
    description: 'Summary of daily farm activities and tasks',
    icon: <Clock size={24} color={colors.primary} />,
    color: colors.primary,
  },
  {
    id: 'weekly-summary',
    title: 'Weekly Summary',
    description: 'Overview of weekly progress and metrics',
    icon: <Calendar size={24} color={colors.success} />,
    color: colors.success,
  },
  {
    id: 'inventory-status',
    title: 'Inventory Status',
    description: 'Current inventory levels and status',
    icon: <Warehouse size={24} color={colors.warning} />,
    color: colors.warning,
  },
  {
    id: 'staff-performance',
    title: 'Staff Performance',
    description: 'Staff productivity and task completion metrics',
    icon: <Users size={24} color={colors.info} />,
    color: colors.info,
  },
];

export default function ReportsScreen() {
  const [reports, setReports] = useState<any[]>([]); // State to hold dynamic reports
  const [loadingReports, setLoadingReports] = useState(true); // State for loading indicator
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch reports from Firestore on component mount
  useEffect(() => {
    const fetchReports = async () => {
      setLoadingReports(true);
      try {
        const reportsCollectionRef = collection(firestore, 'reports');
        const querySnapshot = await getDocs(reportsCollectionRef);
        const fetchedReports = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          // Convert Firestore Timestamp to a readable date string if it exists
          date: doc.data().date?.toDate ? doc.data().date.toDate().toISOString().split('T')[0] : doc.data().date,
        }));
        setReports(fetchedReports);
      } catch (error) {
        console.error('Error fetching reports:', error);
      } finally {
        setLoadingReports(false);
      }
    };
    fetchReports();
  }, []);

  const filteredReports = searchQuery
    ? reports.filter(report =>
      report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.createdBy.toLowerCase().includes(searchQuery.toLowerCase())
    )
    : reports; // Use the dynamically fetched reports
  // console.log(filteredReports)
  const handleReportPress = (report: any) => {
    // Navigate to the new report detail screen with the report ID
    // router.push(`/reports/${report.id}`);
    if (report?.type === "inventory") {
      // console.log({ report })
      router.push(`/report-templates/inventory-status?id=${report.id}`);
    }
    else
      console.log({ report })
    // 
  };

  const handleTemplatePress = (template: any) => {
    // console.log({ template })
    router.push(`/report-templates/${template.id}`);
  };

  const handleCreateReport = () => {
    router.push('/create-report');
  };

  const getReportIcon = (type: string) => {
    switch (type) {
      case 'operations':
        return <Clock size={20} color={colors.white} />;
      case 'summary':
        return <BarChart3 size={20} color={colors.white} />;
      case 'inventory':
        return <Warehouse size={20} color={colors.white} />;
      case 'staff':
        return <Users size={20} color={colors.white} />;
      default:
        return <FileText size={20} color={colors.white} />;
    }
  };

  const getReportColor = (type: string) => {
    switch (type) {
      case 'operations':
        return colors.primary;
      case 'summary':
        return colors.success;
      case 'inventory':
        return colors.warning;
      case 'staff':
        return colors.info;
      default:
        return colors.gray[600];
    }
  };

  const renderReportItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.reportItem}
      onPress={() => handleReportPress(item)}
    >
      <View style={[
        styles.reportIcon,
        { backgroundColor: getReportColor(item.type) }
      ]}>
        {getReportIcon(item.type)}
      </View>
      <View style={styles.reportContent}>
        <Text style={styles.reportTitle}>{item?.title}</Text>
        <Text style={styles.reportMeta}>
          {new Date(item.date).toLocaleDateString()} • {item.createdBy}
        </Text>
      </View>
      <View style={styles.reportActions}>
        <TouchableOpacity style={styles.reportAction}>
          <Download size={18} color={colors.gray[500]} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.reportAction}>
          <Share2 size={18} color={colors.gray[500]} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderTemplateItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[styles.templateItem, { borderLeftColor: item.color }]}
      onPress={() => handleTemplatePress(item)}
    >
      <View style={[styles.templateIcon, { backgroundColor: item.color + '20' }]}>
        {item.icon}
      </View>
      <View style={styles.templateContent}>
        <Text style={styles.templateTitle}>{item.title}</Text>
        <Text style={styles.templateDescription}>{item.description}</Text>
      </View>
      <ChevronRight size={20} color={colors.gray[400]} />
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Reports',
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Input
              placeholder="Search reports..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              containerStyle={styles.searchContainer}
              leftIcon={<Search size={20} color={colors.gray[500]} />}
            />

            <TouchableOpacity
              style={styles.createButton}
              onPress={handleCreateReport}
            >
              <Plus size={20} color={colors.white} style={styles.createButtonIcon} />
              <Text style={styles.createButtonText}>Create Report</Text>
            </TouchableOpacity>
          </View>

          {/* <View style={styles.section}>
            <Text style={styles.sectionTitle}>Report Templates</Text>
            <FlatList
              data={reportTemplates}
              renderItem={renderTemplateItem}
              keyExtractor={item => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.templatesList}
            />
          </View> */}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Reports</Text>
            {loadingReports ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Loading reports...</Text>
              </View>
            ) : filteredReports.length > 0 ? (
              <View style={styles.reportsList}>
                {filteredReports.map(report => (
                  <View key={report.id}>
                    {renderReportItem({ item: report })}
                  </View>
                ))}
                {/* For better performance with long lists, consider replacing the .map with a FlatList */}
              </View>
            ) : (
              <View style={styles.emptyState}>
                <FileText size={48} color={colors.gray[300]} />
                <Text style={styles.emptyStateTitle}>No reports found</Text>
                <Text style={styles.emptyStateText}>
                  {searchQuery
                    ? "No reports match your search criteria"
                    : "You haven't created any reports yet"}
                </Text>
              </View>
            )}
          </View>

          {/* <View style={styles.section}>
            <Text style={styles.sectionTitle}>Analytics</Text>
            <View style={styles.analyticsCard}>
              <View style={styles.analyticsHeader}>
                <Text style={styles.analyticsTitle}>Report Generation</Text>
                <Text style={styles.analyticsPeriod}>Last 30 days</Text>
              </View>

              <View style={styles.analyticsContent}>
                <Image
                  source={{ uri: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8Y2hhcnR8ZW58MHx8MHx8&w=1000&q=80' }}
                  style={styles.analyticsChart}
                  resizeMode="cover"
                />

                <View style={styles.analyticsStats}>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>12</Text>
                    <Text style={styles.statLabel}>Reports Generated</Text>
                  </View>

                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>4</Text>
                    <Text style={styles.statLabel}>Templates Used</Text>
                  </View>

                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>8</Text>
                    <Text style={styles.statLabel}>Downloads</Text>
                  </View>
                </View>
              </View>
            </View>
          </View> */}
        </ScrollView>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  searchContainer: {
    marginBottom: 16,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
  },
  createButtonIcon: {
    marginRight: 8,
  },
  createButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  templatesList: {
    paddingRight: 16,
  },
  templateItem: {
    width: 200,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    borderLeftWidth: 4,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  templateIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  templateContent: {
    flex: 1,
    marginBottom: 12,
  },
  templateTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  templateDescription: {
    fontSize: 12,
    color: colors.gray[600],
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.gray[600],
  },
  reportsList: {
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  reportItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  reportIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  reportContent: {
    flex: 1,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 4,
  },
  reportMeta: {
    fontSize: 12,
    color: colors.gray[500],
  },
  reportActions: {
    flexDirection: 'row',
  },
  reportAction: {
    padding: 8,
    marginLeft: 4,
  },
  emptyState: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
  },
  analyticsCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  analyticsHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  analyticsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  analyticsPeriod: {
    fontSize: 12,
    color: colors.gray[500],
  },
  analyticsContent: {
    padding: 16,
  },
  analyticsChart: {
    height: 180,
    borderRadius: 8,
    marginBottom: 16,
  },
  analyticsStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
  },
});