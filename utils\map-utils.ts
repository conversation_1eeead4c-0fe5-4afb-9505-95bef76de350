export function calculatePolygonArea(coordinates: Array<{latitude: number; longitude: number}>) {
  // if (coordinates.length < 3) return 0;

  // const R = 6371000; // Earth's radius in meters

  // // Convert coordinates to radians
  // const coords = coordinates.map(point => ({
  //   lat: (point.latitude * Math.PI) / 180,
  //   lng: (point.longitude * Math.PI) / 180
  // }));

  // let area = 0;
  
  // // Use the Spherical Excess formula to calculate area
  // for (let i = 0; i < coords.length - 1; i++) {
  //   const p1 = coords[i];
  //   const p2 = coords[i + 1];
  //   area += (p2.lng - p1.lng) * (2 + Math.sin(p1.lat) + Math.sin(p2.lat));
  // }

  // area = area * R * R / 2;
  // return Math.abs(area);
   if (coordinates.length < 3) return 0;

  const R = 6371000; // Earth's radius in meters
  const SQM_TO_ACRES = 0.000247105; // 1 square meter = 0.000247105 acres

  // Convert coordinates to radians
  const coords = coordinates.map(point => ({
    lat: (point.latitude * Math.PI) / 180,
    lng: (point.longitude * Math.PI) / 180
  }));

  let area = 0;

  // Use the Spherical Excess formula to calculate area
  for (let i = 0; i < coords.length - 1; i++) {
    const p1 = coords[i];
    const p2 = coords[i + 1];
    area += (p2.lng - p1.lng) * (2 + Math.sin(p1.lat) + Math.sin(p2.lat));
  }

  area = Math.abs(area * R * R / 2); // Area in square meters
  const areaInAcres = area * SQM_TO_ACRES;

  return areaInAcres;
}