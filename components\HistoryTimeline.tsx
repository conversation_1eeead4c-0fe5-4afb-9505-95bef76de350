import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  Image,
  Modal
} from 'react-native';
import { colors } from '@/constants/colors';
import { HistoryEntry } from '@/types';
import { 
  CheckCircle, 
  ClipboardCheck, 
  AlertCircle, 
  FileText, 
  Image as ImageIcon,
  X
} from 'lucide-react-native';

interface HistoryTimelineProps {
  items: Array<{
    id: string;
    title: string;
    description: string;
    date: Date;
    type: string;
  }>;
  onImagePress?: (imageUrl: string) => void;
}

const HistoryTimeline: React.FC<HistoryTimelineProps> = ({ 
  items = [],
  onImagePress
}) => {
  const safeItems = items || [];
  const [selectedImage, setSelectedImage] = React.useState<string | null>(null);

  const getIconForType = (type: string) => {
    switch (type) {
      case 'task_completed':
        return <CheckCircle size={20} color={colors.success} />;
      case 'checklist_completed':
        return <ClipboardCheck size={20} color={colors.primary} />;
      case 'status_change':
        return <AlertCircle size={20} color={colors.warning} />;
      case 'note_added':
        return <FileText size={20} color={colors.info} />;
      case 'image_added':
        return <ImageIcon size={20} color={colors.secondary} />;
      default:
        return <CheckCircle size={20} color={colors.gray[500]} />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleImagePress = (imageUrl: string) => {
    if (onImagePress) {
      onImagePress(imageUrl);
    } else {
      setSelectedImage(imageUrl);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView>
        {safeItems.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No history entries yet</Text>
          </View>
        ) : (
          safeItems
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .map((entry) => (
              <View key={entry.id} style={styles.entryContainer}>
                <View style={styles.timelineConnector}>
                  <View style={styles.timelineDot} />
                  <View style={styles.timelineLine} />
                </View>
                
                <View style={styles.entryContent}>
                  <View style={styles.entryHeader}>
                    <View style={styles.entryIcon}>
                      {getIconForType(entry.type)}
                    </View>
                    <View style={styles.entryInfo}>
                      <Text style={styles.entryTitle}>{entry.title}</Text>
                      <Text style={styles.entryDate}>{formatDate(entry.date)}</Text>
                    </View>
                  </View>
                  
                  {entry.description && (
                    <Text style={styles.entryDescription}>{entry.description}</Text>
                  )}
                  
                  {entry.images && entry.images.length > 0 && (
                    <View style={styles.imagesContainer}>
                      {entry.images.map((imageUrl, index) => (
                        <TouchableOpacity 
                          key={index}
                          onPress={() => handleImagePress(imageUrl)}
                        >
                          <Image 
                            source={{ uri: imageUrl }} 
                            style={styles.entryImage}
                          />
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                  
                  <View style={styles.entryFooter}>
                    <Text style={styles.entryUser}>By {entry.userName}</Text>
                  </View>
                </View>
              </View>
            ))
        )}
      </ScrollView>

      <Modal
        visible={!!selectedImage}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setSelectedImage(null)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={() => setSelectedImage(null)}
          >
            <X size={24} color={colors.white} />
          </TouchableOpacity>
          
          {selectedImage && (
            <Image 
              source={{ uri: selectedImage }} 
              style={styles.fullImage}
              resizeMode="contain"
            />
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
  },
  entryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  timelineConnector: {
    width: 24,
    alignItems: 'center',
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.primary,
    marginTop: 6,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: colors.gray[300],
    marginTop: 4,
  },
  entryContent: {
    flex: 1,
    marginLeft: 12,
    paddingBottom: 16,
  },
  entryHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  entryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  entryInfo: {
    flex: 1,
  },
  entryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 2,
  },
  entryDate: {
    fontSize: 12,
    color: colors.gray[500],
  },
  entryDescription: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 12,
    marginLeft: 40,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginLeft: 40,
    marginBottom: 12,
  },
  entryImage: {
    width: 80,
    height: 80,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  entryFooter: {
    marginLeft: 40,
  },
  entryUser: {
    fontSize: 12,
    color: colors.gray[600],
    fontStyle: 'italic',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
    padding: 8,
  },
  fullImage: {
    width: '100%',
    height: '80%',
  },
});

export default HistoryTimeline;
